import { Injectable } from '@nestjs/common';
import { InjectEntityManager } from '@nestjs/typeorm';
import { EntityManager } from 'typeorm';

import { DB_PRE } from '../constants';
import { resOk, resErr, timeFmt, insertValues } from '../utils';
import { CustomHeadersDto } from '../dto';
import { CheckinTestSubmitDto, ActivityDetailQueryDto, UserInfoQueryDto, CheckinLogInfoDto, CheckinSubmitDto, CheckinDaysInfoDto } from '../dto/Checkin';

@Injectable()
export class CheckinService {
  constructor(@InjectEntityManager() private readonly entityManager: EntityManager) {}

  /** 打卡活动-信息列表 */
  async activityInfo(headers: CustomHeadersDto): Promise<ResObj> {
    const { login_jobnum, factoryid } = headers;

    try {
      // 查询正在进行中的活动
      const activeActivity = await this.entityManager.query(`
        select c.id as period_id, code_name, activity_desc, start_time, end_time, days
        from ${DB_PRE}checkin_period_conf c, ${DB_PRE}user u
        where c.factory_id=${factoryid}
        and u.factory_id=c.factory_id
        and u.jobnum='${login_jobnum}'
        and c.user_type like concat('%[', u.user_type ,']%')
        and c.state=1
        and c.start_time<=now()
        and c.end_time>now()
        order by c.id desc
      `);

      // 查询今日情绪打卡记录
      const emotionLog = await this.entityManager.query(`
        select count(1) as emotion_log
        from ${DB_PRE}checkin_emotion_log
        where factory_id=${factoryid}
        and jobnum='${login_jobnum}'
        and create_time>=STR_TO_DATE('${timeFmt(undefined, 'Y-M-D 00:00:00')}', '%Y-%m-%d %H:%i:%S')
      `);

      // 查询最近结束的活动
      const recentActivity = await this.entityManager.query(`
        select c.id as period_id, code_name, activity_desc, start_time, end_time, c.days, max(l.create_time) as last_checkin_time
        from ${DB_PRE}checkin_period_conf c, ${DB_PRE}user u, ${DB_PRE}checkin_log l
        where l.jobnum=u.jobnum
        and l.period_id=c.id
        and c.factory_id=${factoryid}
        and u.factory_id=c.factory_id
        and u.jobnum='${login_jobnum}'
        and c.user_type like concat('%[', u.user_type ,']%')
        and c.state=1
        and c.end_time<=now()
        GROUP BY l.period_id
        order by c.end_time desc
      `);

      const list = [];
      [...activeActivity, ...recentActivity].forEach(info => {
        info.start_time = info.start_time.getTime();
        info.end_time = info.end_time.getTime();
        info.last_checkin_time = info.last_checkin_time?.getTime() || 0;
        info.emotion_log = emotionLog[0].emotion_log;

        if (info.end_time > Date.now()) {
          // 活动进行中
          list.push(info);
        } else {
          // 活动已结束
          // 仅返回用户参与过的，只保留：持续时间 + 30天
          if (info.last_checkin_time + info.days * 24 * 3600 * 1000 > Date.now() - 30 * 24 * 3600 * 1000) {
            delete info.last_checkin_time;
            list.push(info);
          }
        }
      });

      return resOk(list);
    } catch (error) {
      return resErr(500, error.message);
    }
  }

  /** 打卡活动-详情 */
  async activityDetail(query: ActivityDetailQueryDto, headers: CustomHeadersDto): Promise<ResObj> {
    const { period_id } = query;
    const { login_jobnum, factoryid, is_checkin_preview } = headers;

    try {
      // 查询正在进行中的活动
      let sql = `select c.activity_desc, c.start_time, c.end_time, c.days, l.create_time
        from (${DB_PRE}checkin_period_conf c, ${DB_PRE}user u)
        left join ${DB_PRE}checkin_log l on l.jobnum=u.jobnum and l.period_id=c.id
        where c.factory_id=${factoryid}
        and u.jobnum='${login_jobnum}'
        and c.id=${period_id}
        and u.factory_id=c.factory_id
        and c.user_type like concat('%[', u.user_type ,']%')
        and c.state=1
        and c.start_time<=now()
        order by l.id asc
        limit 0,1`;
      
      // 预览模式
      if (is_checkin_preview) {
        sql = `select c.activity_desc, c.start_time, c.end_time, c.days, l.create_time
        from ${DB_PRE}checkin_period_conf c
        left join ${DB_PRE}checkin_log l on l.jobnum='${login_jobnum}' and l.period_id=c.id 
        where c.factory_id=${factoryid}
        and c.id=${period_id}
        and c.state=1
        order by l.id asc
        limit 0,1`;
      }

      const activityDetail = await this.entityManager.query(sql);
      if (!activityDetail?.length) {
        return resErr(202, '活动不存在');
      }

      if (activityDetail[0].create_time) {
        // 打过卡
        const startTime = new Date(timeFmt(activityDetail[0].create_time.getTime(), 'Y-M-D 00:00:00')).getTime();
        const endTime = startTime + activityDetail[0].days * 24 * 3600 * 1000;
        activityDetail[0].start_time = startTime;
        activityDetail[0].end_time = endTime;
      } else {
        // 没打过卡
        activityDetail[0].start_time = activityDetail[0].start_time.getTime();
        activityDetail[0].end_time = activityDetail[0].end_time.getTime();
      }
      delete activityDetail[0].create_time;

      return resOk(activityDetail[0]);
    } catch (error) {
      return resErr(500, error.message);
    }
  }

  /** 打卡活动-简单详情 */
  async simpleDetail(query: ActivityDetailQueryDto): Promise<ResObj> {
    const { period_id } = query;

    try {
      // 查询正在进行中的活动
      const activityDetail = await this.entityManager.query(`
        select code_name, activity_desc
        from ${DB_PRE}checkin_period_conf
        where id=${period_id}
        and state=1
      `);

      if (!activityDetail?.length) {
        return resErr(202, '活动不存在');
      }

      return resOk(activityDetail[0]);
    } catch (error) {
      return resErr(500, error.message);
    }
  }

  /** 打卡活动-提交测试问卷 */
  async testSubmit(body: CheckinTestSubmitDto, headers: CustomHeadersDto): Promise<ResObj> {
    const { period_id, test_id, answer_list } = body;
    const { login_jobnum, factoryid } = headers;

    try {
      // 查询测试问卷配置
      const testInfo = await this.entityManager.query(
        `select * from ${DB_PRE}checkin_test_conf
        where factory_id=? and id=? and period_id=? and state=1`,
        [
          factoryid,
          test_id,
          period_id
        ]
      );

      if (!testInfo?.length) {
        return resErr(202, '问卷不存在');
      }

      // 检查是否已提交过
      const submittedCount = await this.entityManager.query(
        `select count(1) as count
        from ${DB_PRE}checkin_test_log
        where factory_id=? and jobnum=? and test_id=? and period_id=?`,
        [
          factoryid,
          login_jobnum,
          test_id,
          period_id
        ]
      );

      if (submittedCount[0].count > 0) {
        return resErr(203, '请勿重复提交');
      }

      // 获取题目列表
      const topicList = await this.entityManager.query(
        `select title, type, dimension, is_scored
        from ${DB_PRE}checkin_test_topic
        where test_id=?
        order by sort_num asc`,
        [test_id]
      );

      if (topicList.length !== answer_list.length) {
        return resErr(204, '题目数量不对');
      }

      // 准备插入数据
      const dimension: Record<string, number[]> = {}; // 维度得分
      const rows: any[][] = [];
      const columns = [
        'factory_id',
        'jobnum',
        'period_id',
        'test_id',
        'topic_id',
        'dimension',
        'answer',
        'score',
        'is_scored',
        'create_time',
        'test_title',
        'topic_title',
        'topic_type',
        'test_day_num',
        'test_type'
      ];

      topicList.forEach((topic: any, index: number) => {
        const answer = answer_list[index];

        // 如果参与计分
        if (topic.is_scored) {
          if (!dimension[topic.dimension]) {
            dimension[topic.dimension] = [];
          }
          dimension[topic.dimension].push(answer.score);
        }

        // 添加对应的值到参数数组
        rows.push([
          factoryid,
          login_jobnum,
          period_id,
          test_id,
          answer.topic_id,
          topic.dimension || '',
          answer.value || '',
          answer.score !== undefined ? answer.score : null,
          topic.is_scored,
          new Date(), // create_time
          testInfo[0].title,
          topic.title,
          topic.type,
          testInfo[0].day_num,
          testInfo[0].type
        ]);
      });

      // 使用 insertValues 方法执行批量插入
      await insertValues(
        this.entityManager,
        'checkin_test_log',
        columns,
        rows
      );

      // 计算各维度得分
      const dimension_list = [];
      const entries = Object.entries(dimension);

      entries.forEach(([label, scores]) => {
        // 使用 Function 构造函数代替 eval，更安全且避免 eslint 警告
        // 创建一个函数，接收 scores 参数并执行计算公式
        const calculateScore = new Function(
          'scores',
          `return ${testInfo[0].score_formula?.replace(/\{list\}/g, 'scores')}`
        );

        const obj = {
          label,
          value: calculateScore(scores)
        };
        dimension_list.push(obj);
      });

      // 获取活动代号
      const periodInfo = await this.entityManager.query(
        `select code_name from ${DB_PRE}checkin_period_conf where id=?`,
        [period_id]
      );

      // 返回结果
      return resOk({
        cert_src: testInfo[0].cert_src, // 证书图片地址（为空不发证书）
        dimension_list, // 维度得分
        code_name: periodInfo[0]?.code_name,
        // 维度解析
        analysis: {
          recap_json: testInfo[0].recap_json,
          score_json: testInfo[0].score_json,
          intro_json: testInfo[0].intro_json,
          suggest_json: testInfo[0].suggest_json,
          score_tips: testInfo[0].score_tips,
          recap_prev: testInfo[0].recap_prev,
          score_split: testInfo[0].score_split,
        }
      });
    } catch (error) {
      return resErr(205, error.message);
    }
  }

  /** 打卡活动-用户活动信息 */
  async userInfo(query: UserInfoQueryDto, headers: CustomHeadersDto): Promise<ResObj> {
    const { period_id } = query;
    const { login_jobnum, factoryid, is_checkin_preview } = headers;

    if (!factoryid || !login_jobnum || !period_id) {
      return resErr(201, '缺少参数');
    }

    const now = Date.now();
    try {
      // 查询活动信息
      const activityInfo = await this.entityManager.query(`
        select id, days, start_time, end_time
        from ${DB_PRE}checkin_period_conf
        where factory_id=${factoryid}
        and state=1
        and id=${period_id}
      `);

      if (!activityInfo[0]?.id) {
        return resErr(101, '活动不存在');
      }

      if (!is_checkin_preview && activityInfo[0].start_time > now) {
        return resErr(102, '活动未开始');
      }

      // 查询用户打卡记录
      const logInfo = await this.entityManager.query(`
        select l.days_sum, l.chapter_id, c.days_sum as chapter_days_sum, l.create_time
        from ${DB_PRE}checkin_log l, ${DB_PRE}checkin_chapter c
        where l.factory_id=${factoryid}
        and l.period_id=${period_id}
        and l.jobnum='${login_jobnum}'
        and l.chapter_id=c.id
        order by l.id asc
      `);

      // 查询用户喜爱度评价
      // const favoriteInfo = await this.entityManager.query(`
      //   select checkin_days_sum
      //   from ${DB_PRE}checkin_task_favorite
      //   where factory_id=${factoryid}
      //   and jobnum='${login_jobnum}'
      //   and period_id=${period_id}
      //   order by id desc limit 0,1
      // `);

      // 查询测试问卷信息
      const testList = await this.entityManager.query(`
        select c.id, c.day_num, c.type, c.title, c.tooltips,
        (select count(1) from ${DB_PRE}checkin_test_log where test_id=c.id and jobnum='${login_jobnum}' and period_id=${period_id}) as complete,
        c.cert_src
        from ${DB_PRE}checkin_test_conf c
        where c.factory_id=${factoryid}
        and c.state=1
        and c.period_id=${period_id}
        order by c.day_num asc
      `);

      // 查询章节信息
      const chapterList = await this.entityManager.query(`
        select task_list, days_sum, checkin_type, days_type, id
        from ${DB_PRE}checkin_chapter
        where factory_id=${factoryid}
        and period_id=${period_id}
        and state=1
        order by sort_num asc
      `);

      // 查询天数配置信息
      const daysList = await this.entityManager.query(`
        select *
        from ${DB_PRE}checkin_days_conf
        where factory_id=${factoryid}
        and period_id=${period_id}
        order by chapter_id asc, sort_num asc
      `);

      // 查询是否获得证书
      const certInfo = await this.entityManager.query(`
        select count(1) as count
        from ${DB_PRE}cert
        where jobnum='${login_jobnum}'
        and factory_id='${factoryid}'
        and type=1
        and checkin_period_id=${period_id}
      `);

      const days_total = activityInfo[0].days;
      const lastLogInfo = logInfo[logInfo.length - 1] || {};
      const gotCert = certInfo[0].count > 0;

      // 设置默认值
      if (lastLogInfo.days_sum === undefined) {
        lastLogInfo.days_sum = 0;
      }

      let task_id = null; // 任务ID，首页点击后直接跳去打卡
      let days_conf_id = null; // 天数配置ID，首页点击后直接跳去打卡
      let daysSum = 0;
      let days_type = 0;

      // 计算当前应该打卡的任务
      for (let index = 0; index < chapterList.length; index++) {
        const chapter = chapterList[index];

        daysSum += chapter.days_sum;
        if (lastLogInfo.days_sum < daysSum) {
          const idx = lastLogInfo.days_sum - daysSum + chapter.days_sum;

          if (chapter.days_type === 1) {
            // 分别设置
            const daysConfigs = daysList.filter((o: any) => o.chapter_id === chapter.id);
            days_conf_id = daysConfigs[idx]?.id;
          } else {
            // 统一设置
            const taskIds = chapter.task_list?.split(',') || [];
            task_id = taskIds[idx] || null;
          }
          days_type = chapter.days_type;
          break;
        }
      }

      // 构建返回数据
      const data = {
        days_sum: lastLogInfo.days_sum, // 累计打卡天数
        days_total,
        task_id,
        days_conf_id,
        days_type,
        task_evaluate: null, // 是否要作出最喜欢的任务评价
        test_info: null, // 是否需要先进行测试
        getcert: null,
      };

      // 是否有需要完成的测试
      const testInfo = testList.find((o: any) => {
        let num = 0;
        if (o.type === 0) {
          // 前测
          num = 1;
        }
        return lastLogInfo.days_sum + num >= o.day_num && !Number(o.complete);
      });

      // 活动已结束，只返回后测题
      const endTime = logInfo.length
        ? new Date(timeFmt(logInfo[0]?.create_time.getTime(), 'Y-m-d 00:00:00')).getTime() + activityInfo[0].days * 24 * 3600 * 1000
        : activityInfo[0].end_time.getTime();

      if (testInfo && (Date.now() < endTime || testInfo.type === 1)) {
        data.test_info = {
          test_id: testInfo.id,
          title: testInfo.title,
          tooltips: testInfo.tooltips,
        };
      }

      // 是否有需要发证书未发的情况
      const getcert = testList.find((o: any) => Number(o.complete) && o.cert_src);
      if (!gotCert && getcert) {
        data.getcert = {
          cert_src: getcert.cert_src,
          test_id: getcert.id,
        };
      }

      return resOk(data);
    } catch (error) {
      return resErr(500, error.message);
    }
  }

  /** 打卡活动-打卡记录 */
  async logInfo(query: CheckinLogInfoDto, headers: CustomHeadersDto): Promise<ResObj> {
    const { period_id } = query;
    const { login_jobnum, factoryid, is_checkin_preview } = headers;

    try {
      // 查询活动配置
      let sql = `select id, step_len, days from ${DB_PRE}checkin_period_conf where factory_id=? and state=1 and id=? and start_time<=now()`;
      
      // 预览模式
      if (is_checkin_preview) {
        sql = `select id, step_len, days from ${DB_PRE}checkin_period_conf where factory_id=? and state=1 and id=?`;
      }

      const activityResult = await this.entityManager.query(
        sql,
        [factoryid, period_id]
      );

      const activity = activityResult[0];
      if (!activity?.id) {
        return resErr(101, '活动未开始');
      }

      // 查询打卡记录
      const logResult = await this.entityManager.query(
        `select id, type, chapter_id, days_continue, points, checkin_date, create_time
        from ${DB_PRE}checkin_log
        where factory_id=? and period_id=? and jobnum=?
        order by id asc`,
        [
          factoryid,
          period_id,
          login_jobnum
        ]
      );

      // 查询章节配置
      const chapterResult = await this.entityManager.query(
        `select id, days_sum, checkin_type, days_type
        from ${DB_PRE}checkin_chapter
        where factory_id=? and period_id=? and state=1
        order by sort_num asc`,
        [factoryid, period_id]
      );

      // 查询天数配置
      const daysConfResult = await this.entityManager.query(
        `select chapter_id, id
        from ${DB_PRE}checkin_days_conf
        where factory_id=? and period_id=?
        order by chapter_id asc, sort_num asc`,
        [factoryid, period_id]
      );

      const list = [];
      const now = Date.now();
      const today = new Date(timeFmt(undefined, 'Y-M-D 00:00:00')).getTime();
      const days_sum = logResult.length;
      let startTime = today;
      let points_total = 0;

      let days_continue = 0;
      if (logResult.length) {
        // 打过卡，则以第一次打卡时间为开始时间
        const last = logResult[logResult.length - 1];
        if (new Date(last.checkin_date).getTime() >= today - 24 * 3600 * 1000) {
          days_continue = last.days_continue;
        }
        startTime = new Date(timeFmt(new Date(logResult[0].create_time).getTime(), 'Y/M/D 00:00:00')).getTime();
      }

      const daysConf = daysConfResult;
      for (let index = 0; index < activity.days; index++) {
        const row = logResult[index];
        const obj: any = {};

        if (row) {
          // 已完成打卡
          obj.checked = true;
          obj.supplement = row.type === 1;
          obj.chapter_id = row.chapter_id;
          obj.log_id = row.id;
          obj.points = row.points;
          points_total += row.points;
        } else {
          // 未完成打卡
          let chapter_id = 0;
          let days = 0;
          let checkin_type = 0;
          let days_type = 0;

          for (let i = 0; i < chapterResult.length; i++) {
            const info = chapterResult[i];
            days += info.days_sum;
            if (index < days) {
              chapter_id = info.id;
              checkin_type = info.checkin_type;
              days_type = info.days_type;

              // 分别设置，返回天数配置ID
              if (days_type === 1 && daysConf.length) {
                const dayArr = daysConf.filter((o: any) => o.chapter_id === chapter_id);
                if (dayArr[index - days + info.days_sum]) {
                  obj.days_conf_id = dayArr[index - days + info.days_sum].id;
                }
              }
              break;
            }
          }

          obj.chapter_id = chapter_id;
          obj.checkin_type = checkin_type;
          obj.days_type = days_type;

          if (startTime + index * 3600 * 24 * 1000 <= now) {
            // 活动已经进行到这一天了
            obj.notcheck = true;
          } else {
            // 还未进行到这一天
            obj.locked = true;
          }
        }
        list.push(obj);
      }

      return resOk({
        step_len_list: activity.step_len?.split(',').map((v: string) => parseInt(v)) || [],
        list,
        days_sum,
        points_total,
        days_continue,
      });
    } catch (error) {
      return resErr(500, error.message);
    }
  }

  /** 打卡活动-提交打卡 */
  async submit(body: CheckinSubmitDto, headers: CustomHeadersDto): Promise<ResObj> {
    const {
      chapter_id, task_id = 0, days_conf_id, experience = '', img_keys = ''
    } = body;
    const { login_jobnum, factoryid } = headers;

    if (
      !factoryid ||
      !chapter_id ||
      (!days_conf_id && !task_id) ||
      !login_jobnum ||
      (!experience.trim() && !img_keys)
    ) {
      return resErr(101, '缺少参数');
    }

    try {
      // 查询活动和章节信息
      const activityInfo = await this.entityManager.query(`
        select p.id, p.start_time, p.end_time, p.days, c.period_id, c.image_text_color, c.bg_img, c.complete_tips, c.card_img, c.title,
        m.title as medal_title, m.cover as medal_cover, m.intro as medal_intro, c.sort_num, c.days_sum, c.checkin_type, c.days_type,
        (select sum(days_sum) from ${DB_PRE}checkin_chapter where period_id=c.period_id and sort_num<=c.sort_num and is_del=0) as days_total
        from ${DB_PRE}checkin_period_conf p, ${DB_PRE}checkin_chapter c
        left join ${DB_PRE}checkin_medal m on m.id=c.medal_id
        where c.factory_id=${factoryid} and c.id=${chapter_id} and c.period_id=p.id and p.state=1
      `);

      // 查询用户打卡记录
      const userLogs = await this.entityManager.query(`
        select l.days_sum, l.days_continue, l.checkin_date, l.create_time
        from ${DB_PRE}checkin_log l, ${DB_PRE}checkin_chapter c
        where c.id=${chapter_id} and l.period_id=c.period_id and l.jobnum='${login_jobnum}' and c.factory_id=${factoryid}
        order by l.id desc
      `);

      // 查询用户总积分
      const pointsTotal = await this.entityManager.query(`
        select sum(l.points) as points_total
        from ${DB_PRE}checkin_log l, ${DB_PRE}checkin_chapter c
        where l.period_id=c.period_id and c.id=${chapter_id} and l.factory_id=${factoryid} and l.jobnum='${login_jobnum}'
      `);

      // 查询任务完成提示
      const taskTips = await this.entityManager.query(`
        select complete_tips
        from ${DB_PRE}checkin_task
        where factory_id=${factoryid} and id=${task_id}
      `);

      // 查询任务重复次数
      const taskCount = await this.entityManager.query(`
        select count(l.id) as count
        from ${DB_PRE}checkin_log l, ${DB_PRE}checkin_chapter c
        where l.period_id=c.period_id and c.id=${chapter_id} and l.factory_id=${factoryid} and l.jobnum='${login_jobnum}' and l.task_id=${task_id}
      `);

      // 查询章节完成提示
      const chapterTips = await this.entityManager.query(`
        select complete_tips
        from ${DB_PRE}checkin_complete_tips
        where factory_id=${factoryid} and chapter_id=${chapter_id}
        order by id asc
      `);

      // 查询所有章节信息
      const allChapters = await this.entityManager.query(`
        select days_sum, id
        from ${DB_PRE}checkin_chapter
        where period_id=(select period_id from ${DB_PRE}checkin_chapter where factory_id=${factoryid} and id=${chapter_id})
        order by sort_num asc
      `);

      // 查询天数配置（如果有）
      let daysConf: any = {};
      if (days_conf_id > 0) {
        const daysConfResult = await this.entityManager.query(`
          select *
          from ${DB_PRE}checkin_days_conf
          where id=${days_conf_id}
        `);
        daysConf = daysConfResult[0] || {};
      }

      // 验证章节是否匹配
      if (daysConf.chapter_id && daysConf.chapter_id !== chapter_id) {
        return resErr(201, '章节不符');
      }

      const now = Date.now();
      const activity = activityInfo[0];
      const first = userLogs.length ? userLogs[userLogs.length - 1] : null;
      const today = new Date(timeFmt(undefined, 'Y-m-d 00:00:00')).getTime();
      const startTime = first ? new Date(timeFmt(first.create_time.getTime(), 'Y-m-d 00:00:00')).getTime() : today;
      const endTime = first ? startTime + activity.days * 24 * 3600 * 1000 : activity.end_time.getTime();

      if (!activity || (activity.start_time.getTime() > now || endTime <= now)) {
        return resErr(202, '活动未开放');
      }

      const checkinLog = userLogs[0];
      let days_sum = 1; // 累计打卡
      let days_continue = 0; // 连续打卡
      let points = 1; // 获得的积分
      let type = 0; // 打卡类型：0正常打卡 1补卡
      let checkin_date = timeFmt(undefined, 'Y-m-d'); // 打哪一天的卡

      const diffDays = Math.ceil((now - startTime) / (3600 * 24 * 1000)); // 活动已经开始的天数

      if (checkinLog) {
        // 有打卡记录
        days_sum = checkinLog.days_sum + 1;
        if (days_sum > diffDays) {
          return resErr(203, '该日还未解锁打卡');
        }

        if (diffDays - checkinLog.days_continue > 1) {
          // 中断过打卡
          if (diffDays === days_sum) {
            // 最后一天（打卡活动开始后的第N天）
            const today = new Date(timeFmt(undefined, 'Y-m-d 00:00:00'));
            if (
              checkinLog.create_time >= today.getTime() - 3600 * 24 * 1000 &&
              checkinLog.create_time < today.getTime()
            ) {
              // 上次打卡是昨天
              days_continue = checkinLog.days_continue + 1;
              points = days_continue;
            } else {
              // 上次打卡有中断
              days_continue = 1;
            }
          } else {
            // 非最后一天，补卡
            type = 1;
            checkin_date = timeFmt(checkinLog.checkin_date.getTime() + 3600 * 24 * 1000, 'Y-m-d');
          }
        } else {
          // 一直都是连续打卡
          days_continue = checkinLog.days_continue + 1;
          points = days_continue;
        }
      } else {
        // 第一次打卡
        if (diffDays > 1) {
          // 开始几天后才开始打卡，补卡
          type = 1;
          checkin_date = timeFmt(startTime, 'Y-m-d');
        } else {
          // 活动第一天
          days_continue = 1;
        }
      }

      // 临时修改，最多改成5积分
      if (days_continue > 5) {
        points = 5;
      }

      // 如果章节和天数对应不上
      if (days_sum > activity.days_total || days_sum <= activity.days_total - activity.days_sum) {
        return resErr(204, '打卡章节不符');
      }

      // 处理打卡提示
      const tipsArr = chapterTips;
      let card_tips = taskTips[0]?.complete_tips || '';
      if (activity.days_type === 1) {
        // 天数-分别设置
        if (tipsArr[daysConf.sort_num - 1]?.complete_tips) {
          card_tips = tipsArr[daysConf.sort_num - 1]?.complete_tips;
        }
      } else {
        // 天数-统一设置
        if (taskCount[0]?.count >= 1 && tipsArr.length) {
          const idx = Math.floor(Math.random() * tipsArr.length);
          card_tips = tipsArr[idx].complete_tips;
        }
      }

      const points_total = (pointsTotal[0].points_total || 0) + points;

      // 计算完成当前章节需要的总天数
      let currentChapterCompleteDays = 0;
      for (const obj of allChapters) {
        currentChapterCompleteDays += obj.days_sum;
        if (obj.id === chapter_id) {
          break;
        }
      }

      let medalInfo = {};
      let chapter_complete = false;
      // 如果打卡完今天就完成本章节
      if (days_sum >= currentChapterCompleteDays) {
        chapter_complete = true;

        // 本章节有勋章
        if (activity.medal_cover) {
          medalInfo = {
            medal_title: activity.medal_title,
            medal_cover: activity.medal_cover,
            medal_intro: activity.medal_intro,
          };
        }
      }

      // 使用事务执行所有数据库操作
      return this.entityManager.transaction(async (manager) => {
        // 插入打卡记录
        await manager.query(`
          insert into ${DB_PRE}checkin_log(period_id, chapter_id, task_id, factory_id, jobnum, create_time, checkin_date, type, points, days_sum, days_continue, experience, img_keys, card_tips, days_type, task_type, days_conf_id)
          values(${activity.period_id}, ${chapter_id}, ${task_id || null}, ${factoryid}, '${login_jobnum}', now(), '${checkin_date}', ${type}, ${points}, ${days_sum}, ${days_continue}, '${experience}', '${img_keys}', '${card_tips}', ${activity.days_type}, ${daysConf.task_type || 0}, ${days_conf_id || null})
        `);

        // 插入排名数据/更新总积分
        await manager.query(`
          insert into ${DB_PRE}checkin_rank(factory_id, jobnum, period_id, points_total, update_time, last_checkin_time)
          values(${factoryid}, '${login_jobnum}', '${activity.period_id}', '${points}', now(), now())
          on duplicate key update points_total=points_total+${points}, last_checkin_time=now()
        `);

        // 更新全部用户的排名数据
        await manager.query(`
          update ${DB_PRE}checkin_rank a
          INNER JOIN (
            select t.id, @i:=@i+1 as rankNum from
            (select @i:=0) r,
            (select * from ${DB_PRE}checkin_rank where factory_id=${factoryid} and period_id=${activity.period_id} order by points_total desc, last_checkin_time asc) as t
          ) t1
          on a.id = t1.id
          set rank = rankNum
        `);

        // 如果完成章节且有勋章，插入勋章记录
        if (chapter_complete && activity.medal_cover) {
          await manager.query(`
            insert into ${DB_PRE}medal(factory_id, jobnum, type, cover, medal_name, checkin_chapter_id, create_time)
            values(${factoryid}, '${login_jobnum}', 1, '${activity.medal_cover}', '${activity.medal_title}', '${chapter_id}', now())
          `);
        }

        // 查询用户排名
        const rankResult = await manager.query(`
          select rank
          from ${DB_PRE}checkin_rank
          where factory_id=${factoryid} and period_id=${activity.period_id} and jobnum='${login_jobnum}'
        `);

        return resOk({
          period_id: activity.id,
          points_total,
          points,
          days_sum,
          sort_num: activity.sort_num,
          checkin_type: activity.checkin_type,
          days_continue,
          chapter_title: activity.title,
          complete_tips: activity.complete_tips,
          card_tips,
          image_text_color: activity.image_text_color,
          bg_img: activity.bg_img,
          card_img: activity.card_img,
          rank: rankResult[0].rank,
          chapter_complete,
          ...medalInfo,
        });
      }).catch(err => {
        return resErr(500, err.message);
      });
    } catch (error) {
      return resErr(500, error.message);
    }
  }

  /** 百日打卡-每天的任务情况 */
  async daysInfo(query: CheckinDaysInfoDto, headers: CustomHeadersDto): Promise<ResObj> {
    const { period_id, days_num: queryDaysNum } = query;
    const { login_jobnum, factoryid } = headers;
    let days_num = queryDaysNum || 0;

    if (!factoryid || !period_id) {
      return resErr(201, '缺少参数');
    }

    try {
      // 查询活动配置
      const periodResult = await this.entityManager.query(
        `select * from ${DB_PRE}checkin_period_conf where id=? and state=1`,
        [period_id]
      );

      const period = periodResult[0];
      if (!period) {
        return resErr(202, '未查询到数据');
      }

      // 查询用户打卡记录
      const logResult = await this.entityManager.query(
        `select days_sum, create_time from ${DB_PRE}checkin_log
        where factory_id=? and period_id=? and jobnum=?
        order by id desc`,
        [
          factoryid,
          period_id,
          login_jobnum
        ]
      );

      // 查询章节配置
      const chapterResult = await this.entityManager.query(
        `select * from ${DB_PRE}checkin_chapter
        where period_id=? and state=1 and is_del=0
        order by sort_num asc`,
        [period_id]
      );

      // 查询天数配置
      const daysConfResult = await this.entityManager.query(
        `select * from ${DB_PRE}checkin_days_conf
        where period_id=?
        order by chapter_id asc, sort_num asc`,
        [period_id]
      );

      const now = Date.now();
      const last = logResult[0];
      const first = logResult.length ? logResult[logResult.length - 1] : null;
      const days_total = period.days;
      const today = new Date(timeFmt(undefined, 'Y-M-D 00:00:00')).getTime();
      const startTime = first ? new Date(timeFmt(new Date(first.create_time).getTime(), 'Y-M-D 00:00:00')).getTime() : today;
      const endTime = first ? startTime + days_total * 24 * 3600 * 1000 : new Date(period.end_time).getTime();
      const diffDays = Math.ceil(((now >= endTime ? (endTime - 1) : now) - startTime) / (3600 * 24 * 1000)); // 活动已经开始的天数

      if (days_num > days_total || days_num > diffDays + 1) {
        return resErr(203, '禁止查询数据');
      }

      // 返回第几天的任务数据
      if (!days_num) {
        days_num = last?.days_sum || 0;
      }

      const is_checked = last?.days_sum >= diffDays; // 最后一天是否打卡
      const disabled = now < new Date(period.start_time).getTime() || now >= endTime; // 是否不能打卡

      let tempDaysSum = 0;
      let curChapter: any = {};
      let daysIdx = 0; // 章节的第几天

      for (let i = 0; i < chapterResult.length; i++) {
        const o = chapterResult[i];
        const flag = days_num >= tempDaysSum && (days_num < tempDaysSum + o.days_sum || (is_checked && days_num === tempDaysSum + o.days_sum));

        if (flag) {
          curChapter = o;
          if (is_checked) {
            // 当天已经打卡、活动已结束
            daysIdx = days_num - tempDaysSum - 1;
          } else {
            daysIdx = days_num - tempDaysSum;
          }
          break;
        } else {
          tempDaysSum += o.days_sum;
        }
      }

      const data: any = {
        is_checked,
        chapter_id: curChapter.id,
        chapter_title: curChapter.title,
        checkin_type: curChapter.checkin_type,
        days_type: curChapter.days_type,
        disabled,
      };

      let sql = '';
      let params: any[] = [];

      if (curChapter.days_type === 1) {
        // 分别设置
        const daysInfo = daysConfResult.filter((o: any) => o.chapter_id === curChapter.id)[daysIdx] || {};
        delete daysInfo.sort_num;

        Object.assign(data, daysInfo);

        if (daysInfo.task_type === 1) {
          // 音频练习
          return resOk(data);
        } else {
          // 日常任务
          sql = `select * from ${DB_PRE}checkin_task where id in (${daysInfo.task_list})`;
        }
      } else {
        // 统一设置
        if (!curChapter.task_list) {
          return resOk(data);
        }

        if (curChapter.checkin_type === 1) {
          // 固定任务
          const taskId = curChapter.task_list.split(',')[daysIdx];
          sql = `select * from ${DB_PRE}checkin_task where id=?`;
          params = [taskId];
        } else {
          // 任选任务
          sql = `select * from ${DB_PRE}checkin_task where id in (${curChapter.task_list})`;
        }
      }

      if (sql) {
        const taskResult = await this.entityManager.query(sql, params);
        data.task_list = taskResult;
      }

      return resOk(data);
    } catch (error) {
      return resErr(500, error.message);
    }
  }
}

import { Injectable } from '@nestjs/common';
import { InjectEntityManager } from '@nestjs/typeorm';
import { EntityManager } from 'typeorm';

import { DB_PRE } from '../constants';
import { resOk, resErr, timeFmt, insertValues } from '../utils';
import { CustomHeadersDto } from '../dto';
import { CheckinTestSubmitDto, ActivityDetailQueryDto, UserInfoQueryDto } from '../dto/Checkin';

@Injectable()
export class CheckinService {
  constructor(@InjectEntityManager() private readonly entityManager: EntityManager) {}

  /** 打卡活动-信息列表 */
  async activityInfo(headers: CustomHeadersDto): Promise<ResObj> {
    const { login_jobnum, factoryid } = headers;

    try {
      // 查询正在进行中的活动
      const activeActivity = await this.entityManager.query(`
        select c.id as period_id, code_name, activity_desc, start_time, end_time, days
        from ${DB_PRE}checkin_period_conf c, ${DB_PRE}user u
        where c.factory_id=${factoryid}
        and u.factory_id=c.factory_id
        and u.jobnum='${login_jobnum}'
        and c.user_type like concat('%[', u.user_type ,']%')
        and c.state=1
        and c.start_time<=now()
        and c.end_time>now()
        order by c.id desc
      `);

      // 查询今日情绪打卡记录
      const emotionLog = await this.entityManager.query(`
        select count(1) as emotion_log
        from ${DB_PRE}checkin_emotion_log
        where factory_id=${factoryid}
        and jobnum='${login_jobnum}'
        and create_time>=STR_TO_DATE('${timeFmt(undefined, 'Y-M-D 00:00:00')}', '%Y-%m-%d %H:%i:%S')
      `);

      // 查询最近结束的活动
      const recentActivity = await this.entityManager.query(`
        select c.id as period_id, code_name, activity_desc, start_time, end_time, c.days, max(l.create_time) as last_checkin_time
        from ${DB_PRE}checkin_period_conf c, ${DB_PRE}user u, ${DB_PRE}checkin_log l
        where l.jobnum=u.jobnum
        and l.period_id=c.id
        and c.factory_id=${factoryid}
        and u.factory_id=c.factory_id
        and u.jobnum='${login_jobnum}'
        and c.user_type like concat('%[', u.user_type ,']%')
        and c.state=1
        and c.end_time<=now()
        GROUP BY l.period_id
        order by c.end_time desc
      `);

      const list = [];
      [...activeActivity, ...recentActivity].forEach(info => {
        info.start_time = info.start_time.getTime();
        info.end_time = info.end_time.getTime();
        info.last_checkin_time = info.last_checkin_time?.getTime() || 0;
        info.emotion_log = emotionLog[0].emotion_log;

        if (info.end_time > Date.now()) {
          // 活动进行中
          list.push(info);
        } else {
          // 活动已结束
          // 仅返回用户参与过的，只保留：持续时间 + 30天
          if (info.last_checkin_time + info.days * 24 * 3600 * 1000 > Date.now() - 30 * 24 * 3600 * 1000) {
            delete info.last_checkin_time;
            list.push(info);
          }
        }
      });

      return resOk(list);
    } catch (error) {
      return resErr(500, error.message);
    }
  }

  /** 打卡活动-详情 */
  async activityDetail(query: ActivityDetailQueryDto, headers: CustomHeadersDto): Promise<ResObj> {
    const { period_id } = query;
    const { login_jobnum, factoryid, is_checkin_preview } = headers;

    try {
      // 查询正在进行中的活动
      let sql = `select c.activity_desc, c.start_time, c.end_time, c.days, l.create_time
        from (${DB_PRE}checkin_period_conf c, ${DB_PRE}user u)
        left join ${DB_PRE}checkin_log l on l.jobnum=u.jobnum and l.period_id=c.id
        where c.factory_id=${factoryid}
        and u.jobnum='${login_jobnum}'
        and c.id=${period_id}
        and u.factory_id=c.factory_id
        and c.user_type like concat('%[', u.user_type ,']%')
        and c.state=1
        and c.start_time<=now()
        order by l.id asc
        limit 0,1`;
      
      // 预览模式
      if (is_checkin_preview) {
        sql = `select c.activity_desc, c.start_time, c.end_time, c.days, l.create_time
        from ${DB_PRE}checkin_period_conf c
        left join ${DB_PRE}checkin_log l on l.jobnum='${login_jobnum}' and l.period_id=c.id 
        where c.factory_id=${factoryid}
        and c.id=${period_id}
        and c.state=1
        order by l.id asc
        limit 0,1`;
      }

      const activityDetail = await this.entityManager.query(sql);
      if (!activityDetail?.length) {
        return resErr(202, '活动不存在');
      }

      if (activityDetail[0].create_time) {
        // 打过卡
        const startTime = new Date(timeFmt(activityDetail[0].create_time.getTime(), 'Y-M-D 00:00:00')).getTime();
        const endTime = startTime + activityDetail[0].days * 24 * 3600 * 1000;
        activityDetail[0].start_time = startTime;
        activityDetail[0].end_time = endTime;
      } else {
        // 没打过卡
        activityDetail[0].start_time = activityDetail[0].start_time.getTime();
        activityDetail[0].end_time = activityDetail[0].end_time.getTime();
      }
      delete activityDetail[0].create_time;

      return resOk(activityDetail[0]);
    } catch (error) {
      return resErr(500, error.message);
    }
  }

  /** 打卡活动-简单详情 */
  async simpleDetail(query: ActivityDetailQueryDto): Promise<ResObj> {
    const { period_id } = query;

    try {
      // 查询正在进行中的活动
      const activityDetail = await this.entityManager.query(`
        select code_name, activity_desc
        from ${DB_PRE}checkin_period_conf
        where id=${period_id}
        and state=1
      `);

      if (!activityDetail?.length) {
        return resErr(202, '活动不存在');
      }

      return resOk(activityDetail[0]);
    } catch (error) {
      return resErr(500, error.message);
    }
  }

  /** 打卡活动-提交测试问卷 */
  async testSubmit(body: CheckinTestSubmitDto, headers: CustomHeadersDto): Promise<ResObj> {
    const { period_id, test_id, answer_list } = body;
    const { login_jobnum, factoryid } = headers;

    try {
      // 查询测试问卷配置
      const testInfo = await this.entityManager.query(
        `select * from ${DB_PRE}checkin_test_conf
        where factory_id=? and id=? and period_id=? and state=1`,
        [
          factoryid,
          test_id,
          period_id
        ]
      );

      if (!testInfo?.length) {
        return resErr(202, '问卷不存在');
      }

      // 检查是否已提交过
      const submittedCount = await this.entityManager.query(
        `select count(1) as count
        from ${DB_PRE}checkin_test_log
        where factory_id=? and jobnum=? and test_id=? and period_id=?`,
        [
          factoryid,
          login_jobnum,
          test_id,
          period_id
        ]
      );

      if (submittedCount[0].count > 0) {
        return resErr(203, '请勿重复提交');
      }

      // 获取题目列表
      const topicList = await this.entityManager.query(
        `select title, type, dimension, is_scored
        from ${DB_PRE}checkin_test_topic
        where test_id=?
        order by sort_num asc`,
        [test_id]
      );

      if (topicList.length !== answer_list.length) {
        return resErr(204, '题目数量不对');
      }

      // 准备插入数据
      const dimension: Record<string, number[]> = {}; // 维度得分
      const rows: any[][] = [];
      const columns = [
        'factory_id',
        'jobnum',
        'period_id',
        'test_id',
        'topic_id',
        'dimension',
        'answer',
        'score',
        'is_scored',
        'create_time',
        'test_title',
        'topic_title',
        'topic_type',
        'test_day_num',
        'test_type'
      ];

      topicList.forEach((topic: any, index: number) => {
        const answer = answer_list[index];

        // 如果参与计分
        if (topic.is_scored) {
          if (!dimension[topic.dimension]) {
            dimension[topic.dimension] = [];
          }
          dimension[topic.dimension].push(answer.score);
        }

        // 添加对应的值到参数数组
        rows.push([
          factoryid,
          login_jobnum,
          period_id,
          test_id,
          answer.topic_id,
          topic.dimension || '',
          answer.value || '',
          answer.score !== undefined ? answer.score : null,
          topic.is_scored,
          new Date(), // create_time
          testInfo[0].title,
          topic.title,
          topic.type,
          testInfo[0].day_num,
          testInfo[0].type
        ]);
      });

      // 使用 insertValues 方法执行批量插入
      await insertValues(
        this.entityManager,
        'checkin_test_log',
        columns,
        rows
      );

      // 计算各维度得分
      const dimension_list = [];
      const entries = Object.entries(dimension);

      entries.forEach(([label, scores]) => {
        // 使用 Function 构造函数代替 eval，更安全且避免 eslint 警告
        // 创建一个函数，接收 scores 参数并执行计算公式
        const calculateScore = new Function(
          'scores',
          `return ${testInfo[0].score_formula?.replace(/\{list\}/g, 'scores')}`
        );

        const obj = {
          label,
          value: calculateScore(scores)
        };
        dimension_list.push(obj);
      });

      // 获取活动代号
      const periodInfo = await this.entityManager.query(
        `select code_name from ${DB_PRE}checkin_period_conf where id=?`,
        [period_id]
      );

      // 返回结果
      return resOk({
        cert_src: testInfo[0].cert_src, // 证书图片地址（为空不发证书）
        dimension_list, // 维度得分
        code_name: periodInfo[0]?.code_name,
        // 维度解析
        analysis: {
          recap_json: testInfo[0].recap_json,
          score_json: testInfo[0].score_json,
          intro_json: testInfo[0].intro_json,
          suggest_json: testInfo[0].suggest_json,
          score_tips: testInfo[0].score_tips,
          recap_prev: testInfo[0].recap_prev,
          score_split: testInfo[0].score_split,
        }
      });
    } catch (error) {
      return resErr(205, error.message);
    }
  }

  /** 打卡活动-用户活动信息 */
  async userInfo(query: UserInfoQueryDto, headers: CustomHeadersDto): Promise<ResObj> {
    const { period_id } = query;
    const { login_jobnum, factoryid, is_checkin_preview } = headers;

    if (!factoryid || !login_jobnum || !period_id) {
      return resErr(201, '缺少参数');
    }

    const now = Date.now();
    try {
      // 查询活动信息
      const activityInfo = await this.entityManager.query(`
        select id, days, start_time, end_time
        from ${DB_PRE}checkin_period_conf
        where factory_id=${factoryid}
        and state=1
        and id=${period_id}
      `);

      if (!activityInfo[0]?.id) {
        return resErr(101, '活动不存在');
      }

      if (!is_checkin_preview && activityInfo[0].start_time > now) {
        return resErr(102, '活动未开始');
      }

      // 查询用户打卡记录
      const logInfo = await this.entityManager.query(`
        select l.days_sum, l.chapter_id, c.days_sum as chapter_days_sum, l.create_time
        from ${DB_PRE}checkin_log l, ${DB_PRE}checkin_chapter c
        where l.factory_id=${factoryid}
        and l.period_id=${period_id}
        and l.jobnum='${login_jobnum}'
        and l.chapter_id=c.id
        order by l.id asc
      `);

      // 查询用户喜爱度评价
      // const favoriteInfo = await this.entityManager.query(`
      //   select checkin_days_sum
      //   from ${DB_PRE}checkin_task_favorite
      //   where factory_id=${factoryid}
      //   and jobnum='${login_jobnum}'
      //   and period_id=${period_id}
      //   order by id desc limit 0,1
      // `);

      // 查询测试问卷信息
      const testList = await this.entityManager.query(`
        select c.id, c.day_num, c.type, c.title, c.tooltips,
        (select count(1) from ${DB_PRE}checkin_test_log where test_id=c.id and jobnum='${login_jobnum}' and period_id=${period_id}) as complete,
        c.cert_src
        from ${DB_PRE}checkin_test_conf c
        where c.factory_id=${factoryid}
        and c.state=1
        and c.period_id=${period_id}
        order by c.day_num asc
      `);

      // 查询章节信息
      const chapterList = await this.entityManager.query(`
        select task_list, days_sum, checkin_type, days_type, id
        from ${DB_PRE}checkin_chapter
        where factory_id=${factoryid}
        and period_id=${period_id}
        and state=1
        order by sort_num asc
      `);

      // 查询天数配置信息
      const daysList = await this.entityManager.query(`
        select *
        from ${DB_PRE}checkin_days_conf
        where factory_id=${factoryid}
        and period_id=${period_id}
        order by chapter_id asc, sort_num asc
      `);

      // 查询是否获得证书
      const certInfo = await this.entityManager.query(`
        select count(1) as count
        from ${DB_PRE}cert
        where jobnum='${login_jobnum}'
        and factory_id='${factoryid}'
        and type=1
        and checkin_period_id=${period_id}
      `);

      const days_total = activityInfo[0].days;
      const lastLogInfo = logInfo[logInfo.length - 1] || {};
      const gotCert = certInfo[0].count > 0;

      // 设置默认值
      if (lastLogInfo.days_sum === undefined) {
        lastLogInfo.days_sum = 0;
      }

      let task_id = null; // 任务ID，首页点击后直接跳去打卡
      let days_conf_id = null; // 天数配置ID，首页点击后直接跳去打卡
      let daysSum = 0;
      let days_type = 0;

      // 计算当前应该打卡的任务
      for (let index = 0; index < chapterList.length; index++) {
        const chapter = chapterList[index];

        daysSum += chapter.days_sum;
        if (lastLogInfo.days_sum < daysSum) {
          const idx = lastLogInfo.days_sum - daysSum + chapter.days_sum;

          if (chapter.days_type === 1) {
            // 分别设置
            const daysConfigs = daysList.filter(o => o.chapter_id === chapter.id);
            days_conf_id = daysConfigs[idx]?.id;
          } else {
            // 统一设置
            const taskIds = chapter.task_list?.split(',') || [];
            task_id = taskIds[idx] || null;
          }
          days_type = chapter.days_type;
          break;
        }
      }

      // 构建返回数据
      const data = {
        days_sum: lastLogInfo.days_sum, // 累计打卡天数
        days_total,
        task_id,
        days_conf_id,
        days_type,
        task_evaluate: null, // 是否要作出最喜欢的任务评价
        test_info: null, // 是否需要先进行测试
        getcert: null,
      };

      // 是否有需要完成的测试
      const testInfo = testList.find(o => {
        let num = 0;
        if (o.type === 0) {
          // 前测
          num = 1;
        }
        return lastLogInfo.days_sum + num >= o.day_num && !Number(o.complete);
      });

      // 活动已结束，只返回后测题
      const endTime = logInfo.length
        ? new Date(timeFmt(logInfo[0]?.create_time.getTime(), 'Y-m-d 00:00:00')).getTime() + activityInfo[0].days * 24 * 3600 * 1000
        : activityInfo[0].end_time.getTime();

      if (testInfo && (Date.now() < endTime || testInfo.type === 1)) {
        data.test_info = {
          test_id: testInfo.id,
          title: testInfo.title,
          tooltips: testInfo.tooltips,
        };
      }

      // 是否有需要发证书未发的情况
      const getcert = testList.find(o => Number(o.complete) && o.cert_src);
      if (!gotCert && getcert) {
        data.getcert = {
          cert_src: getcert.cert_src,
          test_id: getcert.id,
        };
      }

      return resOk(data);
    } catch (error) {
      return resErr(500, error.message);
    }
  }
}

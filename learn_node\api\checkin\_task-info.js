/**
 * @description 百日打卡-任务详情
 */

module.exports = (req, res) => {
	let json = {
		code: 0,
		data: {},
		msg: '',
	};
	
	const login_jobnum = safeString(req.headers.login_jobnum);
	const factoryid = parseInt(req.headers.factoryid);
	const task_id = parseInt(req.query.task_id);
	const days_conf_id = parseInt(req.query.days_conf_id);
	const chapter_id = parseInt(req.query.chapter_id);
	
	if (!factoryid || (!days_conf_id && (!task_id || !chapter_id))) {
		json.code = 201;
		json.msg = '缺少参数';
		res.json(json);
		return;
	}

	const now = Date.now();
	if (days_conf_id) {
		// 分别设置的打卡任务
		querySql(`select c.chapter_img, c.image_text_color, c.days_type, (select sum(days_sum) from ${databasePre}checkin_chapter where period_id=c.period_id and sort_num<c.sort_num and is_del=0) as chapter_diff_days, d.sort_num, c.title as chapter_title, d.task_type, d.task_list, d.audio_intro, d.audio_intro_name, d.audio_practice, d.audio_practice_name, d.audio_intro_img, d.audio_intro_duration, d.audio_practice_duration from ${databasePre}checkin_days_conf d, ${databasePre}checkin_chapter c where d.factory_id=${factoryid} and d.id=${days_conf_id} and d.chapter_id=c.id;
		
		select p.id, p.end_time, p.days, l.create_time from (${databasePre}checkin_period_conf p, ${databasePre}checkin_chapter c) left join ${databasePre}checkin_log l on l.period_id=p.id and l.jobnum='${login_jobnum}' where c.factory_id=${factoryid} and p.state=1 and c.id=${chapter_id} and p.start_time<=now() and c.period_id=p.id order by l.id asc limit 0,1;`, (err, result, conn) => {
			if (err) {
				res.json(err);
				return;
			}

			const row = result[0][0];
			if (!row) {
				json.code = 202;
				json.msg = '未查询到数据';
				res.json(json);
				return;
			}

			// chapter_diff_days: 本章节前需要完成的天数
			json.data = row;

			const period = result[1][0];
			
			if (period) {
				// 活动已开始
				if (period.create_time) {					
					// 打过卡，判断是否超过持续时间
					json.data.disabled = new Date(timeFormatter(period.create_time.getTime(), 'Y-m-d 00:00:00')).getTime() + period.days * 24 * 3600 * 1000 <= now;
				} else {
					// 从未打卡，判断活动是否结束
					json.data.disabled = period.end_time.getTime() <= now;
				}
			} else {
				// 活动未开始
				json.data.disabled = true;
			}

			if (row.task_type === 0 && row.task_list) {
				// 日常任务
				conn.query(`select id, title, intro from ${databasePre}checkin_task where id in(${row.task_list}) order by field(id, ${row.task_list})`, (err, re) => {
					if (err) {
						res.json(err);
						return;
					}

					json.data.task_list = re;
					res.json(json);
				});
			} else {
				res.json(json);
			}
		})
	} else {
		// 统一设置打卡任务
		querySql(`select t.title, t.intro, t.intro_voice, t.intro_voice_duration, c.image_text_color, c.chapter_img, p.start_time, c.days_sum as chapter_days_sum, (select sum(days_sum) from ${databasePre}checkin_chapter where period_id=c.period_id and sort_num<=c.sort_num and is_del=0) as acc_days, (select days_sum from ${databasePre}checkin_log where period_id=c.period_id and jobnum='${login_jobnum}' and factory_id=${factoryid} order by id desc limit 0,1) as days_sum from ${databasePre}checkin_task t, ${databasePre}checkin_chapter c, ${databasePre}checkin_period_conf p where t.factory_id=${factoryid} and t.id=${task_id} and c.factory_id=t.factory_id and c.id=${chapter_id} and c.period_id=p.id;
		
		select p.id, p.end_time, p.days, l.create_time from (${databasePre}checkin_period_conf p, ${databasePre}checkin_chapter c) left join ${databasePre}checkin_log l on l.period_id=p.id and l.jobnum='${login_jobnum}' and l.chapter_id=c.id where c.factory_id=${factoryid} and p.state=1 and c.id=${chapter_id} and p.start_time<=now() and c.period_id=p.id order by l.id asc limit 0,1;`, (err, result) => {
			if (err) {
				res.json(err);
				return;
			}

			if (!result.length) {
				json.code = 202;
				json.msg = '未查询到数据';
				res.json(json);
				return;
			}

			const row = result[0][0];
			const period = result[1][0];
			let disabled = true;
			if (row.days_sum >= row.acc_days) {
				// 本章已经打完（调过时间）
				disabled = true;
			} else {
				if (period) {
					// 活动进行中
					if (period.create_time) {
						// 打过卡
						const logTime = new Date(timeFormatter(period.create_time.getTime(), 'Y-m-d 00:00:00')).getTime();
						if (logTime + period.days * 24 * 3600 * 1000 <= now) {
							// 已超过持续时间
							disabled = true;
						} else {
							// 未超过持续时间，判断是否已经打完最后一天
							disabled = row.days_sum >= Math.ceil((now - logTime) / (3600 * 24 * 1000));
						}
					} else {
						// 从未打卡，判断活动是否结束
						disabled = period.end_time.getTime() <= now;
					}
				} else {
					// 活动未开始
					disabled = true;
				}
			}
			json.data = {
				title: row.title,
				intro: row.intro,
				intro_voice: row.intro_voice,
				intro_voice_duration: row.intro_voice_duration,
				chapter_img: row.chapter_img,
				chapter_days_sum: row.chapter_days_sum,
				chapter_acc_days: row.acc_days, // 需要的累计天数
				image_text_color: row.image_text_color,
				// 活动未开放 | 已经打完最后一天 | 本章已经打完（调过时间）
				// disabled: !result[1][0].count || row.days_sum >= diffDays || row.days_sum >= row.acc_days,
				disabled,
			}

			res.json(json);
		})
	}
}
/**
 * <AUTHOR>
 * @description 获取小程序二维码
 */

const acToken = require('./_acToken');

module.exports = (req, res) => {
	let json = {
		code: 0,
		data: {},
		msg: '',
	};

	const factory_id = parseInt(req.query.factory_id) || 0;
	if (!factory_id) {
		json.code = 201;
		json.msg = '缺少参数';
		res.json(json);
		return;
	}

	let scene = Object.entries(req.query).map(([k, v]) => `${k}=${v}`).join('&');
	if (req.query.path === 'checkinPreview') {
		// 打卡预览
		scene = `1:;${req.query.factory_id}:;${req.query.jobnum}:;${req.query.period_id}`;
	}
	
	acToken().then(actk => {
		request.post({
			url: `https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=${actk}`,
			json: {
				scene,
			},
		}).pipe(res);
	});
}
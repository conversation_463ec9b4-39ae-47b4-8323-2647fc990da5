/**
 * <AUTHOR>
 * @description: 获取题目列表
 * @param type 1游客 2申请志愿者 3志愿者（正式学习模块）
 */

module.exports = async (req, res) => {
	let json = {
		code: 0,
		data: {},
		msg: '',
	};

	const type = parseInt(req.query.type);
	const factoryid = parseInt(req.headers.factoryid);
	const wxopenid = safeString(req.headers.wxopenid);

	if (!type) {
		json.code = 201;
		json.msg = '缺少参数';
		res.json(json);
		return;
	}

	let sql = '';
	const ip = await getIp(req);
	switch(type) {
		// 游客
		case 1:
			sql = `SELECT id,title,A,B,C,D,answer,analysis FROM ${databasePre}quelib_1 WHERE switch = '1';
			
			insert into ${databasePre}youke_iplog(ip, create_time, factory_id, wx_openid) values('${ip}', now(), '${factoryid}', '${wxopenid}')`;
			break;
	}
	
	querySql(sql, (err, result) => {
		if (err) {
			res.json(err);
			return;
		}

		json.data = result[0];
		res.json(json);
	})
}
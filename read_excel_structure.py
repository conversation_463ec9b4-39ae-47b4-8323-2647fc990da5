import pandas as pd
import sys
import openpyxl
from openpyxl.utils import get_column_letter

def read_excel_structure(file_path):
    try:
        # Read the Excel file with pandas for data
        df = pd.read_excel(file_path)
        print(f"文件: {file_path}")
        print(f"表格大小: {df.shape[0]} 行 x {df.shape[1]} 列")
        
        # Use openpyxl to get merged cells and other structure information
        wb = openpyxl.load_workbook(file_path)
        sheet = wb.active
        
        # Get merged cells
        print("\n合并单元格:")
        for merged_cell_range in sheet.merged_cells.ranges:
            print(f"范围: {merged_cell_range}")
        
        # Print cell values with coordinates
        print("\n单元格内容:")
        max_row = min(sheet.max_row, 30)  # Limit to first 30 rows
        max_col = min(sheet.max_column, 10)  # Limit to first 10 columns
        
        # Print column headers
        header_row = ["位置"]
        for col in range(1, max_col + 1):
            header_row.append(f"列{col}")
        print(" | ".join(str(item).ljust(15) for item in header_row))
        print("-" * (17 * len(header_row)))
        
        # Print cell values
        for row in range(1, max_row + 1):
            row_data = [f"行{row}"]
            for col in range(1, max_col + 1):
                cell = sheet.cell(row=row, column=col)
                cell_value = str(cell.value) if cell.value is not None else ""
                row_data.append(cell_value[:15])  # Limit to 15 chars
            print(" | ".join(str(item).ljust(15) for item in row_data))
        
        if sheet.max_row > max_row or sheet.max_column > max_col:
            print("... (表格太大，只显示部分内容)")
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
        read_excel_structure(file_path)
    else:
        print("Please provide the path to an Excel file")

<template>
	<scroll-view
		class="my-points por"
		scroll-y
		:bounces="false"
		:class="['type' + user.userType]"
		@scrolltolower="loadMore()"
		enhanced>
		<Navback />

		<view
			class="total-score"
			:style="{ top: user.safePadHeight }">
			<view class="total-score__badge">我的积分</view>
			<view class="total-score__num">{{ scoreTotal }}</view>
		</view>

		<view class="ui-radius-content">
			<view class="tabs">
				<view
					class="tabs-item"
					:class="{ active: tabIdx === i }"
					v-for="(v, i) in tabsArr"
					:key="i"
					@click="changeTabIdx(i)">
					{{ v }}
				</view>
			</view>

			<view
				class="score-info"
				v-if="tabIdx === 0">
				<view
					class="score-info__item"
					v-for="o in scoreList"
					:key="name">
					<view class="score-info__name fwb f36">
						{{ o.name }}
					</view>
					<view
						class="score-info__times f24"
						:class="{ hide: o.times === undefined }">
						已成功打卡{{ o.times ?? emptyValue }}次
					</view>
					<view class="score-info__nums mt40 f28">
						<text class="fwb f56">{{ o.score ?? emptyValue }}</text>
						分
					</view>
				</view>
			</view>

			<template v-else>
				<view class="c_d tac pt20 mt10 pb20 f24">—— 排行榜数据更新1次/天，当天数据隔天更新 ——</view>
				<view class="myrank fwb df c-c">
					<view class="rank">查看范围</view>
					<view
						class="filter"
						@click="popupRef.open('bottom')">
						{{ rangeYear ? rangeYear + '年' : '全部' }}
						<uni-icons
							type="down"
							size="12"
							color="#999"></uni-icons>
					</view>
				</view>

				<!-- 列表 -->
				<view
					class="rank-li df s-c"
					:class="['r' + (i + 1), { current: i + 1 == myInfo.rank }]"
					v-for="(o, i) in rankList"
					:key="i">
					<view class="no">
						<view class="no_ct df c-c">
							{{ i < 3 ? '' : i + 1 }}
						</view>
					</view>
					<view class="jobnum fwb f36">
						{{ o.jobnum }}
					</view>
					<view class="points f24">
						<text class="f36 fwb">{{ o.score_total }}</text>
						积分
					</view>
				</view>

				<uni-load-more
					color="#969799"
					:status="loadMoreState"
					:contentText="{ contentdown: '点击显示全部数据' }"
					@clickLoadMore="loadAllData"></uni-load-more>

				<!-- 悬浮固定 -->
				<view
					class="rank-li df s-c fixed-rank"
					v-if="rankList.length">
					<view class="no">
						<view class="no_ct df c-c">
							{{ myInfo.rank }}
						</view>
					</view>
					<view class="jobnum fwb f36">
						{{ user.loginJobnum }}
					</view>
					<view class="points f24">
						<text class="f36 fwb">{{ myInfo.score_total }}</text>
						积分
					</view>
				</view>
			</template>
		</view>

		<uni-popup
			ref="popupRef"
			:safe-area="false">
			<view class="year-list">
				<view class="year-list__title">查看范围</view>
				<view
					class="year-list__item df c-c"
					:class="{ disabled: o.disabled }"
					v-for="(o, i) in yearsList"
					:key="i"
					@click="onSelect(o)">
					{{ o.value ? o.value + '年' : '全部' }}
				</view>
			</view>
		</uni-popup>
	</scroll-view>
</template>

<script setup name="MyPoints">
/**
 * @description 我的积分
 */
import { computed, ref } from 'vue';
import { storeToRefs } from 'pinia';
import { useUserStore } from '@/stores/user';

import Navback from '@/components/Navback/index.vue';

const { user } = storeToRefs(useUserStore());

const emptyValue = ref('-');

const tabIdx = ref(1); // 当前选中的索引
const tabsArr = ref(['积分详情', '排行榜']);
function changeTabIdx(idx) {
	if (tabIdx.value === idx) return;
	tabIdx.value = idx;
}

const scoreList = ref([
	{
		name: '志愿者培训'
	},
	{
		name: '打卡活动'
	},
	{
		name: '服务记录'
	},
	{
		name: '其它积分'
	}
]);
const scoreTotal = computed(() => {
	return scoreList.value[0].score === undefined
		? emptyValue.value
		: scoreList.value.reduce((pre, cur) => {
				return (parseFloat(pre) || 0) + (parseFloat(cur?.score) || 0);
		  }, null);
});
async function getScoreInfo() {
	const { code, data } = await ajax('api/user/score-info');
	if (code) return;

	scoreList.value[0] = {
		...scoreList.value[0],
		...(data.training || {})
	};
	scoreList.value[1] = {
		...scoreList.value[1],
		...(data.checkin || {})
	};
	scoreList.value[2] = {
		...scoreList.value[2],
		...(data.service || {})
	};
	scoreList.value[3] = {
		...scoreList.value[3],
		...(data.other || {})
	};
}

const rangeYear = ref('');
const popupRef = ref();
const yearsList = computed(() => {
	const arr = [null];
	for (let i = 2023; i <= new Date().getFullYear(); i++) {
		arr.push(i);
	}

	return arr.map((v) => {
		return {
			value: v,
			disabled: rangeYear.value === v
		};
	});
});
function onSelect(obj) {
	if (!obj || rangeYear.value === obj.value) return;
	rangeYear.value = obj.value || '';
	onlyShort.value = true;
	pageSize.value = 10;
	finished.value = false;
	pageNum.value = 0;
	loadMore(true);
	popupRef.value.close();
}

const myInfo = ref({});
const rankList = ref([]);
const loading = ref(false);
const finished = ref(false);
const pageSize = ref(10);
const pageNum = ref(0);
const onlyShort = ref(true); // 是否 仅展示前10
const loadMoreState = computed(() => {
	if (finished.value) {
		return 'noMore';
	} else {
		return onlyShort.value ? 'more' : 'loading';
	}
});
async function loadMore(init) {
	if (finished.value || loading.value || (!init && onlyShort.value) || (!tabIdx.value && pageNum.value > 0)) return;

	pageNum.value++;
	loading.value = true;

	const { code, data } = await ajax({
		url: 'user/score-rank-list',
		data: {
			year: rangeYear.value,
			page_num: pageNum.value,
			page_size: pageSize.value
		}
	});
	loading.value = false;
	if (code) return;

	if (pageNum.value > 1) {
		rankList.value = rankList.value.concat(...data.list);
	} else {
		rankList.value = data.list;

		// 第一页加载“我的”信息
		data.my_info.rank = data.my_info.rank || emptyValue.value;
		data.my_info.points_total = data.my_info.points_total || emptyValue.value;
		myInfo.value = data.my_info;
	}

	if (data.list.length < pageSize.value) {
		finished.value = true;
	} else {
		finished.value = false;
	}
}
function loadAllData() {
	if (loading.value) return;
	onlyShort.value = false;
	pageSize.value = 20;
	pageNum.value = 0;
	loadMore();
}

getScoreInfo();
loadMore(true);
</script>

<style lang="scss">
@import 'index.scss';
</style>

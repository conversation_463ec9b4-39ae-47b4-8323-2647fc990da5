@import '@/scss/mixins.scss';

.formal-model1 {
	.box {
		width: 590rpx;
		margin: 0 auto;
		font-size: 30rpx;
		line-height: 1.5;
	}
	.subtitle {
		font-size: 38rpx;
		margin: 0 auto 40rpx;
	}
	.answer-text {
		position: relative;
		margin: 0 0 30rpx 40rpx;
	}
	.answer-serial {
		position: absolute;
		left: 0;
		top: 0;
		transform: translateX(-40rpx);
	}
	.i-check {
		position: absolute;
		font-size: 80rpx;
		color: $c_main_light;
		top: 50%;
		right: 10rpx;
		transform: translateY(-60%);
	}
	.subtit {
		font-size: 48rpx;
		position: relative;
		margin: 60rpx 0 30rpx;
		z-index: 0;
		&:before {
			content: '';
			position: absolute;
			width: 110rpx;
			height: 10rpx;
			border-radius: 10rpx;
			background: $c_main_light;
			left: 0;
			bottom: 8rpx;
			z-index: -1;
			// opacity: .5;
		}
	}
	.info {
		margin-bottom: 80rpx;
	}
}

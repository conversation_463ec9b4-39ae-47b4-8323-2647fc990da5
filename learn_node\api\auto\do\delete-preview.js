/**
 * @description 删掉预览工号数据
 */
const { timerSuccess, timerError } = require('../../../utils/log4js');

module.exports = () => {
	timerSuccess(`删掉预览工号数据-开始`)

	const previewJobnumList = [
		'PreviewTest1',
		'PreviewTest2',
		'PreviewTest3',
		'PreviewTest4',
		'PreviewTest5',
		'PreviewTest6',
		'PreviewTest7',
		'PreviewTest8',
		'PreviewTest9',
		'PreviewTest10',
	]
	const jobnums = previewJobnumList.map(v => `'${v}'`).join();

	const sqlArr = [
		`delete from ${databasePre}checkin_log where jobnum in(${jobnums})`,
		`delete from ${databasePre}checkin_rank where jobnum in(${jobnums})`,
		`delete from ${databasePre}checkin_task_favorite where jobnum in(${jobnums})`,
		`delete from ${databasePre}checkin_test_log where jobnum in(${jobnums})`,
		`delete from ${databasePre}cert where type=1 and jobnum in(${jobnums})`,
		`delete from ${databasePre}medal where type=1 and jobnum in(${jobnums})`,
	];
	execTrans(sqlArr, (err) => {
		if (err) {
			timerError(err);
			return;
		}
		timerSuccess('删掉预览工号数据-完成')
	})
}
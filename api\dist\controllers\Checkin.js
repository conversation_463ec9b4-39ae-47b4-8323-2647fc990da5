"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CheckinController = void 0;
const common_1 = require("@nestjs/common");
const dto_1 = require("../dto");
const Checkin_1 = require("../dto/Checkin");
const Checkin_2 = require("../services/Checkin");
const validate_dto_1 = require("../filters/validate-dto");
let CheckinController = class CheckinController {
    constructor(service) {
        this.service = service;
    }
    async activityInfo(headers) {
        const headerDto = await (0, validate_dto_1.validateDto)(dto_1.CustomHeadersDto, headers);
        return this.service.activityInfo(headerDto);
    }
    async activityDetail(query, headers) {
        const headerDto = await (0, validate_dto_1.validateDto)(dto_1.CustomHeadersDto, headers);
        return this.service.activityDetail(query, headerDto);
    }
    async simpleDetail(query) {
        return this.service.simpleDetail(query);
    }
    async testSubmit(body, headers) {
        const headerDto = await (0, validate_dto_1.validateDto)(dto_1.CustomHeadersDto, headers);
        return this.service.testSubmit(body, headerDto);
    }
    async userInfo(query, headers) {
        const headerDto = await (0, validate_dto_1.validateDto)(dto_1.CustomHeadersDto, headers);
        return this.service.userInfo(query, headerDto);
    }
    async logInfo(query, headers) {
        const headerDto = await (0, validate_dto_1.validateDto)(dto_1.CustomHeadersDto, headers);
        return this.service.logInfo(query, headerDto);
    }
    async submit(body, headers) {
        const headerDto = await (0, validate_dto_1.validateDto)(dto_1.CustomHeadersDto, headers);
        return this.service.submit(body, headerDto);
    }
    async daysInfo(query, headers) {
        const headerDto = await (0, validate_dto_1.validateDto)(dto_1.CustomHeadersDto, headers);
        return this.service.daysInfo(query, headerDto);
    }
};
exports.CheckinController = CheckinController;
__decorate([
    (0, common_1.Get)('activity-info'),
    __param(0, (0, common_1.Headers)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.CustomHeadersDto]),
    __metadata("design:returntype", Promise)
], CheckinController.prototype, "activityInfo", null);
__decorate([
    (0, common_1.Get)('activity-detail'),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Headers)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Checkin_1.ActivityDetailQueryDto, dto_1.CustomHeadersDto]),
    __metadata("design:returntype", Promise)
], CheckinController.prototype, "activityDetail", null);
__decorate([
    (0, common_1.Get)('simple-detail'),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Checkin_1.ActivityDetailQueryDto]),
    __metadata("design:returntype", Promise)
], CheckinController.prototype, "simpleDetail", null);
__decorate([
    (0, common_1.Post)('test-submit'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Headers)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Checkin_1.CheckinTestSubmitDto,
        dto_1.CustomHeadersDto]),
    __metadata("design:returntype", Promise)
], CheckinController.prototype, "testSubmit", null);
__decorate([
    (0, common_1.Get)('userinfo'),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Headers)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Checkin_1.UserInfoQueryDto,
        dto_1.CustomHeadersDto]),
    __metadata("design:returntype", Promise)
], CheckinController.prototype, "userInfo", null);
__decorate([
    (0, common_1.Get)('log-info'),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Headers)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Checkin_1.CheckinLogInfoDto,
        dto_1.CustomHeadersDto]),
    __metadata("design:returntype", Promise)
], CheckinController.prototype, "logInfo", null);
__decorate([
    (0, common_1.Post)('submit'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Headers)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Checkin_1.CheckinSubmitDto,
        dto_1.CustomHeadersDto]),
    __metadata("design:returntype", Promise)
], CheckinController.prototype, "submit", null);
__decorate([
    (0, common_1.Get)('days-info'),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Headers)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Checkin_1.CheckinDaysInfoDto,
        dto_1.CustomHeadersDto]),
    __metadata("design:returntype", Promise)
], CheckinController.prototype, "daysInfo", null);
exports.CheckinController = CheckinController = __decorate([
    (0, common_1.Controller)('checkin'),
    __metadata("design:paramtypes", [Checkin_2.CheckinService])
], CheckinController);
//# sourceMappingURL=Checkin.js.map
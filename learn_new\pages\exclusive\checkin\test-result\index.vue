<template>
	<view class="checkin-test-result">
		<FixedHeader :height="352 + 50">
			<!-- 综合评分 -->
			<TotalScore
				:analysis="analysis"
				:score="totalScore"
				style="top: 50rpx; position: relative" />
		</FixedHeader>

		<view class="ui-radius-content">
			<view class="title mb50">本次测试结果</view>
			<view
				class="ct-wrap mt30 f_c_orange f26"
				v-if="analysis.score_tips">
				{{ analysis.score_tips }}
			</view>

			<!-- 雷达图 -->
			<ChartRadar
				:data="dataArr"
				v-if="dataArr.length >= 3" />

			<!-- 得分解析 -->
			<ScoreAnalysisSimple
				:analysis="analysis"
				:data="dataArr" />

			<view class="ct-wrap">
				<view class="tips-text">测评结果可在打卡记录中回顾*</view>

				<!-- 百日打卡相关 -->
				<view class="btns-wrap">
					<view class="star star1"></view>
					<view class="star star2"></view>
					<view class="star star3"></view>
					<view
						class="ui-button-orange"
						@click="backHome">
						返回首页
					</view>
				</view>
			</view>
		</view>

		<!-- 填写证书 -->
		<PopupGetCert
			ref="getCertRef"
			:imgSrc="certImg"
			:periodId="periodId"
			:testId="testId"
			:codeName="codeName" />
	</view>
</template>

<script setup name="CheckinTestResult">
/**
 * @description 百日打卡-测试结果
 */
import { onLoad } from '@dcloudio/uni-app';
import { ref, computed } from 'vue';
import { goBack } from '@/utils/tools.js';
import { decodeData } from '@/utils/forward-data.js';

import FixedHeader from '@/components/FixedHeader/index.vue';
import TotalScore from '@/components/TotalScore/index.vue';
import ChartRadar from '@/components/ChartRadar/index.vue';
// import ScoreAnalysis from '@/components/ScoreAnalysis/index.vue';
import ScoreAnalysisSimple from '@/components/ScoreAnalysisSimple/index.vue';
import PopupGetCert from '../components/PopupGetCert/index.vue';

// 总分
const totalScore = computed(() => {
	const arr = dataArr.value;
	if (!arr.length) return 0;

	let sum = 0;
	arr.forEach((o) => {
		sum += parseFloat(o.value);
	});
	return sum / arr.length;
});

const certImg = ref(); // 证书图片地址
const codeName = ref(''); // 活动名称
const getCertRef = ref();
const periodId = ref();
const testId = ref();

// 返回打卡首页
function backHome() {
	const allPages = getCurrentPages();
	const idx = allPages.findIndex((o) => o.route === 'pages/exclusive/checkin/index/index');
	goBack({
		delta: allPages.length - idx - 1
	});
}

/**
 * 测评结果
 * @param [{ label, value }]
 */
const dataArr = ref([]);
const analysis = ref({});

// 百日打卡相关
// const textIdx = ref(0);
// const textArr = ref(['开启', '继续', '继续', '完成']);
onLoad((opts) => {
	// textIdx.value = opts.textIdx;	
	dataArr.value = decodeData(opts.data, []);
	analysis.value = decodeData(opts.analysis, {});
	certImg.value = opts.certSrc;
	codeName.value = opts.codeName;
	periodId.value = opts.periodId;
	testId.value = opts.testId;

	// 如果有证书图片地址
	if (opts.certSrc) {
		setTimeout(() => {
			getCertRef.value.open();
		}, 300);
	}
});
</script>

<style lang="scss" :scoped="false">
@import 'index.scss';
</style>

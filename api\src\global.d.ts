declare global {
  /** 返回的数据格式 */
  interface ResObj<T = any> {
    /** 返回码 */
    code: number;
  
    /** 返回数据 */
    data?: T;
    
    /** 描述信息 */
    msg: string;
  }
  
  /** 公共头部类型 */
  interface CustomHeaders extends Headers {
    /** 工厂ID */
    factoryid: number;
    
    /** 微信openid */
    wxopenid: string;

    /** 用户ID */
    userid: number;
    
    /** 工号 */
    login_jobnum: string;
  }

  /** 数字字符串 */
  type NumberString = number | string;
}

export {};

import { EntityManager } from 'typeorm';

import { DB_PRE } from '../constants';

/**
 * 批量插入记录
 * @param entityManager - TypeORM 的 EntityManager 实例
 * @param table 不带前缀的表名
 * @param columns 字段名
 * @param rows 数据（二维数组）
 */
export async function insertValues(
  entityManager: EntityManager,
  table: string,
  columns: string[],
  rows: any[][]
) {
  if (rows.length === 0) return;

  const placeholders = rows
    .map(() => `(${columns.map(() => '?').join(', ')})`)
    .join(', ');

  const flatValues = rows.flat();

  const sql = `INSERT INTO ${DB_PRE}${table} (${columns.join(', ')}) VALUES ${placeholders}`;

  // console.log('[SQL]', sql);
  // console.log('[Params]', flatValues);

  return await entityManager.query(sql, flatValues);
}

/**
 * 删除记录
 * @param entityManager - TypeORM 的 EntityManager 实例
 * @param table - 不带前缀的表名
 * @param whereClause - WHERE 条件（例如：'id = ? AND status = ?'）
 * @param params - 对应 WHERE 条件的参数数组（可选）
 * @returns 执行结果（通常是数据库返回的结果数组）
 */
export async function deleteValues(
  entityManager: EntityManager,
  table: string,
  whereClause: string,
  params: any[] = []
) {
  const sql = `DELETE FROM ${DB_PRE}${table} WHERE ${whereClause}`;
  return await entityManager.query(sql, params);
}

/**
 * 更新记录
 * @param entityManager - TypeORM 的 EntityManager 实例
 * @param table - 不带前缀的表名
 * @param updateColumns - 需要更新的字段名数组（例如：['name', 'status']）
 * @param updateValues - 与字段一一对应的更新值数组
 * @param whereClause - WHERE 条件字符串（例如：'id = ?'）
 * @param whereParams - WHERE 条件中 ? 对应的参数数组
 * @returns 执行结果（通常是数据库返回的结果数组）
 */
export async function updateValues(
  entityManager: EntityManager,
  table: string,
  updateColumns: string[],
  updateValues: any[],
  whereClause: string,
  whereParams: any[] = []
) {
  const setClause = updateColumns.map(col => `${col} = ?`).join(', ');
  const sql = `UPDATE ${DB_PRE}${table} SET ${setClause} WHERE ${whereClause}`;
  const params = [...updateValues, ...whereParams];

  // console.log('[SQL]', sql);
  // console.log('[Params]', params);

  return await entityManager.query(sql, params);
}

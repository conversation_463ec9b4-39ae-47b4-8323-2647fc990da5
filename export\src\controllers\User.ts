import { Controller, Get, Query, Headers } from '@nestjs/common';

import { UserQueryDto } from '../dto/User';
import { UserService } from '../services/User';
import { UserEntity } from '../entities/User';

@Controller('user')
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Get('list')
  async getList(@Query() query: UserQueryDto): Promise<ResObj<UserEntity[]>> {
    return this.userService.getList(query);
  }

  @Get('list-admin')
  async getListAdmin(@Query() query: UserQueryDto, @Headers() headers: CustomHeaders): Promise<ResObj<UserEntity[]>> {
    return this.userService.getListAdmin(query, headers);
  }
}

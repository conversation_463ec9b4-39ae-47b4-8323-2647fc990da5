@import '@/scss/mixins.scss';

.formal-model4{
	background-image: url('#{$imgHost}learn/model2_bg.jpg');
	
	.c_yellow{
		color: #f6bc50;
		font-weight: bold;
	}
	.q2-2{
		.btn-tit{
			height: 160rpx;
			text-align: center;
		}
		.tips{
			padding: 130rpx 60rpx;
			justify-content: space-between;
			align-items: flex-start;
			font-size: 40rpx;
		}
	}
	.q2-3{
		background-image: url('#{$imgHost}learn/question_btntit_bg.png');
		::v-deep .line0, ::v-deep .line1, ::v-deep .line2, ::v-deep .line3{
			&::after{
				content: '';
				position: absolute;
				left: 100%;
				height: 0;
				border-top: 6rpx dashed #fff;
				transform-origin: 0 0;
				width: 300rpx;
				transform: rotate(-72deg);
			}
		}
		::v-deep .line0{
			&::after{
				width: 500rpx;
				transform: rotate(80.5deg);
			}
		}
		::v-deep .item{
			padding: 0 14rpx;
		}
	}
	
	
	.q3-1{
		.btn-tit{
			height: 140rpx;
			text-align: center;
		}
	}
	.q3-3{
		::v-deep .pop{
			font-size: 28rpx;
			height: 66rpx;
		}
		::v-deep .p0{
			margin: -330rpx 0 0 -340rpx;
		}
		::v-deep .p1{
			margin: -330rpx 0 0 -100rpx;
		}
		::v-deep .p2{
			margin: -330rpx 0 0 130rpx;
		}
		::v-deep .p3{
			margin: -190rpx 0 0 -340rpx;
		}
		::v-deep .p4{
			margin: -190rpx 0 0 -100rpx;
		}
		::v-deep .p5{
			margin: -190rpx 0 0 130rpx;
		}
		::v-deep .p6{
			margin: -50rpx 0 0 -340rpx;
		}
		::v-deep .p7{
			margin: -50rpx 0 0 -100rpx;
		}
		::v-deep .p8{
			margin: -50rpx 0 0 130rpx;
		}
		::v-deep .p9{
			margin: 110rpx 0 0 -340rpx;
		}
		::v-deep .p10{
			margin: 110rpx 0 0 -100rpx;
		}
		::v-deep .p11{
			margin: 110rpx 0 0 130rpx;
		}
	}
	
	
	.q4-2{
		background-image: url('#{$imgHost}learn/model2_bg.jpg');
	}
	.q4-4-1{
		.formal-menu{
			.button{
				align-items: flex-start;
				text-align: left;
			}
		}
	}
	.q4-4-1-1{
		.tips{
			align-items: flex-start;
		}
	}
	.q4-4-1-2{
		::v-deep .p0{
			margin: -330rpx 0 0 -340rpx;
			font-size: 28rpx;
			height: 66rpx;
		}
		::v-deep .p1{
			margin: -330rpx 0 0 -100rpx;
			font-size: 28rpx;
			height: 66rpx;
		}
		::v-deep .p2{
			margin: -330rpx 0 0 130rpx;
			font-size: 28rpx;
			height: 66rpx;
		}
		::v-deep .p3{
			margin: -190rpx 0 0 -280rpx;
		}
		::v-deep .p4{
			margin: -190rpx 0 0 70rpx;
		}
		::v-deep .p5{
			margin: -50rpx 0 0 -280rpx;
		}
		::v-deep .p6{
			margin: -50rpx 0 0 70rpx;
		}
		::v-deep .p7{
			margin: 110rpx 0 0 -340rpx;
			font-size: 28rpx;
			height: 66rpx;
		}
		::v-deep .p8{
			margin: 110rpx 0 0 -100rpx;
			font-size: 28rpx;
			height: 66rpx;
		}
		::v-deep .p9{
			margin: 110rpx 0 0 130rpx;
			font-size: 28rpx;
			height: 66rpx;
		}
		
		::v-deep .cates{
			text-align: center;
			font-size: 42rpx;
			.cate1{
				background-image: url('#{$imgHost}learn/cate2.png');
				width: 375rpx;
				height: 311rpx;
			}
			.cate2{
				background-image: url('#{$imgHost}learn/cate3.png');
				width: 375rpx;
				height: 254rpx;
				left: 375rpx;
			}
		}
	}
	.q4-4-1-4{
		::v-deep .connect-line__title{
			font-size: 32rpx;
		}
		::v-deep .item{
			padding: 0 20rpx;
		}
		::v-deep .line0, ::v-deep .line1, ::v-deep .line2{
			&::after{
				content: '';
				position: absolute;
				left: 100%;
				height: 0;
				border-top: 6rpx dashed #fff;
				transform-origin: 0 0;
				width: 250rpx;
				transform: rotate(-73deg);
			}
		}
		::v-deep .line0{
			&::after{
				width: 520rpx;
				transform: rotate(81deg);
			}
		}
	}
	.q4-4-2-1{
		::v-deep .connect-line__title{
			font-size: 32rpx;
		}
		.small-submit{
			margin-top: 50rpx;
		}
		
		::v-deep .line0, ::v-deep .line1, ::v-deep .line2, ::v-deep .line3, ::v-deep .line4{
			&::after{
				content: '';
				position: absolute;
				left: 100%;
				height: 0;
				border-top: 6rpx dashed #fff;
				transform-origin: 0 0;
				width: 90rpx;
			}
		}
		::v-deep .line2{
			&::after{
				width: 400rpx;
				transform: rotate(78deg);
			}
		}
		::v-deep .line3, ::v-deep .line4{
			&::after{
				width: 200rpx;
				transform: rotate(-67deg);
			}
		}
	}
	.q4-4-2-4{
		::v-deep .connect-line__title{
			font-size: 30rpx;
		}
		
		::v-deep .line0, ::v-deep .line1{
			&::after{
				content: '';
				position: absolute;
				left: 100%;
				height: 0;
				border-top: 6rpx dashed #fff;
				transform-origin: 0 0;
				width: 400rpx;
				transform: rotate(78deg);
			}
		}
		::v-deep .line1{
			&::after{
				transform: rotate(-78deg);
			}
		}
	}
	.q4-4-3-1{
		::v-deep .pop{
			font-size: 28rpx;
			height: 66rpx;
		}
		::v-deep .p0{
			margin: -360rpx 0 0 -320rpx;
		}
		::v-deep .p1{
			margin: -270rpx 0 0 -80rpx;
		}
		::v-deep .p2{
			margin: -170rpx 0 0 -260rpx;
		}
		::v-deep .p3{
			margin: -60rpx 0 0 -130rpx;
		}
		::v-deep .p4{
			margin: 40rpx 0 0 -320rpx;
		}
		::v-deep .p5{
			margin: 140rpx 0 0 -80rpx;
		}
		::v-deep .cates{
			text-align: center;
			font-size: 42rpx;
			.cate1{
				background-image: url('#{$imgHost}learn/cate2.png');
				width: 375rpx;
				height: 311rpx;
			}
			.cate2{
				background-image: url('#{$imgHost}learn/cate3.png');
				width: 375rpx;
				height: 254rpx;
				left: 375rpx;
			}
		}
	}
	.q4-4-3-3{
		.btn-tit{
			font-size: 34rpx;
		}
	}
	
	
	.q5-1, .q5-4{
		.btn-tit{
			flex-direction: column;
			height: 130rpx;
		}
	}
}
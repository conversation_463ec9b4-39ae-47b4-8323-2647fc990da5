"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const typeorm_1 = require("@nestjs/typeorm");
const mysql_1 = require("./config/mysql");
const index_1 = require("./modules/index");
const Logger_1 = require("./middleware/Logger");
let AppModule = class AppModule {
    configure(consumer) {
        consumer.apply(Logger_1.LoggerMiddleware).forRoutes('*');
    }
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({
                isGlobal: true,
                envFilePath: `.env.${process.env.NODE_ENV || 'production'}`,
            }),
            typeorm_1.TypeOrmModule.forRoot({
                ...mysql_1.default[process.env.NODE_ENV || 'production'],
                entities: [__dirname + '/entities/*{.ts,.js}'],
                extra: {
                    connectionLimit: 100,
                    waitForConnections: true,
                    queueLimit: 0,
                },
            }),
            ...index_1.default,
        ],
    })
], AppModule);
//# sourceMappingURL=app.module.js.map
/**
 * <AUTHOR>
 * @description 百日打卡-打卡记录
 */

module.exports = (req, res) => {
	let json = {
		code: 0,
		data: {},
		msg: '',
	};
	
	const login_jobnum = safeString(req.headers.login_jobnum);
	const factoryid = parseInt(req.headers.factoryid);
	const period_id = parseInt(req.query.period_id);
	
	if (!factoryid || !period_id || !login_jobnum) {
		json.code = 201;
		json.msg = '缺少参数';
		res.json(json);
		return;
	}

	querySql(`select id, step_len, days from ${databasePre}checkin_period_conf where factory_id=${factoryid} and state=1 and id=${period_id} and start_time<=now();

	select id, type, chapter_id, days_continue, points, checkin_date, create_time from ${databasePre}checkin_log where factory_id=${factoryid} and period_id=${period_id} and jobnum='${login_jobnum}' order by id asc;
	
	select id, days_sum, checkin_type, days_type from ${databasePre}checkin_chapter where factory_id=${factoryid} and period_id=${period_id} and state=1 order by sort_num asc;
	
	select chapter_id, id from ${databasePre}checkin_days_conf where factory_id=${factoryid} and period_id=${period_id} order by chapter_id asc, sort_num asc;`, (err, result) => {
		if (err) {
			res.json(err);
			return;
		}

		const activity = result[0][0];
		if (!activity?.id) {
			json.code = 101;
			json.msg = '活动未开始';
			res.json(json);
			return;
		}

		const list = [];
		const now = Date.now();
		const today = new Date(timeFormatter(undefined, 'Y-m-d 00:00:00')).getTime();
		const days_sum = result[1].length;
		let startTime = today;
		let points_total = 0;

		let days_continue = 0;
		// 打过卡，则以第一次打卡时间为开始时间
		if (result[1].length) {
			const last = result[1][result[1].length - 1];
			if (last.checkin_date >= today - 24 * 3600 * 1000) {
				days_continue = last.days_continue;
			}
			startTime = new Date(timeFormatter(result[1][0].create_time.getTime(), 'Y/m/d 00:00:00')).getTime();
		}

		const daysConf = result[3];
		for (let index = 0; index < activity.days; index++) {
			const row = result[1][index];
			const obj = {};
			if (row) {
				// 已完成打卡
				obj.checked = true;
				obj.supplement = row.type === 1;
				obj.chapter_id = row.chapter_id;
				obj.log_id = row.id;
				obj.points = row.points;
				points_total += row.points;
			} else {
				// 未完成打卡

				let chapter_id = 0;
				let days = 0;
				let checkin_type = 0;
				let days_type = 0;
				for (let i = 0; i < result[2].length; i++) {
					const info = result[2][i];
					days += info.days_sum;
					if (index < days) {
						chapter_id = info.id;
						checkin_type = info.checkin_type;
						days_type = info.days_type;

						// 分别设置，返回天数配置ID
						if (days_type === 1 && daysConf.length) {
							const dayArr = daysConf.filter(o => o.chapter_id === chapter_id);							
							obj.days_conf_id = dayArr[index - days + info.days_sum].id;
						}
						break;
					}
				}
				obj.chapter_id = chapter_id;
				obj.checkin_type = checkin_type;
				obj.days_type = days_type;
				// obj.chapter_id = result[2][Math.floor(index / 10)]?.id;
				
				if (startTime + index * 3600 * 24 * 1000 <= now) {
					// 活动已经进行到这一天了
					obj.notcheck = true;
				} else {
					// 还未进行到这一天
					obj.locked = true;
				}
			}
			list.push(obj);
		}

		json.data = {
			step_len_list: activity.step_len?.split(',').map(v => parseInt(v)) || [],
			list,
			days_sum,
			points_total,
			days_continue,
		}
		res.json(json);
	})
}
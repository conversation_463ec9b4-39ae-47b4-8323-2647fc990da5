import sys
import os
import zipfile
import xml.etree.ElementTree as ET

def read_xlsx_content(file_path):
    """Read and display content from an XLSX file."""
    try:
        # Open the XLSX file as a ZIP archive
        with zipfile.ZipFile(file_path, 'r') as xlsx:
            # Get the sheet data
            sheet_data = xlsx.read('xl/worksheets/sheet1.xml')
            
            # Parse the XML
            root = ET.fromstring(sheet_data)
            
            # Define the namespace
            ns = {'s': 'http://schemas.openxmlformats.org/spreadsheetml/2006/main'}
            
            # Get the shared strings if available
            shared_strings = []
            if 'xl/sharedStrings.xml' in xlsx.namelist():
                ss_data = xlsx.read('xl/sharedStrings.xml')
                ss_root = ET.fromstring(ss_data)
                for si in ss_root.findall('.//s:si', ns):
                    text_elements = si.findall('.//s:t', ns)
                    if text_elements:
                        shared_strings.append(''.join(t.text if t.text else '' for t in text_elements))
                    else:
                        shared_strings.append('')
            
            # Process the rows
            rows = []
            for row in root.findall('.//s:row', ns):
                cells = []
                for cell in row.findall('.//s:c', ns):
                    cell_value = ''
                    v_element = cell.find('s:v', ns)
                    
                    if v_element is not None and v_element.text:
                        # Check if it's a shared string
                        if cell.get('t') == 's' and shared_strings:
                            try:
                                idx = int(v_element.text)
                                if 0 <= idx < len(shared_strings):
                                    cell_value = shared_strings[idx]
                            except (ValueError, IndexError):
                                cell_value = v_element.text
                        else:
                            cell_value = v_element.text
                    
                    cells.append(cell_value)
                
                if cells:  # Only add non-empty rows
                    rows.append(cells)
            
            # Print the content
            print(f"File: {os.path.basename(file_path)}")
            print(f"Total rows: {len(rows)}")
            
            # Print the first few rows
            max_rows_to_show = min(20, len(rows))
            for i, row in enumerate(rows[:max_rows_to_show]):
                # Format the row for display
                formatted_row = '\t'.join(str(cell) for cell in row)
                print(f"Row {i+1}: {formatted_row}")
            
            if len(rows) > max_rows_to_show:
                print(f"... and {len(rows) - max_rows_to_show} more rows")
    
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
        read_xlsx_content(file_path)
    else:
        print("Please provide the path to an Excel file")

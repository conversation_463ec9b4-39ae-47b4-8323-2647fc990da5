
/**
 * @description 时间格式化
 * @param ts 时间戳
 * @param fms 格式化类型，默认Y-M-D H:I:S
 */
export const timeFmt = (ts = Date.now(), fmt = 'Y-M-D H:I:S') => {
  const time = new Date(Number(ts));
  const year = time.getFullYear();
  let month: NumberString = time.getMonth() + 1;
  let day: NumberString = time.getDate();
  let hour: NumberString = time.getHours();
  let minute: NumberString = time.getMinutes();
  let second: NumberString = time.getSeconds();
  const microSecond: NumberString = ts.toString().substring(ts.toString().length - 3);
  month = month < 10 ? '0' + month : month;
  day = day < 10 ? '0' + day : day;
  hour = hour < 10 ? '0' + hour : hour;
  minute = minute < 10 ? '0' + minute : minute;
  second = second < 10 ? '0' + second : second;

  return fmt
    .replace(/Y/g, year.toString())
    .replace(/M/g, month.toString())
    .replace(/D/g, day.toString())
    .replace(/H/g, hour.toString())
    .replace(/I/g, minute.toString())
    .replace(/S/g, second.toString())
    .replace(/ms/g, microSecond);
};

/**
 * @description 获取客户端ip地址
 */
export function getIp(request) {
  return new Promise((resolve) => {
    if (process.env.NODE_ENV === 'local') {
      resolve('localhost');
    } else {
      const ip = (request.headers['x-forwarded-for'] || '').split(',')[0] || request.ip;
      resolve(ip?.replace(/^.*:/, '')); // 去掉开头的::ffff:
    }
  });
}
<template>
	<view
		class="formal-model1 swiper-question-wrap"
		:style="{ paddingTop: user.statusBarHeight }">
		<uni-icons
			type="back"
			class="nav-back"
			@click="goBack()"></uni-icons>

		<!-- 题目列表 -->
		<Question
			v-if="hadMounted"
			class="swiper-question"
			short
			:list="questionList"
			@submit="submit" />

		<!-- 介绍页 -->
		<uni-popup
			ref="introRef"
			type="center">
			<view class="popup-fullscreen intro">
				<view class="intro-ticket">
					<view class="dots">
						<text
							class="dot"
							v-for="v in 14"
							:key="v"></text>
					</view>
					<view class="button text">在正式学习前，请先测试心理健康的知识水平。</view>
				</view>
				<view
					class="button plain round small-submit"
					@click="introRef.close()">
					我知道了
				</view>
			</view>
		</uni-popup>

		<!-- 结果页 -->
		<uni-popup
			ref="resultRef"
			type="center">
			<view class="popup-fullscreen intro">
				<view class="intro-ticket">
					<view class="dots">
						<text
							class="dot"
							v-for="v in 14"
							:key="v"></text>
					</view>
					<!-- <view class="button text">共答对：{{ rightNums }}/{{ questionList.length }}，{{ resultTips }}</view> -->
					<view class="button text">{{ resultTips }}</view>
				</view>
				<view
					class="button plain round small-submit"
					@click="nextJiexi()"
					v-if="type">
					查看解析
				</view>
				<view
					class="button plain round small-submit"
					@click="goBack()"
					v-else>
					继 续
				</view>
			</view>
		</uni-popup>

		<!-- 解析 -->
		<uni-popup
			ref="jiexiRef"
			type="center">
			<view
				class="popup-fullscreen swiper-question-wrap jx"
				v-if="jiexiArr[jiexiIdx]">
				<view class="box">
					<view class="subtitle">{{ jiexiArr[jiexiIdx].topic }}</view>
					<view
						class="answer-text"
						v-for="(val, serial) in jiexiArr[jiexiIdx].chossNum"
						:key="serial">
						<view class="answer-serial">{{ answerMapList[serial] }}:</view>
						<view>{{ jiexiArr[jiexiIdx][answerMapList[serial]] }}</view>
						<text
							class="iconfont i-check"
							v-if="answerMapList[serial] === jiexiArr[jiexiIdx].answer"></text>
					</view>
					<view class="subtit">解析</view>
					<view class="info">{{ jiexiArr[jiexiIdx]._jiexi }}</view>

					<view
						class="button small-submit"
						@click="nextJiexi()">
						{{ jiexiIdx < jiexiArr.length - 1 ? '下一页' : '完 成' }}
					</view>
				</view>
			</view>
		</uni-popup>

		<!-- 完成进度 弹窗 -->
		<Process
			ref="processRef"
			modelName="后测"
			:total="1"
			:step="1"
			:jobNum="user.loginJobnum"
			is-complete
			@complete="goBack()" />
	</view>
</template>

<script setup>
import { onLoad } from '@dcloudio/uni-app';
import { ref, onMounted } from 'vue';
import { storeToRefs } from 'pinia';
import { useUserStore } from '@/stores/user';
import formatQuestion from '@/utils/format-formal-question.js';

import Question from '@/components/Question/index.vue';
import Process from '@/components/Process/index.vue';
import { goBack, postError } from '@/utils/tools.js';

const { user } = storeToRefs(useUserStore());

const answerMapList = answerMap;
const type = ref(0); // 0前测、1后测
const questionList = ref([]);
const hadMounted = ref(false);
const rightNums = ref(0);
const resultTips = ref('您已完成前测，请开始您的学习！');
const jiexiIdx = ref(-1);
const jiexiArr = ref([]);

const introRef = ref();
const resultRef = ref();
const jiexiRef = ref();
const processRef = ref();

async function getQuestionList() {
	const list = await ajax('question/model1.json');
	questionList.value = formatQuestion(list, (o) => {
		o._jiexi = o.jiexi;
		delete o.jiexi;
	});
	jiexiArr.value = questionList.value.filter((o) => o._jiexi);
}
getQuestionList();

async function submit(arr) {
	let rights = 0;
	let op1 = [];
	let op2 = [];
	arr.forEach((o, i) => {
		if (o.correct) rights++;

		if (i < 5) {
			op1.push(o.checked.join(''));
		} else {
			op2.push(o.checked.join(''));
		}
	});

	// 提交结果
	const { code } = await ajax({
		// url: 'api/api.php?a=putModule' + (type.value ? '7' : '1'),
		url: 'learn/submit',
		method: 'post',
		showLoading: true,
		data: {
			module: type.value ? 7 : 1,
			jobnum: user.value.loginJobnum,
			op1: op1.join('-'),
			op2: op2.join('-')
		}
	});

	// 异常处理
	if (code) {
		postError()
			.then(() => {
				submit(arr);
			})
			.catch(() => {});
	} else {
		rightNums.value = rights;
		resultRef.value.open();
	}
}

function nextJiexi() {
	if (jiexiIdx.value > -1) {
		if (jiexiIdx.value >= jiexiArr.value.length - 1) {
			if (type.value) {
				// 后测
				processRef.value.open();
			} else {
				// 前测
				goBack();
			}
			return;
		}
		jiexiRef.value.close();
		setTimeout(() => {
			jiexiIdx.value++;
			jiexiRef.value.open();
		}, 350);
	} else {
		jiexiIdx.value++;
		resultRef.value.close();
		jiexiRef.value.open();
		hadMounted.value = false;
	}
}

onLoad((opts) => {
	type.value = opts.type;
});
onMounted(() => {
	if (type.value) {
		// 后测
		hadMounted.value = true;
		resultTips.value = '您已完成后测，点击下方按钮可查看解析';
	} else {
		// 前测
		introRef.value.open();
		setTimeout(() => {
			hadMounted.value = true;
		}, 500);
	}
});
</script>

<style lang="scss">
@import 'index.scss';
</style>

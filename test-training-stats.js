/**
 * 测试志愿者培训统计数据导出功能
 */
const axios = require('axios');
const fs = require('fs');
const path = require('path');

// 设置API基础URL
const baseURL = 'http://localhost:3000'; // 根据实际情况修改

async function testTrainingStats() {
  try {
    console.log('开始测试志愿者培训统计数据导出功能...');

    // 发送请求
    const response = await axios.get(`${baseURL}/training/stats-admin`, {
      responseType: 'arraybuffer' // 因为返回的是Excel文件
    });

    // 保存Excel文件
    const fileName = `志愿者培训统计数据-超管-${new Date().toISOString().slice(0, 10)}.xlsx`;
    fs.writeFileSync(path.join(__dirname, fileName), response.data);

    console.log(`测试成功! Excel文件已保存为: ${fileName}`);
  } catch (error) {
    console.error('测试失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data.toString());
    }
  }
}

// 执行测试
testTrainingStats();

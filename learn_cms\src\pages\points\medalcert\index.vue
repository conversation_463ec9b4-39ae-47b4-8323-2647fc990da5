<template>
  <div class="points-medalcert ui-layout-col">
    <afc-filter-bar
      class="mb10"
      size="small"
      :field="searchField"
      :init-filter="searchForm"
      @valueChange="searchForm = $event; init()" />

    <div class="flex1">
      <x-table
        :data-set="dataSet"
        :load-list-failed="loadFailed"
        @reload="init"
        @size-change="sizeChange"
        @current-change="currentChange">
        <template
          v-for="obj in tableCols">
          <el-table-column
            v-if="obj.prop === 'jobnum'"
            :key="obj.prop"
            v-bind="obj" />
          <el-table-column
            v-else
            :key="obj.prop"
            v-bind="obj">
            <template slot-scope="scope">
              <template v-if="!scope.row[obj.prop]?.length">无</template>
              <div
                v-for="item in scope.row[obj.prop]"
                v-else
                :key="item.id"
                class="dib vat ml5 mr5">
                <el-image
                  style="width: 80px; height: 80px"
                  fit="contain"
                  :src="imgHost + item.cover"
                  :preview-src-list="scope.row[obj.prop].map(o => imgHost + o.cover)" />
                <div class="tac c_c f12">{{ item.name }} {{ item.type === 'cert' ? '证书' : '勋章' }}</div>
              </div>
            </template>
          </el-table-column>
        </template>
      </x-table>
    </div>
  </div>
</template>

<script>
/**
 * @description 积分与成就 - 勋章与证书
 */
import afcFilterBar from 'afc-filter-bar-vue2';
import { cloneDeep } from 'lodash';

import {
  searchField,
  tableCols,
} from './table-cols';

import xTable from '@/components/x-table';
import mixinFactoryList from '@/function/mixin-factory-list';

export default {
  components: {
    afcFilterBar,
    xTable,
  },
  mixins: [mixinFactoryList],
  data() {
    return {
      dataSet: {},
      searchForm: {},
    };
  },
  computed: {
    tableCols() {
      const arr = cloneDeep(tableCols);
      // if (this.user.user_group) {
      //   // 工厂管理员，不能筛选工厂
      //   arr.splice(1, 1);
      // }
      return arr;
    },
    searchField() {
      const arr = cloneDeep(searchField);

      if (this.user.user_group) {
        // 工厂管理员，不能筛选工厂
        arr.splice(0, 1);
      } else {
        arr[0].formOpts.optsList = this.factoryList;
      }

      arr[arr.length - 1].formOpts.events.click = () => {
        this.exportExcel();
      };
      return arr;
    },
  },
  created() {
    // 工厂管理员，手动初始化
    if (this.user.user_group) {
      this.init();
    }
  },
  methods: {
    async init() {
      if (!this.searchForm.factory_id) return;
      this.getList();
    },
    handelFilter() {
      this.dataSet.page_num = 1;
      this.getList();
    },

    // 获取列表数据
    async getList() {
      const pager = cloneDeep(this.dataSet);
      delete pager.list;
      delete pager.total;

      const search = cloneDeep(this.searchForm);
      delete search.add;
      delete search.export;
      if (search.create_time?.length) {
        search.start_time = search.create_time[0];
        search.end_time = search.create_time[1];
      }
      delete search.create_time;

      const { code, data } = await ajax({
        url: 'points/medalcert',
        data: {
          ...pager,
          ...search,
        },
      });
      if (code) {
        this.loadFailed = true;
        return;
      }
      this.loadFailed = false;

      this.dataSet = data;
    },
    sizeChange(num) {
      this.$set(this.dataSet, 'page_size', num);
      this.dataSet.page_num = 1;
      this.getList();
    },
    currentChange(num) {
      this.$set(this.dataSet, 'page_num', num);
      this.getList();
    },

    // 导出
    exportExcel() {
      const search = cloneDeep(this.searchForm);
      delete search.export;
      if (search.create_time?.length) {
        search.start_time = search.create_time[0];
        search.end_time = search.create_time[1];
      }
      delete search.create_time;
      
      this.download({
        url: '/export/points/logs',
        data: {
          ...search,
          factory_name: this.user.user_group ? this.user.factory_name : this.factoryList.find(o => o.value === this.searchForm.factory_id).label,
        },
      });
    },
  },
};
</script>

<style lang="scss">
  @import './index';
</style>

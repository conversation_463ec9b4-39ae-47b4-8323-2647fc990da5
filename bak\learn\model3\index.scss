@import '@/scss/mixins.scss';

.formal-model3{
	background-image: url('#{$imgHost}learn/model2_bg.jpg');

	.formal-menu{
		margin: 20rpx auto 80rpx;
		.title{
			height: 180rpx;
			padding: 0 60rpx;
		}
	}
	.img{
		display: block;
		margin: 200rpx auto 50rpx;
	}
	.curve1{
		width: 650rpx;
		height: 277rpx;
		background: url('#{$imgHost}learn/curve1.png') 0 0/cover;
	}
	.curve2{
		width: 640rpx;
		height: 312rpx;
		background: url('#{$imgHost}learn/curve2.png') 0 0/cover;
		margin-top: 80rpx;
	}
	.curve3{
		width: 595rpx;
		height: 469rpx;
		background: url('#{$imgHost}learn/curve3.png') 0 0/cover;
		margin: 50rpx auto 0;
	}
	.curve4{
		width: 641rpx;
		height: 397rpx;
		background: url('#{$imgHost}learn/curve4.png') 0 0/cover;
		margin: 0 auto;
	}
	
	.question{
		background-image: url('#{$imgHost}learn/question_intro_bg.png');
	}
	.q2-2{
		.btn-tit{
			margin-bottom: 100rpx;
		}
		.arrows{
			text-align: center;
			.button{
				width: 310rpx;
				padding: 20rpx;
				height: auto;
				display: inline-block;
				text-align: left;
				margin: 0 10rpx;
			}
			.c_yellow{
				color: #f6bc50;
			}
			.c_blue{
				color: #7bebff;
			}
		}
		.arrow{
			display: flex;
			justify-content: center;
			&::before, &::after{
				content: "\e632";
				display: block;
				font-family: uniicons;
				color: $c_main;
				margin: 30rpx 135rpx 24rpx;
				font-size: 60rpx;
				opacity: .8;
			}
		}
		.arrow1{
			&:before{
				transform: translateX(20rpx) rotate(30deg);
			}
			&:after{
				transform: translateX(-20rpx) rotate(-30deg);
			}
		}
	}
	.q2-4{
		::v-deep .title{
			height: 190rpx;
		}
		::v-deep .p0{
			margin: -330rpx 0 0 -340rpx;
			font-size: 28rpx;
			height: 66rpx;
		}
		::v-deep .p1{
			margin: -330rpx 0 0 -100rpx;
			font-size: 28rpx;
			height: 66rpx;
		}
		::v-deep .p2{
			margin: -330rpx 0 0 130rpx;
			font-size: 28rpx;
			height: 66rpx;
		}
		::v-deep .p3{
			margin: -190rpx 0 0 -280rpx;
		}
		::v-deep .p4{
			margin: -190rpx 0 0 70rpx;
		}
		::v-deep .p5{
			margin: -50rpx 0 0 -280rpx;
		}
		::v-deep .p6{
			margin: -50rpx 0 0 70rpx;
		}
		::v-deep .p7{
			margin: 110rpx 0 0 -340rpx;
			font-size: 28rpx;
			height: 66rpx;
		}
		::v-deep .p8{
			margin: 110rpx 0 0 -100rpx;
			font-size: 28rpx;
			height: 66rpx;
		}
		::v-deep .p9{
			margin: 110rpx 0 0 130rpx;
			font-size: 28rpx;
			height: 66rpx;
		}
	}
	
	
	.q3-2{
		.title{
			font-size: 68rpx;
		}
		.subtit{
			width: 380rpx;
			height: 220rpx;
			padding: 0 30rpx;
			text-align: left;
			margin: 20rpx auto 30rpx;
		}
	}
	.q3-5{
		.title{
			margin-bottom: 30rpx;
			font-size: 56rpx;
		}
		.title2{
			font-size: 74rpx;
			text-align: center;
			letter-spacing: 4rpx;
		}
		.draw-canvas{
			position: absolute;
			left: 0;
			top: 50%;
			margin-top: -200rpx;
		}
	}
	
	
	.q4-1{
		background-image: url('#{$imgHost}learn/question_btntit_bg.png');
		::v-deep .line0, ::v-deep .line1, ::v-deep .line2, ::v-deep .line3{
			&::after{
				content: '';
				position: absolute;
				left: 100%;
				height: 0;
				border-top: 6rpx dashed #fff;
				transform-origin: 0 0;
				width: 250rpx;
				transform: rotate(-67deg);
			}
		}
		::v-deep .line0{
			&::after{
				width: 580rpx;
				transform: rotate(82deg);
			}
		}
		
		.connect-line{
			::v-deep .connect-line__title{
				:nth-child(1){
					font-size: 46rpx !important;
				}
				:nth-child(2){
					font-size: 34rpx !important;
				}
			}
		}
		::v-deep .items{
			.item{
				font-size: 48rpx !important;
				&:nth-child(even){
					font-size: 80rpx !important;
				}
			}
		}
	}
	.q4-4{
		.intro-box{
			padding: 0 20rpx;
			text-align: center;
			font-size: 32rpx;
			height: 740rpx;
		}
		.arrow{
			display: flex;
			justify-content: center;
			margin: 8rpx 0;
			&::before{
				content: "\e632";
				display: block;
				font-family: uniicons;
				color: $c_main;
				font-size: 56rpx;
				opacity: .8;
			}
		}
	}
	.q4-5{
		background-image: url('#{$imgHost}learn/question_btntit_bg.png');
		::v-deep .line0, ::v-deep .line1, ::v-deep .line2, ::v-deep .line3, ::v-deep .line4{
			&::after{
				content: '';
				position: absolute;
				left: 100%;
				height: 0;
				border-top: 6rpx dashed #fff;
				transform-origin: 0 0;
				width: 150rpx;
				transform: rotate(57deg);
			}
		}
		::v-deep .line0{
			&::after{
				width: 500rpx;
				transform: rotate(80.5deg);
			}
		}
		::v-deep .line3{
			&::after{
				width: 250rpx;
				transform: rotate(-72.5deg);
			}
		}
		::v-deep .line4{
			&::after{
				width: 500rpx;
				transform: rotate(-81deg);
			}
		}
		
		.connect-line{
			::v-deep .connect-line__title{
				:nth-child(1){
					font-size: 46rpx !important;
					margin-bottom: 20rpx;
				}
				:nth-child(2){
					font-size: 34rpx !important;
				}
			}
		}
		::v-deep .items{
			min-height: 620rpx;
			margin-top: 50rpx;
		}
		::v-deep .item{
			height: 80rpx;
		}
	}
	
	
	.q5-3{
		::v-deep .title{
			text-align: center;
			height: 180rpx;
		}
		::v-deep .p0{
			margin: -330rpx 0 0 -340rpx;
			font-size: 28rpx;
			height: 66rpx;
		}
		::v-deep .p1{
			margin: -330rpx 0 0 -100rpx;
			font-size: 28rpx;
			height: 66rpx;
		}
		::v-deep .p2{
			margin: -330rpx 0 0 130rpx;
			font-size: 28rpx;
			height: 66rpx;
		}
		::v-deep .p3{
			margin: -190rpx 0 0 -280rpx;
		}
		::v-deep .p4{
			margin: -190rpx 0 0 70rpx;
		}
		::v-deep .p5{
			margin: -50rpx 0 0 -280rpx;
		}
		::v-deep .p6{
			margin: -50rpx 0 0 70rpx;
		}
		::v-deep .p7{
			margin: 110rpx 0 0 -280rpx;
		}
		::v-deep .p8{
			margin: 110rpx 0 0 70rpx;
		}
		
		::v-deep .cate{
			text-align: center;
			padding-top: 50rpx;
			font-size: 46rpx;
		}
		::v-deep .cates{
			.cate1{
				width: 250rpx;
			}
			.cate2{
				width: 251rpx;
				left: 250rpx;
			}
			.cate3{
				width: 250rpx;
				left: 501rpx;
			}
		}
		::v-deep .title{
			height: 190rpx;
		}
		
		::v-deep .p0{
			margin: -330rpx 0 0 -340rpx;
			font-size: 28rpx;
			height: 66rpx;
		}
		::v-deep .p1{
			margin: -330rpx 0 0 -100rpx;
			font-size: 28rpx;
			height: 66rpx;
		}
		::v-deep .p2{
			margin: -330rpx 0 0 130rpx;
			font-size: 28rpx;
			height: 66rpx;
		}
		::v-deep .p3{
			margin: -190rpx 0 0 -280rpx;
		}
		::v-deep .p4{
			margin: -190rpx 0 0 70rpx;
		}
		::v-deep .p5{
			margin: -50rpx 0 0 -280rpx;
		}
		::v-deep .p6{
			margin: -50rpx 0 0 70rpx;
		}
		::v-deep .p7{
			margin: 110rpx 0 0 -340rpx;
			font-size: 28rpx;
			height: 66rpx;
		}
		::v-deep .p8{
			margin: 110rpx 0 0 130rpx;
			font-size: 28rpx;
			height: 66rpx;
		}
	}
}
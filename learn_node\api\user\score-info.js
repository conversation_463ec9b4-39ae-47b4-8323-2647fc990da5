/**
 * @description 用户积分数据
 */

module.exports = (req, res) => {
	let json = {
		code: 0,
		data: {},
		msg: '',
	};
	
	const factoryid = parseInt(req.headers.factoryid);
	const login_jobnum = safeString(req.headers.login_jobnum);
	
	if (!factoryid || !login_jobnum) {
		json.code = 201;
		json.msg = '缺少参数';
		res.json(json);
		return;
	}
	
	querySql(`select sum(ifnull(module1, 0) + ifnull(module2, 0) + ifnull(module3, 0) + ifnull(module4, 0) + ifnull(module5, 0) + ifnull(module6, 0) + ifnull(module7, 0)) as score from ${databasePre}allpoint_data where factory_id='${factoryid}' and jobnum='${login_jobnum}';
	
	select ifnull(sum(points_total), 0) as score, count(id) as times from ${databasePre}checkin_rank where factory_id='${factoryid}' and jobnum='${login_jobnum}';
	
	select ifnull(sum(points), 0) as score, count(id) as times from ${databasePre}service_log where factory_id='${factoryid}' and jobnum='${login_jobnum}' and is_del=0 and check_status<2;`, (err, result) => {
		if (err) {
			res.json(err);
			return;
		}

		json.data = {
			learn: {
				score: result[0][0]?.score || 0,
			},
			checkin: result[1][0],
			service: result[2][0],
		};

		res.json(json);
	})
}
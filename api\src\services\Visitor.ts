import { Injectable } from '@nestjs/common';
import { InjectEntityManager } from '@nestjs/typeorm';
import { EntityManager } from 'typeorm';

import { DB_PRE, DB_TRANSACTION_ERROR_CODE } from '../constants';
import { CustomHeadersDto } from '../dto';
import { VisitorApplySubmitDto } from '../dto/Visitor';
import { resErr, resOk, insertValues, updateValues } from '../utils';

@Injectable()
export class VisitorService {
  constructor(@InjectEntityManager() private readonly entityManager: EntityManager) {}
  
  /** 小课堂-获取题目 */
  async testInfo(headers: CustomHeadersDto): Promise<ResObj> {
    const topic = await this.entityManager.query(`select * from ${DB_PRE}visitor_test_topic where is_del=0 order by sort_num asc, score asc;`);
    if (!topic?.length) {
      return resErr(202, '未配置题目');
    }

    const {
      login_jobnum,
      factoryid,
    } = headers;

    const list = topic.map(o => {
      delete o.is_del;
      delete o.sort_num;
      return o;
    });

    // 插入获取记录
    await insertValues(
      this.entityManager,
      'visitor_getlog',
      [
        'factory_id',
        'jobnum',
        'create_time'
      ],
      [
        [
          factoryid,
          login_jobnum,
          new Date()
        ]
      ]
    );
    return resOk(list);
  }

  /** 小课堂-提交 */
  async testSubmit(body: VisitorApplySubmitDto, headers: CustomHeadersDto): Promise<ResObj> {
    const { answer_list, type } = body;
    const {
      login_jobnum,
      factoryid,
    } = headers;
    
    const topics = await this.entityManager.query(`select id, title, answer, score, sort_num from ${DB_PRE}visitor_test_topic order by sort_num asc;`);
    if (answer_list.length !== topics.length) {
      return resErr(202, '题目数量不对');
    }

    const testLogValues = [];
    const now = new Date();
    let total_score = 0;
    const scoreArr = [];
    answer_list.forEach(answer => {
      const info = topics.find(o => o.id === answer.id);
      let score = 0;
      if (answer.value === info.answer) {
        score = info.score;
      }
      scoreArr.push(score);
      total_score += score;
      testLogValues.push([
        factoryid,
        login_jobnum,
        answer.id,
        info.sort_num,
        answer.value,
        score,
        now,
        info.title,
        type
      ]);
    });
    
    return this.entityManager.transaction(async (manager) => {
      // 插入记录
      await insertValues(
        manager,
        'visitor_test_log',
        [
          'factory_id',
          'jobnum',
          'topic_id',
          'sort_num',
          'answer',
          'score',
          'create_time',
          'topic_title',
          'type'
        ],
        testLogValues,
      );

      // 插入结果
      await insertValues(
        manager,
        'visitor_data',
        [
          'factory_id',
          'jobnum',
          'sum1',
          'sum2',
          'option_str1',
          'option_str2',
          'create_time'
        ],
        [
          [
            factoryid,
            login_jobnum,
            type ? null : total_score,
            type ? total_score : null,
            type ? '' : scoreArr.join(' | '),
            type ? scoreArr.join(' | ') : '',
            now
          ]
        ]
      );

      return resOk(total_score);
    }).catch(err => {
      return resErr(DB_TRANSACTION_ERROR_CODE, err.message);
    });
  }
}

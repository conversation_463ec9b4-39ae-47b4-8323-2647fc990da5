/**
 * <AUTHOR>
 * @description 百日打卡-提交测试问卷
 */

module.exports = (req, res) => {
	let json = {
		code: 0,
		data: {},
		msg: '',
	};
	
	const login_jobnum = safeString(req.headers.login_jobnum);
	const factoryid = parseInt(req.headers.factoryid);
	const period_id = parseInt(req.body.period_id);
	const test_id = parseInt(req.body.test_id);
	const answer_list = req.body.answer_list;
	
	if (!factoryid || !login_jobnum || !period_id || !test_id || !answer_list?.length) {
		json.code = 201;
		json.msg = '缺少参数';
		res.json(json);
		return;
	}

	querySql(`select * from ${databasePre}checkin_test_conf where factory_id=${factoryid} and id=${test_id} and period_id=${period_id} and state=1;
	
	select count(1) as count from ${databasePre}checkin_test_log where factory_id=${factoryid} and jobnum='${login_jobnum}' and test_id=${test_id} and period_id=${period_id};
	
	select title, type, dimension, is_scored from ${databasePre}checkin_test_topic where test_id=${test_id} order by sort_num asc;
	
	select code_name from ${databasePre}checkin_period_conf where id=${period_id};`, (err, result, conn) => {
		if (err) {
			res.json(err);
			return;
		}

		const testInfo = result[0][0];
		if (!testInfo?.title) {
			json.code = 202;
			json.msg = '问卷不存在';
			res.json(json);
			return;
		}

		if (result[1][0].count) {
			json.code = 203;
			json.msg = '请勿重复提交';
			res.json(json);
			return;
		}

		const list = result[2];
		if (list.length !== answer_list.length) {
			json.code = 204;
			json.msg = '题目数量不对';
			res.json(json);
			return;
		}

		const dimension = {}; // 维度
		const values = [];
		list.forEach((o, i) => {
			const answer = answer_list[i];

			// 如果参与计分
			if (o.is_scored) {
				if (!dimension[o.dimension]) {
					dimension[o.dimension] = [];
				}
				dimension[o.dimension].push(answer.score);
			}

			values.push(`(${factoryid}, '${login_jobnum}', ${period_id}, ${test_id}, ${answer.topic_id}, '${o.dimension || ''}', '${safeString(answer.value)}', ${answer.score || answer.score === 0 ? answer.score : null}, ${o.is_scored}, now(), '${testInfo.title}', '${o.title}', '${o.type}', ${testInfo.day_num}, ${testInfo.type})`);
		})

		const sqlArr = [
			`insert into ${databasePre}checkin_test_log(factory_id, jobnum, period_id, test_id, topic_id, dimension, answer, score, is_scored, create_time, test_title, topic_title, topic_type, test_day_num, test_type) values${values.join()};`
		];

		// 有证书颁发证书
		// if (testInfo.cert_src) {
		// 	sqlArr.push(`insert into ${databasePre}cert(factory_id, jobnum, type, cover, checkin_period_id, issue_time) values(${factoryid}, '${login_jobnum}', 1, '${testInfo.cert_src}', ${period_id}, now());`);
		// }
		
		execTrans(sqlArr, (err, re) => {
			if (err) {
				json.code = 205;
				json.msg = err.message;
				res.json(json);
				return;
			}

			const dimension_list = [];
			const entries = Object.entries(dimension);
			entries.forEach(([label, arr]) => {
				const obj = {
					label,
					// value: (20 * arr.reduce((a, b) => a + b) / arr.length).toFixed(1)
					value: eval(testInfo.score_formula?.replace(/\{list\}/g, 'arr'))
				}
				dimension_list.push(obj);
			})
			json.data = {
				cert_src: testInfo.cert_src, // 证书图片地址（为空不发证书）
				dimension_list, // 纬度得分
				code_name: result[3][0]?.code_name,
				// 纬度解析
				analysis: {
					recap_json: testInfo.recap_json,
					score_json: testInfo.score_json,
					intro_json: testInfo.intro_json,
					suggest_json: testInfo.suggest_json,
					score_tips: testInfo.score_tips,
					recap_prev: testInfo.recap_prev,
					score_split: testInfo.score_split,
				}
			}

			res.json(json);
		})
	})
}
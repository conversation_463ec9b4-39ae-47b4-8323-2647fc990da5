@import '@/scss/mixins.scss';

.formal-intro {
	background: linear-gradient(0deg, #fff, #f0fafc);
	// display: flex;
	// flex-direction: column;

	.title {
		margin-top: 100rpx;
		font-size: 68rpx;
		text-align: center;
		letter-spacing: 10rpx;
	}
	.img {
		position: relative;
		.bg {
			background: url('#{$imgHost}learn/intro_bg.png') 0 0/100% 100%;
			margin: 0 auto;
			width: 625rpx;
			height: 446rpx;
		}
		.medal {
			background: url('#{$imgHost}learn/medal1.png') 0 0/100% 100%;
			width: 85rpx;
			height: 85rpx;
			position: absolute;
			top: 60%;
			left: 12.5%;
			transform: translateY(-50%);
		}
		.medal2 {
			background-image: url('#{$imgHost}learn/medal2.png');
			width: 110rpx;
			height: 110rpx;
			left: 24.5%;
		}
		.medal3 {
			background-image: url('#{$imgHost}learn/medal3.png');
			width: 110rpx;
			height: 110rpx;
			left: 61.5%;
		}
		.medal4 {
			background-image: url('#{$imgHost}learn/medal4.png');
			left: 77.5%;
		}
	}
	.tips {
		width: 360rpx;
		margin: 40rpx auto 0;
		font-size: 32rpx;
		line-height: 1.7;
	}
}

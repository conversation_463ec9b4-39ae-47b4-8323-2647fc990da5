<template>
	<view class="exclusive-checkin-task-audio">
		<Navback />

		<view
			v-if="info.task_type === 1"
			class="tabs df s-c"
			:class="['a' + curTabIdx]">
			<view
				class="item f32 df c-c"
				:class="{ active: curTabIdx === i }"
				v-for="(v, i) in tabList"
				@click="switchTab(i)"
				:key="i">
				{{ v }}
			</view>
		</view>

		<view class="h1 fwb tac por">Day {{ (info.chapter_diff_days || 0) + info.sort_num }}</view>
		<view class="h2 tac por">
			{{ info.task_type === 1 ? (curTabIdx > 0 ? info.audio_practice_name : info.audio_intro_name) : `日常任务${NumberCN[info.task_list?.length - 1]}选一` }}
		</view>

		<!-- 音频练习 -->
		<template v-if="info.task_type === 1">
			<view v-show="curTabIdx === 0">
				<image
					:src="realImgSrc(info.audio_intro_img)"
					v-if="info.audio_intro_img"
					mode="widthFix"
					class="practice-img"></image>
			</view>
			<view
				v-show="curTabIdx === 1"
				class="play-btn"
				:class="{ playing: videoState.isPlay, disabled: videoState[1].hadPlayed === false }">
				<view class="wave wave1"></view>
				<view class="wave wave2"></view>
				<view class="wave wave3"></view>
				<view class="wave wave4"></view>
				<view class="wave wave5"></view>
				<uni-icons
					v-if="videoState[1].done"
					type="reload"
					class="iconfont reload"
					@click="playVideo(1, 'reload')"></uni-icons>
				<view
					v-else
					class="iconfont"
					:class="[videoState.isPlay ? 'i-pause' : 'i-play']"
					@click="playVideo(1)"></view>
			</view>
		</template>

		<!-- 日常任务 -->
		<template v-else>
			<view
				class="item-list ui-box-radius"
				:class="{ active: chooseIdx === i }"
				@click="chooseIdx = i"
				v-for="(o, i) in info.task_list"
				:key="i">
				<view class="fwb title">任务{{ i + 1 }}：{{ o.title }}</view>
				<view class="f32">{{ o.intro }}</view>
			</view>
		</template>

		<view class="checkin-submit">
			<view
				class="incomplete-tips fwb f48 tac"
				:class="[info.task_type ? 'tab' + curTabIdx : '']"
				v-if="chooseIdx === null && !videoState[1].done">
				<!-- 音频练习 -->
				<template v-if="info.task_type === 1">
					<!-- 进度条 -->
					<template v-if="curTabIdx === 0 || videoState[curTabIdx].currentTime >= 1 || videoState[1].process > 0 || videoState.isPlay">
						<view class="process f24 df s-c">
							<text>{{ second2str(Math.round(videoState[curTabIdx].currentTime)) }}</text>
							<slider
								class="slider"
								:disabled="!videoState[curTabIdx].hadPlayed"
								:value="videoState[curTabIdx].currentTime || 0"
								@change="sliderChange"
								@changing="sliderChanging"
								block-size="16"
								background-color="#F9EADF"
								active-color="#f19f67"
								block-color="#f19f67"
								min="0"
								:max="Math.round(videoState[curTabIdx].duration) || 1" />
							<text>{{ second2str(videoState[curTabIdx].duration) }}</text>
						</view>
						<view
							v-show="curTabIdx === 0"
							class="play"
							:class="{ disabled: !videoState[0].hadPlayed }">
							<view
								class="iconfont"
								:class="[videoState.isPlay ? 'i-pause' : 'i-play']"
								@click="playVideo(0)"></view>
						</view>
					</template>
					<template v-else>
						<view>请按下开始键</view>
						<view>跟随音频指引开始练习</view>
					</template>
				</template>
				<!-- 日常任务 -->
				<template v-else-if="info.task_type === 0">
					<view>请点击选择</view>
					<view>想要完成的任务</view>
				</template>
			</view>
			<view
				class="checkin-submit_btn-wrap"
				v-else-if="info.task_type === 0 || videoState[1].done">
				<view
					class="checkin-submit_btn fwb"
					@click="goCheckin">
					完成{{ info.task_type === 1 ? '练习' : '任务' }}
				</view>
			</view>
		</view>
	</view>

	<!-- 打卡弹框 -->
	<uni-popup
		ref="popupRef"
		type="center"
		:is-mask-click="false">
		<view class="upload-popup ui-box-radius">
			<view class="title mb10">记录今日打卡心得</view>
			<view class="c_c mb30">(请至少完成一项)</view>
			<uni-easyinput
				type="textarea"
				:maxlength="250"
				v-model="experience"
				placeholder="请输入打卡心得"
				:styles="{ height: '320rpx' }"></uni-easyinput>

			<view class="mt30 upload">
				<uni-file-picker
					ref="fileUploadRef"
					file-mediatype="image"
					limit="6"
					title="上传照片（最多6张）"
					:sizeType="['compressed']"
					:auto-upload="false"
					@select="onSelectImg"
					@delete="onDeleteImg"></uni-file-picker>
			</view>

			<view
				class="submit"
				@click="submit">
				确认打卡
			</view>
		</view>
		<view
			class="iconfont i-close ui-popup-close"
			@click="popupRef.close()"></view>
	</uni-popup>
</template>

<script setup name="CheckinTaskAudio">
/**
 * @description 音频播放页/任务选择页
 */
import { onLoad, onUnload } from '@dcloudio/uni-app';
import { reactive, ref, computed } from 'vue';
import { storeToRefs } from 'pinia';
import { redirect, Toast, realImgSrc, setLocal, delLocal, getLocal, isCurPage, sleep } from '@/utils/tools.js';
import { NumberCN } from '@/utils/data-map.js';
import { encodeData, decodeData } from '@/utils/forward-data.js';
import { useQiniuUptoken } from '@/stores/qiniu-uptoken';
import { useLoading } from '@/hooks/loading.js';
import { useUserStore } from '@/stores/user';
import second2str from '@/utils/second-to-str.js';

import Navback from '@/components/Navback/index.vue';

const { user } = storeToRefs(useUserStore());
const { uptoken } = storeToRefs(useQiniuUptoken());
const { loadStart, loadEnd } = useLoading();

const PagePath = 'pages/exclusive/checkin/task-audio/index';

const popupRef = ref();
const fileUploadRef = ref();
const chooseIdx = ref(null); // 当前选中的任务

const curTabIdx = ref(0);
const tabList = ref(['每日心语', '冥想练习']);
function switchTab(idx) {
	if (idx > 0 && !videoState[0].done) {
		Toast('请先听完每日心语音频');
		return;
	}

	// videoCtx[idx > 0 ? 0 : 1].pause();
	videoCtx.pause();
	curTabIdx.value = idx;
	// 等暂停的回调结束后再初始化状态
	setTimeout(() => {
		videoState.isPlay = null;
	}, 500);
	// videoState[idx].isFirstInto = true;
	// videoState[idx].hadPlayed = false;
	// initVideo(idx, idx > 0 ? info.value.audio_practice_name : info.value.audio_intro_name);
}

const experience = ref(''); // 打卡心得
const uploadImgs = ref([]); // 上传的图片
function goCheckin() {
	if (!videoState[1].done && chooseIdx.value === null) {
		return;
	}

	popupRef.value.open();
}
function onSelectImg(e) {
	e.tempFiles.forEach((o, i) => {
		uni.compressImage({
			src: o.path,
			quality: 90,
			compressedWidth: o.image.width > 750 ? 750 : o.image.width,
			success: (res) => {
				res.sourcePath = o.path;
				uploadImgs.value.push(res);
			}
		});
	});
}
function onDeleteImg(e) {
	const delIdx = uploadImgs.value.findIndex((o) => o.sourcePath === e.tempFilePath);
	if (delIdx > -1) {
		uploadImgs.value.splice(delIdx, 1);
	}
}

let posting = false; // 是否正在上传中
function submit() {
	async function post() {
		const { code, data } = await ajax({
			method: 'post',
			url: 'api/checkin/submit',
			data: {
				days_conf_id: info.value.days_conf_id,
				chapter_id: info.value.chapter_id,
				task_id: chooseIdx.value !== null ? info.value.task_list[chooseIdx.value].id : null,
				img_keys: uploadImgs.value.map((o) => o.imgkey).join(),
				experience: experience.value.trim().length > 250 ? experience.value.trim().substring(0, 250) : experience.value.trim()
			}
		});
		loadEnd();
		posting = false;
		if (code) return;
		delLocal('processVideo2-' + info.value.days_conf_id); // 删掉进度

		redirect('/exclusive/checkin/complete', {
			chapter_id: info.value.chapter_id,
			forward_info: encodeData({
				...info.value,
				...data
			})
		});
	}

	if (posting) return;

	if (!experience.value.trim().length && !uploadImgs.value.length) {
		Toast('请至少完成一项');
		return;
	}

	posting = true;
	loadStart({ title: '上传中' });

	if (uploadImgs.value.length) {
		const now = Date.now();
		let uploaded = 0;
		uploadImgs.value.forEach((obj, idx) => {
			const key = `${now}${idx}`;
			uni
				.uploadFile({
					url: VITE_UPLOAD_HOST,
					filePath: obj.tempFilePath,
					name: 'file',
					formData: {
						token: uptoken.value,
						key
					}
				})
				.then((res) => {
					if (res.statusCode === 200) {
						obj.imgkey = key;
						uploaded++;

						if (uploaded >= uploadImgs.value.length) {
							post();
						}
					}
				})
				.catch((e) => {
					console.log('catch', e);
				});
		});
	} else {
		post();
	}
}

/** 播放音频相关 */
const videoState = reactive({
	isPlay: null, // 是否正在播放，初始null为了避免界面闪一下播放中的状态
	// ended: false ,// 是否播放结束，iOS播放结束会清空地址
	0: { isFirstInto: true, hadPlayed: true },
	1: { isFirstInto: true, hadPlayed: true }
});
// let videoCtx = [uni.createInnerAudioContext(), uni.createInnerAudioContext()];
const videoCtx = uni.getBackgroundAudioManager();
const holdingProcess = ref(false); // 是否正在调整进度
// const hasPlayed = ref(false); // 执行过onplay

// 调整播放进度结束
function sliderChange(e) {
	holdingProcess.value = false;
	let newTime = Math.round(e.detail.value);
	// 用于判断是否听完过
	const done = getLocal(`playVideo${curTabIdx.value + 1}Done-${info.value.days_conf_id}`);
	if (newTime > videoState[curTabIdx.value].process && !done) {
		newTime = videoState[curTabIdx.value].process;
	}
	videoState[curTabIdx.value].currentTime = newTime;
	seekVideo(curTabIdx.value, newTime);
}
// 调整播放进度中
function sliderChanging(e) {
	holdingProcess.value = true;
	videoState[curTabIdx.value].currentTime = Math.round(e.detail.value);
}

// 初始化播放音乐
function initVideo(idx, title) {
	if (!info.value.audio_intro && !info.value.audio_practice) return;

	// videoCtx[idx].src = realImgSrc(idx > 0 ? info.value.audio_practice : info.value.audio_intro);
	// videoCtx[idx].title = title;
	videoCtx.src = realImgSrc(idx > 0 ? info.value.audio_practice : info.value.audio_intro);
	videoCtx.title = title;
	// videoCtx.playbackRate = 2;

	// videoCtx[idx].onCanplay(() => {
	videoCtx.onCanplay(async () => {
		console.log('onCanplay', videoState[idx].hadPlayed);
		if (!isCurPage(PagePath) || videoState[idx].hadPlayed) return;

		videoState[idx].hadPlayed = true;
	});

	// 停止的时候,改变icon状态
	// videoCtx[idx].onEnded(async () => {
	videoCtx.onEnded(async () => {
		if (!isCurPage(PagePath)) return;

		const done = getLocal(`playVideo${idx + 1}Done-${info.value.days_conf_id}`);
		const newTime = 0.001;
		// videoCtx[idx].seek(newTime);
		videoCtx.seek(newTime);
		videoState.isPlay = false;
		videoState[idx].ended = true;
		videoState[idx].done = true;
		setLocal(`playVideo${idx + 1}Done-${info.value.days_conf_id}`, true);
		setLocal(`processVideo${idx + 1}-${info.value.days_conf_id}`, newTime);
		videoState[idx].process = newTime;
		videoState[idx].currentTime = videoState[idx].duration;

		// 第一次听完第一个音频，自动切换到第二个
		if (idx === 0 && !done) {
			await sleep(200);
			switchTab(1);
		}
	});
	// videoCtx[idx].onPause(() => {
	videoCtx.onPause(() => {
		if (!isCurPage(PagePath)) return;
		videoState.isPlay = false;
	});
	// videoCtx[idx].onStop(() => {
	videoCtx.onStop(() => {
		if (!isCurPage(PagePath)) return;
		if (videoState.isPlay === null) return;
		videoState.isPlay = false;
		videoState[idx].ended = true;
		// 关闭屏幕常亮
		uni.setKeepScreenOn({
			keepScreenOn: false
		});
	});
	// videoCtx[idx].onPlay(() => {
	videoCtx.onPlay(() => {
		if (!isCurPage(PagePath)) return;
		console.log('onPlay', videoState.isPlay);

		// 从上次的地方开始播放
		if (videoState[idx].process > 0 && videoState.isPlay === null) {
			console.log('seek', videoState[idx].process);
			videoCtx.seek(videoState[idx].process);
			videoState[idx].currentTime = videoState[idx].process;
			// } else {
			// 	console.log('seek', 0.001);
			// 	videoCtx.seek(0.001);
			// 	videoState[idx].currentTime = 0.001;
		}

		videoState.isPlay = true;
		videoState[idx].isFirstInto = false;
		videoState[idx].ended = false;
		// 设置屏幕常亮
		uni.setKeepScreenOn({
			keepScreenOn: true
		});
	});
	// videoCtx[idx].onTimeUpdate(() => {
	videoCtx.onTimeUpdate(() => {
		if (!isCurPage(PagePath)) return;
		// 没有正在手动改进度的话，自动更新播放进度
		if (!holdingProcess.value && videoState.isPlay) {
			// let curTime = Math.round(videoCtx[idx].currentTime) || 0.001;
			let curTime = videoCtx.currentTime || 0.001;
			if (curTime <= 0) {
				curTime = 0.001;
			} else if (curTime > videoState[idx].duration) {
				curTime = videoState[idx].duration;
			}
			videoState[idx].currentTime = curTime;

			// 记录播放进度
			if (curTime > videoState[idx].process) {
				setLocal(`processVideo${idx + 1}-${info.value.days_conf_id}`, curTime);
				videoState[idx].process = curTime;
			}
		}
	});
}

// 播放
function playVideo(idx, reload) {
	if (!videoState[idx].hadPlayed) return;

	// 重播清空缓存
	if (reload) {
		videoState[idx].done = false;
		seekVideo(idx, 0);
		setLocal('processVideo2-' + info.value.days_conf_id, 0);
		videoState[idx].process = 0.001;
		videoState[0].process = 0.001;
		videoState[0].currentTime = 0.001;
	}

	// if (videoState[idx].ended || !videoCtx[idx].src || videoCtx[idx].src !== realImgSrc(idx > 0 ? info.value.audio_practice : info.value.audio_intro)) {
	if (videoState[idx].ended || !videoCtx.src || videoCtx.src !== realImgSrc(idx > 0 ? info.value.audio_practice : info.value.audio_intro)) {
		initVideo(idx, idx > 0 ? info.value.audio_practice_name : info.value.audio_intro_name);

		// videoCtx[idx].volume = 1;
		// videoCtx[idx].play();
		videoCtx.play();
	} else {
		if (!videoState.isPlay) {
			videoState.isPlay = true;
			// videoCtx[idx].volume = 1;
			// videoCtx[idx].play();
			videoCtx.play();
		} else {
			videoState.isPlay = false;
			// videoCtx[idx].pause();
			videoCtx.pause();
		}
	}
}
function seekVideo(idx, newTime) {
	if (!videoState[idx].hadPlayed) return;

	if (newTime <= 0) {
		newTime = 0.001;
	}
	// videoCtx[idx].seek(newTime);
	videoCtx.seek(newTime);
	videoState[idx].currentTime = newTime;
	// videoCtx[idx].play();
	videoCtx.play();
}

const info = ref({}); // 上一个页面带过来的数据
onLoad((opts) => {
	info.value = decodeData(opts.forward_info);
	videoState[0].done = getLocal('playVideo1Done-' + info.value.days_conf_id);
	videoState[0].process = getLocal('processVideo1-' + info.value.days_conf_id);
	videoState[0].currentTime = videoState[0].process;
	videoState[0].duration = info.value.audio_intro_duration;
	videoState[1].done = getLocal('playVideo2Done-' + info.value.days_conf_id);
	videoState[1].process = getLocal('processVideo2-' + info.value.days_conf_id);
	videoState[1].duration = info.value.audio_practice_duration;
	videoState[1].currentTime = videoState[1].process;
});

// 页面销毁时,停止播放音乐并销毁
onUnload(() => {
	// videoCtx[0]?.stop();
	// videoCtx[1]?.stop();
	// videoCtx = null;
	videoCtx.src = realImgSrc('unused');
	videoCtx.stop();
});
</script>

<style lang="scss">
@import 'index.scss';
</style>

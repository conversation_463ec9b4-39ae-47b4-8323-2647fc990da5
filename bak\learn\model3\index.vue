<template>
<view class="formal-model3" :class="{ 'swiper-question-wrap': questionStep }" :style="{ paddingTop: user.statusBarHeight }">
	<uni-icons type="back" class="nav-back" @click="goBack()"></uni-icons>
	
	<!-- 题目列表 -->
	<view class="nav-title" v-if="questionStep" :style="{ top: user.statusBarHeight }">案例题</view>
	<Question v-if="questionStep" v-show="showQuestion" ref="questionRef" class="swiper-question" :short="shortQuestion" :list="questionList" @midComplete="midComplete" prevText="上一页" nextText="下一页" @submit="submit" />
	
	<!-- 菜单 -->
	<view v-else v-show="showMenu">
		<view class="formal-menu short">
			<view class="button block plain title f34">为了在以后更好地帮助他人，以下4个部分可以帮助自我探索，认识自我</view>
			<view class="border-box mt30">
				<view class="button block light f36" :class="{ complete: completeStep > i, disabled: completeStep < i }" @click="completeStep >= i && refs[`q${i + 2}1Ref`].open()" v-for="(v, i) in menuArr" :key="i">{{ v }}</view>
			</view>
		</view>
		<view class="button small-submit" style="width:300rpx" @click="goBack()">返回总目录</view>
	</view>
	
	
	<!-- ===============================Q1 -->
	<!-- q1-1 弹窗 -->
	<uni-popup ref="q11Ref" type="center">
		<view class="popup-fullscreen question">
			<view class="button plain block btn-tit">心理关爱</view>
			
			<view class="button border block tips">心理关爱是一个生命抚慰另一个生命的过程。心理关爱志愿者只有先自我探索，了解自己的想法、感受和价值观，理解和接纳自己，才能增加对他人的同理心，更好地理解、帮助他人。</view>
			
			<view class="button block round main-btn" @click="nextPage('q1-1', 'q1-2')">下一页</view>
		</view>
	</uni-popup>
	
	<!-- Q1-2 弹框 -->
	<uni-popup ref="q12Ref" type="center">
		<view class="popup-fullscreen intro">
			<view class="intro-ticket">
				<view class="dots">
					<text class="dot" v-for="v in 14" :key="v"></text>
				</view>
				<view class="button text">
					<view>如果心理关爱志愿者没有经过自我探索的练习，不了解自我的特点，便难以区分自己与求助者的观点，容易将自我观点强加于人，反而破坏助人关系，影响助人效果。</view>
				</view>
			</view>
			<view class="button plain round small-submit" @click="nextPage('q1-2', 'q1-3')">下一页</view>
		</view>
	</uni-popup>
	
	<!-- Q1-3 对话 -->
	<uni-popup ref="q13Ref" type="center">
		<view class="popup-fullscreen">
			<Dialogue v-if="questionStep == 'q1-3'" :list="dialogue1" @submit="nextPage('q1-3', 'q1')" />
		</view>
	</uni-popup>
	
	
	<!-- ===============================Q2 -->
	<!-- Q2-1 弹窗 -->
	<uni-popup ref="q21Ref" type="center">
		<view class="popup-fullscreen question q2-1">
			<view class="title">什么是价值观？</view>
			<view class="intro-box">
				<view class="info">价值观是我们内心深处最深切的渴望，左右着我们与世界、他人以及自己互动和联系的方式，是指导并激励我们在生活中前进的主要原则。</view>
			</view>
			<view class="button small-submit" @click="nextPage('q2-1', 'q2-2')">下一页</view>
		</view>
	</uni-popup>
	
	<!-- Q2-2 弹窗 -->
	<uni-popup ref="q22Ref" type="center">
		<view class="popup-fullscreen question q2-2">
			<view class="button plain block btn-tit">价值观的影响</view>
			
			<view class="button border block" style="height:86rpx">领导：周末突然需要加班，谁要参加？</view>
			<view class="arrows">
				<view class="arrow arrow1"></view>
				<view class="button border">
					<text class="c_yellow">A员工：</text>我的价值观是家庭第一。
				</view>
				<view class="button border">
					<text class="c_blue">B员工：</text>我的价值观是追求成功。
				</view>
			</view>
			
			<view class="arrows">
				<view class="arrow arrow2"></view>
				<view class="button border">
					<text class="c_yellow">A员工：</text>选择不参加加班，回家陪家人过生日。
				</view>
				<view class="button border">
					<text class="c_blue">B员工：</text>选择参加加班，并向家人解释。
				</view>
			</view>
			
			<view class="button block round main-btn" @click="nextPage('q2-2', 'q2-3')">下一页</view>
		</view>
	</uni-popup>
	
	<!-- Q2-3 解析 -->
	<uni-popup ref="q23Ref" type="center">
		<view class="popup-fullscreen jiexi">
			<view class="title">答案解析</view>
			<view class="intro-box">
				<view class="subtit" style="margin-top: 150rpx">解析</view>
				<view class="info">不同的态度和行为背后往往隐藏着不同的价值观。志愿者要理解自身行为或求助者的行为，需要先了解背后的价值观。</view>
			</view>
			<view class="button small-submit" @click="nextPage('q2-3', 'q2-4')">下一页</view>
		</view>
	</uni-popup>
	
	<!-- Q2-4 弹窗 -->
	<uni-popup ref="q24Ref" type="center">
		<view class="nav-title" :style="{ top: user.statusBarHeight }">案例题</view>
		<view class="popup-fullscreen q2-4" v-if="questionStep == 'q2-4'">
			<DropCategory :info="dropCate1" @complete="completeDropCate1" />
		</view>
	</uni-popup>
	
	<!-- Q2-5 解析 -->
	<uni-popup ref="q25Ref" type="center">
		<view class="popup-fullscreen jiexi">
			<view class="title">答案解析</view>
			<view class="intro-box">
				<view class="info">
					<view>助人时，要尊重对方的价值观，受不同生活环境、成长背景影响，每个人都有自己的价值观。在助人过程中，志愿者容易下意识地评价对方的价值观或行为，导致对方产生抵触情绪，破坏助人的效果。</view>
					<view class="mt50 mb50">价值观没有好坏对错之分。</view>
					<view>作为志愿者，助人时须时刻提醒自己，要尊重求助者的价值观。</view>
				</view>
			</view>
			<view class="button small-submit" @click="submit()">我知道了</view>
		</view>
	</uni-popup>
	
	
	<!-- ===============================Q3 -->
	<!-- Q3-1 弹框 -->
	<uni-popup ref="q31Ref" type="center">
		<view class="popup-fullscreen intro">
			<view class="intro-ticket">
				<view class="dots">
					<text class="dot" v-for="v in 14" :key="v"></text>
				</view>
				<view class="button text">
					<view>今天的我们可以说是过去的种种经历塑造的，绘制生命线就是一种探索自己过往经历的心理技术。</view>
				</view>
			</view>
			<view class="button plain round small-submit" @click="nextPage('q3-1', 'q3-2')">下一页</view>
		</view>
	</uni-popup>
	
	<!-- Q3-2 弹窗 -->
	<uni-popup ref="q32Ref" type="center">
		<view class="popup-fullscreen question q3-2">
			<view class="title">为什么要绘制生命线？</view>
			<view class="intro-box">
				<view class="subtit">我们的过去是由各种事件组成，其中一些重要事件对当下的我们及他人，仍在产生影响。</view>
				<view class="info">回顾、思考这些事件，将使我们更深刻地了解自我，对“我是什么样的人”“我是如何成为今天的我”等问题感到豁然开朗，也将使我们更好地帮助求助者。</view>
			</view>
			<view class="button small-submit" @click="nextPage('q3-2', 'q3-3')">下一页</view>
		</view>
	</uni-popup>
	
	<!-- q3-3 弹窗 -->
	<uni-popup ref="q33Ref" type="center">
		<view class="popup-fullscreen question">
			<view class="button plain block btn-tit">如何绘制生命线</view>
			<view class="c_b f32 mt20 ml50 mr50">在生命线中，横轴代表时间，从左到右代表从出生至今；纵轴代表情绪，越向上代表积极情绪越强烈，越向下代表消极情绪越强烈。</view>
			
			<view class="img curve1"></view>
			
			<view class="button block round main-btn" @click="nextPage('q3-3', 'q3-4')">下一页</view>
		</view>
	</uni-popup>
	
	<!-- q3-4 弹窗 -->
	<uni-popup ref="q34Ref" type="center">
		<view class="popup-fullscreen question">
			<view class="button plain block btn-tit">案例</view>
			<view class="c_b f32 mt20 ml50 mr50">
				<view>· 8岁时父母离异，内心埋怨父亲；</view>
				<view>· 13岁和校排球队一起夺得区冠军；</view>
				<view>· 18岁高考失利，错失梦想中的大学；</view>
				<view>· 23岁以优异成绩毕业并进入现公司；</view>
				<view>· 26岁步入婚姻并有了女儿；</view>
				<view>· 30岁母亲重病。</view>
			</view>
			
			<view class="img curve2"></view>
			
			<view class="button block round main-btn" @click="nextPage('q3-4', 'q3-5')">下一页</view>
		</view>
	</uni-popup>
	
	<!-- q3-5 弹窗 -->
	<uni-popup ref="q35Ref" type="center">
		<view class="popup-fullscreen question q3-5">
			<view class="title">课后练习1</view>
			<view class="title2 fwb">绘制自己的生命线</view>
			
			<view class="f24 mt20 ml50 mr50">
				<view>1.确保环境安静，20分钟内不受打扰。</view>
				<view>2.在纸上画出横轴代表时间，纵轴代表情绪。</view>
				<view>3.回忆人生各阶段印象深刻的事件，以5岁为一个阶段。</view>
				<view>4.在相应的时间和情绪坐标处，标出印象深刻的事件。</view>
				<view>5.用一条曲线将这些事件都连起来。</view>
				<view>6.思考一下，这些事件和当下的你有什么样的联系。</view>
			</view>
			
			<view class="img curve3"></view>
			<zwp-draw-pad class="draw-canvas" width="750rpx" height="650rpx" color="#f00" :size="1" />
			
			<view class="button block round main-btn" @click="nextPage('q3-5', 'q3-6')">下一页</view>
		</view>
	</uni-popup>
	
	<!-- Q3-6 解析 -->
	<uni-popup ref="q36Ref" type="center">
		<view class="popup-fullscreen jiexi">
			<view class="title">温馨提示</view>
			<view class="intro-box">
				<view class="info">
					<view>① 可以闭上眼睛，回忆当时的生活情境及生活中的重要他人。</view>
					<view>② 重要事件并不一定是大事，那些令你印象深刻的小事也可能是对你产生深远影响的重要事件。</view>
					<view class="mt50 pt50">在绘制过程中，你可能产生强烈的情绪、疑问，如果有需要，你可以找<text class="c_red">其他心理关爱志愿者</text>或心理咨询师进行倾诉或者探讨。</view>
				</view>
			</view>
			<view class="button small-submit" @click="nextPage('q3-6', 'q3-7')">下一页</view>
		</view>
	</uni-popup>
	
	<!-- Q3-7 弹框 -->
	<uni-popup ref="q37Ref" type="center">
		<view class="popup-fullscreen intro">
			<view class="button block light pl50 pr50">探索求助者的生命历程</view>
			<view class="intro-ticket mt50 pl50 pr50 f32">
				<view class="dots">
					<text class="dot" v-for="v in 14" :key="v"></text>
				</view>
				<view class="mt30">接纳和理解对求助者来说是十分重要的心理支持，但志愿者要真正理解求助者并非易事，有时甚至会感到求助者的想法或行为不合理。</view>
				<view class="mt50 pt10 pb30">志愿者可以询问和倾听求助者过去的高光时刻与低谷时刻，探索求助者的过往生命历程，以更好地理解求助者当下的想法与行为，从而真正做到共情与接纳。</view>
			</view>
			<view class="button plain round small-submit" @click="submit()">我知道了</view>
		</view>
	</uni-popup>
	
	
	<!-- ===============================Q4 -->
	<!-- Q4-1 连线 -->
	<uni-popup ref="q41Ref" type="center">
		<view class="nav-title" :style="{ top: user.statusBarHeight }">案例题</view>
		<view class="popup-fullscreen question q4-1">
			<ConnectLine :info="lines1" @complete="lineComplete.lines1 = true" />
			<view class="button small-submit" @click="nextPage('q4-1', 'q4-2')">提交</view>
		</view>
	</uni-popup>
	
	<!-- Q4-2 弹框 -->
	<uni-popup ref="q42Ref" type="center">
		<view class="popup-fullscreen intro">
			<view style="font-size: 70rpx">温馨提示</view>
			<view class="intro-ticket">
				<view class="dots">
					<text class="dot" v-for="v in 14" :key="v"></text>
				</view>
				<view class="button text">
					<view class="pt20 pb20">情绪是个人心理的重要组成部分。描绘自己的情绪也是自我探索的重要组成部分。</view>
				</view>
			</view>
			<view class="button plain round small-submit" @click="nextPage('q4-2', 'q4-3')">下一页</view>
		</view>
	</uni-popup>
	
	<!-- Q4-3 弹窗 -->
	<uni-popup ref="q43Ref" type="center">
		<view class="popup-fullscreen question">
			<view class="title">感知情绪的重要性</view>
			<view class="intro-box">
				<view class="info">在日常生活中，不仅需要理性地思考，也需要去感性地感知情绪。良好的情绪感知能力有助于志愿者更好地区分自我和求助者的情绪。</view>
			</view>
			<view class="button small-submit" @click="nextPage('q4-3', 'q4-4')">下一页</view>
		</view>
	</uni-popup>
	
	<!-- Q4-4 弹窗 -->
	<uni-popup ref="q44Ref" type="center">
		<view class="popup-fullscreen question q4-4">
			<view class="c_b f32 ml50 mr50 mb30 pl20 pr20">在案例1中，如果志愿者在帮助求助者时进行了自我情绪觉察，并觉察到自己有如下的情绪变化：</view>
			<view class="intro-box fwb">
				<view class="mt10">志愿者听到求助者想要放弃抚养权</view>
				<text class="arrow"></text>
				<view>心跳和呼吸有些加速，想要指责求助者不负责任</view>
				<text class="arrow"></text>
				<view>及时察觉了自己的愤怒</view>
				<text class="arrow"></text>
				<view>意识到，愤怒是源于自己早年经历，与求助者无关</view>
				<text class="arrow"></text>
				<view>将重点拉回到帮助求助者上</view>
			</view>
			<view class="button small-submit" @click="nextPage('q4-4', 'q4')">下一页</view>
		</view>
	</uni-popup>
	
	<!-- Q4-5 连线 -->
	<uni-popup ref="q45Ref" type="center">
		<view class="nav-title" :style="{ top: user.statusBarHeight }">案例题</view>
		<view class="popup-fullscreen question q4-5">
			<ConnectLine :info="lines2" @complete="lineComplete.lines2 = true" />
			<view class="button small-submit" @click="nextPage('q4-5', 'q4')">提交</view>
		</view>
	</uni-popup>
	
	<!-- Q4-6 弹框 -->
	<uni-popup ref="q46Ref" type="center">
		<view class="popup-fullscreen intro">
			<view style="font-size: 70rpx">课后练习2</view>
			<view class="intro-ticket">
				<view class="dots">
					<text class="dot" v-for="v in 14" :key="v"></text>
				</view>
				<view class="button text">
					<view class="pt20 pb20">在日常生活、工作和助人过程中，体会自己的情绪，并尝试用词汇来描绘自己的情绪，逐渐提升自己的情绪觉察能力。</view>
				</view>
			</view>
			<view class="button plain round small-submit" @click="submit()">我知道了</view>
		</view>
	</uni-popup>
	
	
	<!-- ===============================Q5 -->
	<!-- Q5-1 弹框 -->
	<uni-popup ref="q51Ref" type="center">
		<view class="popup-fullscreen intro">
			<view class="intro-ticket">
				<view class="dots">
					<text class="dot" v-for="v in 14" :key="v"></text>
				</view>
				<view class="button text">
					<view>了解自己应对压力的方式，找到适合自己的减压方式，调整好自我心理健康状态，才能更好地帮助他人。</view>
				</view>
			</view>
			<view class="button plain round small-submit" @click="nextPage('q5-1', 'q5')">下一页</view>
		</view>
	</uni-popup>
	
	<!-- q5-2 弹窗 -->
	<uni-popup ref="q52Ref" type="center">
		<view class="popup-fullscreen question">
			<view class="button plain block btn-tit">压力与效率的关系</view>
			<view class="f26 mt20 mr30">
				<view class="fwb">毫无压力：</view>
				<view class="c_b">动力不足，身心麻木，工作绩效低下</view>
				<view class="fwb mt20">有点压力：</view>
				<view class="c_b">动力充足，身心兴奋，工作绩效达到高峰</view>
				<view class="fwb mt20">压力过大：</view>
				<view class="c_b">感到崩溃，身心疲惫，工作绩效低下</view>
			</view>
			
			<view class="img curve4"></view>
			
			<view class="button block round main-btn" @click="nextPage('q5-2', 'q5-3')">下一页</view>
		</view>
	</uni-popup>
	
	<!-- Q5-3 弹窗 -->
	<uni-popup ref="q53Ref" type="center">
		<view class="nav-title" :style="{ top: user.statusBarHeight }">案例题</view>
		<view class="popup-fullscreen q5-3" v-if="questionStep == 'q5-3'">
			<DropCategory :info="dropCate2" @complete="nextPage('q5-3', 'q5-4')" />
		</view>
	</uni-popup>
	
	<uni-popup ref="q54Ref" type="center">
		<view class="popup-fullscreen jiexi">
			<view class="title">答案解析</view>
			<view class="intro-box">
				<view class="subtit">解析</view>
				<view class="info">在过大的压力下，我们的负面情绪、身体不适以及不利于健康的行为都有可能增加，要多加注意。
					<!-- <view>健康的心理不仅仅是没有静很障碍，还包含了一种幸福的状态。</view>
					<view>它是我们工作生活的重要基础，也是我们整体健康的一部分，对所有人都很重要。</view> -->
				</view>
			</view>
			<view class="button small-submit" @click="nextPage('q5-4', 'q5-5')">下一页</view>
		</view>
	</uni-popup>
	
	<uni-popup ref="q55Ref" type="center">
		<view class="popup-fullscreen">
			<Dialogue v-if="questionStep == 'q5-5'" :list="dialogue2" @submit="nextPage('q5-5', 'q5-6')" />
		</view>
	</uni-popup>
	
	<uni-popup ref="q56Ref" type="center">
		<view class="popup-fullscreen jiexi">
			<view class="title">答案解析</view>
			<view class="intro-box">
				<view class="subtit">解析</view>
				<view class="info">
					<view>只要方法对，压力不可怕。</view>
					<view class="mt50">每个人应对压力的有效方式都不同，这受性别、年龄、性格、成长经历等影响。寻找适合自己且健康的减压方式，才能够积极使用、长期坚持，从而有效减压。</view>
				</view>
			</view>
			<view class="button small-submit" @click="nextPage('q5-6', 'q52')">下一页</view>
		</view>
	</uni-popup>
	
	<uni-popup ref="q57Ref" type="center">
		<view class="popup-fullscreen intro">
			<view class="intro-ticket">
				<view class="dots">
					<text class="dot" v-for="v in 14" :key="v"></text>
				</view>
				<view class="button text">
					<view>什么是健康的减压方式？通过回答以下问题，你将能找到答案。</view>
				</view>
			</view>
			<view class="button plain round small-submit" @click="nextPage('q5-7', 'q53')">下一页</view>
		</view>
	</uni-popup>
	
	
	<!-- ===============================Q6 -->
	<uni-popup ref="q61Ref" type="center">
		<view class="popup-fullscreen intro">
			<view class="intro-ticket">
				<view class="dots">
					<text class="dot" v-for="v in 14" :key="v"></text>
				</view>
				<view class="button text">
					<view>在助人过程中，志愿者如果发现求助者压力较大的，可以与求助者探讨适合其的解压方法，并鼓励和建议求助者采取积极解压方法。</view>
				</view>
			</view>
			<view class="button plain round small-submit" @click="nextPage('q6-1', 'q6-2')">下一页</view>
		</view>
	</uni-popup>
	
	<uni-popup ref="q62Ref" type="center">
		<view class="popup-fullscreen question">
			<view class="button plain block btn-tit">课程回顾</view>
			
			<view class="button border block tips">
				<view>增强了自我觉察，我们才能更好地观察和理解求助者；</view>
				<view class="mt50">学会了健康减压，我们才有能力去关怀和赋能求助者。</view>
			</view>
			
			<view class="button block round main-btn" @click="nextPage('q6-2', 'q6')">下一页</view>
		</view>
	</uni-popup>
	
	<uni-popup ref="q63Ref" type="center">
		<view class="popup-fullscreen question">
			<view class="button plain block btn-tit">总结</view>
			
			<view class="button border block tips">大家根据课程内容，在需要的时候觉察自己的情绪，体会自己的感受，探索自己的经历，调节自身减压方式。</view>
			
			<view class="button block round main-btn" @click="submit()">我知道了</view>
		</view>
	</uni-popup>
	
	
	<!-- 完成进度 弹窗 -->
	<Process ref="processRef" :total="5" :step="completeStep" modelName="第二" :jobNum="user.loginJobnum" :isComplete="questionStep == 'q6-3'" @complete="goBack()" />
</view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { storeToRefs } from 'pinia';
import { useUserStore } from '@/stores/user';

import Question from '@/components/Question/index.vue';
import ConnectLine from '@/components/ConnectLine/index.vue';
import DropCategory from '@/components/DropCategory/index.vue';
import Dialogue from '@/components/Dialogue/index.vue';
import Process from '@/components/Process/index.vue';

import formatQuestion from '@/utils/format-formal-question.js';
import {
	goBack,
	getLocal,
	setLocal,
	postError,
	Toast,
	modelComplete,
} from '@/utils/tools.js';

const { user } = storeToRefs(useUserStore());

const modelKey = 'model3-complete';
const completeStep = ref(getLocal(modelKey) || 0); // 已完成的模块step
const questionList = ref([]);
const shortQuestion = ref(false);
const questionStep = ref(null);
const showQuestion = ref(true);
const showMenu = ref(false);
const menuArr = ref([
	'了解自己的价值观',
	'绘制自己的生命线',
	'描绘自己的情绪',
	'缓解自己的压力',
	'小结',
]);

const lineComplete = reactive({
	lines1: false,
	lines2: false,
	// lines3: false,
})
// 连线对象1
const lines1 = ref({
	title: '体会情绪|请找出下列词语对应的表情，并连线',
	lineArr: [
		'开心',
		'😭',
		'难过',
		'🙂',
		'平静',
		'😢',
		'担心',
		'😁',
	],
	rightConn: [
		'0-7',
		'1-2',
		'3-4',
		'5-6',
	]
});
// 连线对象2
const lines2 = ref({
	title: '体会情绪|请仔细体会以下词语所代表的情绪，并将两种相近的情绪进行连线。',
	lineArr: [
		'担心',
		'遗憾',
		'开心',
		'难过',
		'生气',
		'幸福',
		'伤心',
		'愤怒',
		'失望',
		'焦虑',
	],
	rightConn: [
		'0-9',
		'2-5',
		'4-7',
		'3-6',
		'1-8',
	]
});
// 连线对象3
// const lines3 = ref({
// 	title: '请连线匹配以下典型表现|和心理障碍',
// 	lineArr: [
// 		'惊恐、焦虑',
// 		'焦虑症',
// 		'妄想、幻觉',
// 		'精神分裂症',
// 		'抑郁、躁狂',
// 		'抑郁症',
// 		'低落、厌世',
// 		'双相情感障碍',
// 	],
// 	rightConn: [
// 		'0-1',
// 		'2-3',
// 		'4-7',
// 		'5-6',
// 	]
// });

// 拖动归类对象1
const dropCate1 = ref({
	title: '下面是常见的10种价值观，请选出对你来说最重要的4个价值观，并回忆它们对你产生的影响。',
	totalProps: 4,
	type: 1,
	category: [{
		name: '第一',
		list: [
			'健康',
			'财富和金钱',
		]
	}, {
		name: '第二',
		list: [
			'祖国',
			'宗教信仰自由',
		]
	}, {
		name: '第三',
		list: [
			'职业、工作',
			'名誉、声誉',
		]
	}, {
		name: '第四',
		list: [
			'社会地位',
			'亲朋好友',
			'家庭',
			'自我价值感',
		]
	}],
});
// 拖动归类对象2
const dropCate2 = ref({
	title: '过大的压力不利于身心健康，需及时察觉并积极调节。请将以下压力表现进行归类。',
	totalProps: 9,
	category: [{
		name: '情绪',
		list: [
			'焦虑',
			'暴躁',
			'抑郁',
		]
	}, {
		name: '身体',
		list: [
			'身体疼痛，乏力',
			'失眠',
			'免疫力降低',
		]
	}, {
		name: '行为',
		list: [
			'赌博',
			'暴食',
			'酗酒吸烟',
		]
	}],
});

// 对话列表1
const dialogue1 = ref([{
	user: 0,
	msg: '我正在离婚，因为没条件抚养孩子，可能要放弃抚养权。',
}, {
	user: 1,
	msg: '你不应该这样做，会错过孩子的成长啊！',
}, {
	user: 0,
	msg: '我会重新认真考虑。',
}, {
	user: 1,
	msg: '你有感觉好一些了吗？欢迎下次再来找我。',
}, {
	user: 0,
	msg: '我心情更差了，下次还是不找你了。',
}]);
// 对话列表2
const dialogue2 = ref([{
	user: 0,
	msg: '小王，我特别羡慕你，你好像从来没有压力。',
}, {
	user: 1,
	msg: '我有压力，但打乒乓球能帮助我减压。',
}, {
	user: 0,
	msg: '为啥我一看到快速弹跳的乒乓球压力更大了呢？',
}, {
	user: 1,
	msg: '适合每个人的减压方式不一样。先了解一些健康的减压方法，然后逐一尝试吧！',
}]);

const q11Ref = ref();
const q12Ref = ref();
const q13Ref = ref();

const q21Ref = ref();
const q22Ref = ref();
const q23Ref = ref();
const q24Ref = ref();
const q25Ref = ref();

const q31Ref = ref();
const q32Ref = ref();
const q33Ref = ref();
const q34Ref = ref();
const q35Ref = ref();
const q36Ref = ref();
const q37Ref = ref();

const q41Ref = ref();
const q42Ref = ref();
const q43Ref = ref();
const q44Ref = ref();
const q45Ref = ref();
const q46Ref = ref();

const q51Ref = ref();
const q52Ref = ref();
const q53Ref = ref();
const q54Ref = ref();
const q55Ref = ref();
const q56Ref = ref();
const q57Ref = ref();

const q61Ref = ref();
const q62Ref = ref();
const q63Ref = ref();

const processRef = ref();
const questionRef = ref();

const refs = reactive({
	q11Ref,
	q12Ref,
	q13Ref,

	q21Ref,
	q22Ref,
	q23Ref,
	q24Ref,
	q25Ref,

	q31Ref,
	q32Ref,
	q33Ref,
	q34Ref,
	q35Ref,
	q36Ref,
	q37Ref,

	q41Ref,
	q42Ref,
	q43Ref,
	q44Ref,
	q45Ref,
	q46Ref,

	q51Ref,
	q52Ref,
	q53Ref,
	q54Ref,
	q55Ref,
	q56Ref,
	q57Ref,

	q61Ref,
	q62Ref,
	q63Ref,
})

async function nextPage(current, next, noSwipe) {
	if ((current === 'q4-1' && !lineComplete.lines1)
	|| (current === 'q4-5' && !lineComplete.lines2)) {
		Toast('请先完成连线');
		return;
	}
	
	// 格式化ref字符
	let currentRef = '';
	if (current?.includes('-')) {
		currentRef = current.replace(/-/g, '') + 'Ref';
	}
	let nextRef = '';
	if (next?.includes('-')) {
		nextRef = next.replace(/-/g, '') + 'Ref';
	}
	
	if (next.includes('-')) {
		questionStep.value = next;
		refs[nextRef].open();
		refs[currentRef].close();
	} else {
		if (showQuestion.value) {
			// 首次进入答题
			const list = await ajax(`question/${next.replace('q', 'model3-')}.json`);
			questionList.value = formatQuestion(list, (obj, idx) => {
				// 插入中途切出的跳转
				if (next === 'q4') {
					if (idx === 0) {
						obj.hidePrev = true;
						obj.midComplete = true;
					} else if (idx === 1) {
						obj.hidePrev = true;
					}
				} else if (next === 'q5') {
					if ([0, 2].includes(idx)) {
						obj.midComplete = true;
					} else if ([1, 3].includes(idx)) {
						obj.hidePrev = true;
					}
				}
			});
		} else {
			// 答题中途切换出去，然后切回来
			showQuestion.value = true;
			if (!noSwipe) {
				questionRef.value.addSwiperIdx();
			}
			if (next === 'q52') {
				shortQuestion.value = true;
			} else {
				shortQuestion.value = false;
			}
		}
		questionStep.value = next;
		refs[currentRef].close();
	}
}

// 做题中途切出
function midComplete() {
	if (questionStep.value === 'q4') {
		questionStep.value = 'q45Ref';
	} else if (questionStep.value === 'q5') {
		questionStep.value = 'q52Ref';
	} else if (questionStep.value === 'q52') {
		questionStep.value = 'q57Ref';
	}
	refs[questionStep.value].open();
	
	setTimeout(() => {
		showQuestion.value = false;
	}, 300)
}

// 全部做完
async function submit(arr) {
	switch(questionStep.value) {
		case 'q1':
			questionStep.value = null;
			showMenu.value = true;
			showQuestion.value = true;
			setLocal('answer-model3-1', arr.map(o => o.checked.join('')).join('-'));
		break;
		
		case 'q4':
			questionStep.value = 'q4-6';
			q46Ref.value.open();
			showQuestion.value = true;
			setLocal('answer-model3-2', arr.map(o => o.checked.join('')).join('-'));
		break;
		
		case 'q6':					
			// 提交结果
			const idxName = ['第一', '第二', '第三', '第四'];
			const { code, error } = await ajax({
				// url: 'api/api.php?a=putModule3',
				url: 'learn/submit',
				method: 'post',
				showLoading: true,
				data: {
					module: 3,
					jobnum: user.value.loginJobnum,
					op1: [
						getLocal('answer-model3-1'),
						getLocal('answer-model3-2'),
						getLocal('answer-model3-3'),
					].join('-'),
					op2: getLocal('answer-model3-drop').map((v, i) => `${idxName[i]}:${v}`).join('|'),
					op3: '情绪:焦虑,暴躁,抑郁|身体:身体疼痛，乏力,失眠,免疫力降低|行为:赌博,暴食,酗酒吸烟',
					op4: arr.map(o => o.checked.join('')).join('-'),
				}
			});
	
			// 异常处理
			if (code || error) {
				postError().then(() => {
					submit(arr);
				}).catch(() => {})
			} else {
				modelComplete(modelKey, 5, completeStep);
				questionStep.value = 'q6-3';
				q63Ref.value.open();
			}
		break;
		
		case 'q2-5':
		case 'q3-7':
		case 'q4-6':
		case 'q53':
		case 'q6-3':
			showMenu.value = true;
			if (questionStep.value === 'q2-5') {
				modelComplete(modelKey, 1, completeStep);
			} else if (questionStep.value === 'q3-7') {
				modelComplete(modelKey, 2, completeStep);
			} else if (questionStep.value === 'q4-6') {
				modelComplete(modelKey, 3, completeStep);
			} else if (questionStep.value === 'q53') {
				modelComplete(modelKey, 4, completeStep);
				setLocal('answer-model3-3', arr.map(o => o.checked.join('')).join('-'));
			} else if (questionStep.value === 'q6-3') {
				modelComplete(modelKey, 5, completeStep);
				setTimeout(() => {
					questionStep.value = 'q6-3'
				}, 0)
			}
			processRef.value.open();
			refs[questionStep.value.replace(/-/g, '') + 'Ref']?.close();
			questionStep.value = null;
		break;
	}
}

function completeDropCate1(arr) {
	setLocal('answer-model3-drop', arr);
	nextPage('q2-4', 'q2-5');
}

onMounted(() => {
	q11Ref.value.open();
})
</script>

<style lang="scss">
	@import 'index.scss';
</style>
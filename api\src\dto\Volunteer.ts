import {
  IsInt,
  IsString,
  IsOptional,
  IsArray,
  ArrayNotEmpty,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

import { IsArrayOfObjects } from '../validate';

export class VolunteerApplyAnswerDto {
  /** 题目ID */
  @IsInt()
    topic_id: number;

  /** 得分 */
  @IsOptional()
  @IsInt()
    score: number;

  /** 答案 */
  @IsOptional()
  @IsString()
    value: string;
}

/** 志愿者申请-提交 */
export class VolunteerApplySubmitDto {
  /** 答案列表 */
  @IsArray()
  @ArrayNotEmpty()
  @IsArrayOfObjects()
  @ValidateNested({ each: true })
  @Type(() => VolunteerApplyAnswerDto)
    answer_list: VolunteerApplyAnswerDto[];
}
import { IsString, IsInt, IsOptional, IsDateString } from 'class-validator';
import { Transform } from 'class-transformer';

/** 服务记录-统计汇总-查询参数 */
export class StatisticsQueryDto {
  /** 工号 */
  @IsOptional()
  @IsString()
    jobnum: string;

  /** 工厂ID */
  @IsInt()
    factory_id: number;

  /** 工厂名称 */
  @IsString()
    factory_name: string;

  /** 排序字段 */
  @IsString()
    field: string = 'rank';
  
  /** 排序方式 */
  @IsString()
    direction: string = 'asc';
    
  /** 提交-开始时间 */
  @IsOptional()
  @IsDateString()
    start_time: string;
  
  /** 提交-结束时间 */
  @IsOptional()
  @IsDateString()
    end_time: string;
}

/** 服务记录-记录明细-查询参数 */
export class LogsQueryDto {
  /** 工号 */
  @IsOptional()
  @IsString()
    jobnum: string;

  /** 工厂ID */
  @IsInt()
    factory_id: number;

  /** 工厂名称 */
  @IsString()
    factory_name: string;

  /** 审核状态 */
  @IsOptional()
  @Transform(({ value }) => parseInt(value, 10))
  @IsInt()
    check_status: number;
    
  /** 提交-开始时间 */
  @IsOptional()
  @IsDateString()
    start_time: string;
  
  /** 提交-结束时间 */
  @IsOptional()
  @IsDateString()
    end_time: string;
}
import { Injectable } from '@nestjs/common';
import { InjectEntityManager } from '@nestjs/typeorm';
import { EntityManager } from 'typeorm';

import { DB_PRE, DB_TRANSACTION_ERROR_CODE } from '../constants';
import { CustomHeadersDto } from '../dto';
import { VolunteerApplySubmitDto } from '../dto/Volunteer';
import { resErr, resOk, insertValues, updateValues } from '../utils';

@Injectable()
export class VolunteerService {
  constructor(@InjectEntityManager() private readonly entityManager: EntityManager) {}

  /** 志愿者申请-提交 */
  async testSubmit(body: VolunteerApplySubmitDto, headers: CustomHeadersDto): Promise<ResObj> {
    const { answer_list } = body;
    const {
      login_jobnum,
      factoryid,
    } = headers;
    
    const list = await this.entityManager.query(`select title, type, dimension, is_scored from ${DB_PRE}volunteer_test_topic order by sort_num asc;`);
    if (answer_list.length !== list.length) {
      return resErr(202, '题目数量不对');
    }

    const dimension: any = {}; // 维度
    const testLogValues = [];
    const now = new Date();
    const diffIdxArr = [
      5,
      6,
      7,
      18
    ]; // 反相计分的题数索引
    list.forEach((o, i) => {
      const answer = answer_list[i];
    
      // 如果参与计分
      if (o.is_scored) {
        if (diffIdxArr.includes(i)) {
          answer.score = 6 - answer.score;
        }

        if (!dimension[o.dimension]) {
          dimension[o.dimension] = [];
        }
        dimension[o.dimension].push(answer.score);
      }

      testLogValues.push([
        factoryid,
        login_jobnum,
        answer.topic_id,
        o.dimension || '',
        answer.value,
        answer.score || answer.score === 0 ? answer.score : null,
        o.is_scored,
        now,
        o.title,
        o.type
      ]);
    });
  
    // 算分
    const dimension_list = [];
    const entries = Object.entries(dimension);
    entries.forEach(([label, arr]: [string, number[]]) => {
      let value = '0';
      if (label === '服务意愿') {
        value = (25 * (arr.reduce((a, b) => a + b) / arr.length)).toFixed(3);
      } else {
        value = (25 * ((arr.reduce((a, b) => a + b) / arr.length) - 1)).toFixed(3);
      }

      dimension_list.push({
        label,
        value,
      });
    });

    const scoreArr = dimension_list.filter(o => o.label !== '服务意愿');
    const total_score = parseFloat(scoreArr[0].value) / 3 + parseFloat(scoreArr[1].value) / 3 + (parseFloat(scoreArr[2].value) + parseFloat(scoreArr[3].value) + parseFloat(scoreArr[4].value)) / 9;

    return this.entityManager.transaction(async (manager) => {
      // 插入申请记录和得分
      await insertValues(
        manager,
        'shenqingzyz_data',
        [
          'factory_id',
          'jobnum',
          'avgA',
          'avgB',
          'avgC',
          'avgD',
          'avgE',
          'avgF',
          'create_time',
          'is_pass'
        ],
        [
          [
            factoryid,
            login_jobnum,
            dimension_list[0].value,
            dimension_list[1].value,
            dimension_list[2].value,
            dimension_list[3].value,
            dimension_list[4].value,
            dimension_list[5].value,
            now,
            /**
             * 选拔标准
             * 1、C>0
             * 2、A平均分>25
             * 3、B1平均分>25
             * 4、总分>25
             * 满足以上四点，方可成为志愿者
             */
            dimension_list[5].value > 0 && dimension_list[0].value > 25 && dimension_list[1].value > 25 && total_score > 25 ? 1 : 0,
          ]
        ]
      );
      // 插入答题记录
      await insertValues(
        manager,
        'volunteer_test_log',
        [
          'factory_id',
          'jobnum',
          'topic_id',
          'dimension',
          'answer',
          'score',
          'is_scored',
          'create_time',
          'topic_title',
          'topic_type'
        ],
        testLogValues
      );
      // 更新用户类型
      await updateValues(
        manager,
        'user',
        ['user_type', 'user_type_update_time'],
        ['潜在志愿者', now],
        'factory_id = ? and jobnum = ? and user_type = ?',
        [
          factoryid,
          login_jobnum,
          '员工'
        ]
      );

      return resOk({
        dimension_list, // 纬度得分
        total_score: total_score.toFixed(2), // 总分
      });
    }).catch(err => {
      return resErr(DB_TRANSACTION_ERROR_CODE, err.message);
    });
  }
}

import {
  IsInt,
  IsString,
  IsOptional,
  IsArray,
  ArrayNotEmpty,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

import { IsArrayOfObjects } from '../validate';

export class VisitorApplyAnswerDto {
  /** 题目ID */
  @IsInt()
    id: number;

  /** 答案 */
  @IsOptional()
  @IsString()
    value: string;
}

/** 志愿者申请-提交 */
export class VisitorApplySubmitDto {
  /** 答案列表 */
  @IsArray()
  @ArrayNotEmpty()
  @IsArrayOfObjects()
  @ValidateNested({ each: true })
  @Type(() => VisitorApplyAnswerDto)
    answer_list: VisitorApplyAnswerDto[];

  /** 试卷类型：0前测 1后侧 */
  @IsInt()
    type: 0 | 1;
}
/**
 * @description 更新总积分排名
 */
const { timerSuccess, timerError } = require('../../../utils/log4js');

module.exports = () => {
	timerSuccess(`总积分排名更新-开始`)

	const currentYear = new Date().getFullYear();
	querySql(`select id from ${databasePre}factory order by id;
	
	select count(1) as count from ${databasePre}user_score_rank where year='${currentYear - 1}'`, (err, re, conn) => {
		if (err) {
			timerError(err);
			return;
		}

		const keyArr = ['score_training', 'score_checkin', 'score_service', 'score_visitor', 'score_others'];
		re[0].forEach(({ id }) => {
			// 志愿者培训 + 打卡活动 + 志愿者服务 + 小课堂 + 其它积分
			conn.query(`select sum(ifnull(module1, 0) + ifnull(module2, 0) + ifnull(module3, 0) + ifnull(module4, 0) + ifnull(module5, 0) + ifnull(module6, 0) + ifnull(module7, 0)) as score, jobnum from ${databasePre}allpoint_data where factory_id='${id}' group by jobnum order by jobnum;

			select ifnull(sum(points_total), 0) as score, jobnum from ${databasePre}checkin_rank where factory_id='${id}' group by jobnum order by jobnum;

			select ifnull(sum(points), 0) as score, jobnum from ${databasePre}service_log where factory_id='${id}' and is_del=0 and check_status<2 group by jobnum order by jobnum;
			
			SELECT r.jobnum, r.sum2 as score FROM psychotest_visitor_data r
			JOIN (
				SELECT jobnum, MAX(id) AS max_id
				FROM psychotest_youke_data
				where factory_id=${id} and sum2 != 0
				GROUP BY jobnum
			) latest
			ON r.jobnum = latest.jobnum AND r.id = latest.max_id
			where factory_id=${id};

			select ifnull(sum(score), 0) as score, jobnum from ${databasePre}user_points where factory_id='${id}' and type=0 group by jobnum order by jobnum;`, (err, result) => {
				if (err) {
					timerError(err);
					return;
				}

				const jobnumObj = {};
				result.forEach((list, idx) => {
					list.forEach(({ jobnum, score }) => {
						jobnum = jobnum.toUpperCase();
						if (jobnumObj[jobnum] === undefined) {
							jobnumObj[jobnum] = {};
						}

						jobnumObj[jobnum][keyArr[idx]] = score;
						jobnumObj[jobnum].score_total = (jobnumObj[jobnum].score_total || 0) + score;
					})
				})

				// 按总积分倒序
				const sqlArr = Object.entries(jobnumObj).sort((a, b) => {
					return b[1].score_total - a[1].score_total;
				}).map(([jobnum, { score_total, score_training, score_checkin, score_service, score_visitor, score_others }], idx) => {
					jobnum = jobnum.toUpperCase();
					return `insert into ${databasePre}user_score_rank(factory_id, jobnum, score_total, score_training, score_checkin, score_service, score_visitor, rank) values(${id}, '${safeString(jobnum)}', ${score_total}, ${score_training}, ${score_checkin}, ${score_service}, ${score_visitor}, ${score_others}, ${idx + 1}) on duplicate key update score_total=${score_total}, score_training=${score_training}, score_checkin=${score_checkin}, score_service=${score_service}, score_visitor=${score_visitor}, score_others=${score_others}, rank=${idx + 1}`;
				})

				execTrans(sqlArr, (err) => {
					if (err) {
						timerError(err);
						return;
					}
					timerSuccess(`工厂: ${id}, 更新完成-全部`)
				})
			})

			// 根据年份，统计每年内的
			const lastYearDone = re[1][0].count > 0; // 上一年是否统计过
			for (let year = currentYear === 2023 ? 2023 : currentYear - (lastYearDone ? 0 : 1); year <= currentYear; year++) {
				conn.query(`select sum(ifnull(module1, 0) + ifnull(module2, 0) + ifnull(module3, 0) + ifnull(module4, 0) + ifnull(module5, 0) + ifnull(module6, 0) + ifnull(module7, 0)) as score, jobnum from ${databasePre}allpoint_data where factory_id='${id}' group by jobnum order by jobnum;

				select sum(points) as score, jobnum from ${databasePre}checkin_log where factory_id='${id}' and create_time>=STR_TO_DATE('${year}-01-01', '%Y-%m-%d') and create_time<STR_TO_DATE('${year+1}-01-01', '%Y-%m-%d') group by jobnum order by jobnum;

				select ifnull(sum(points), 0) as score, jobnum from ${databasePre}service_log where factory_id='${id}' and is_del=0 and check_status<2 and update_time>=STR_TO_DATE('${year}-01-01', '%Y-%m-%d') and update_time<STR_TO_DATE('${year+1}-01-01', '%Y-%m-%d') group by jobnum order by jobnum;
			
				SELECT r.jobnum, r.sum2 as score FROM ${databasePre}visitor_data r
				JOIN (
					SELECT jobnum, MAX(id) AS max_id
					FROM ${databasePre}visitor_data
					where factory_id=${id} and sum2 != 0 and create_time>=STR_TO_DATE('${year}-01-01', '%Y-%m-%d') and create_time<STR_TO_DATE('${year+1}-01-01', '%Y-%m-%d')
					GROUP BY jobnum
				) latest
				ON r.jobnum = latest.jobnum AND r.id = latest.max_id
				where factory_id=${id};

				select ifnull(sum(score), 0) as score, jobnum from ${databasePre}user_points where factory_id='${id}' and type=0 and create_time>=STR_TO_DATE('${year}-01-01', '%Y-%m-%d') and create_time<STR_TO_DATE('${year+1}-01-01', '%Y-%m-%d') group by jobnum order by jobnum;`, (err, result) => {
					if (err) {
						timerError(err);
						return;
					}

					const jobnumObj = {};
					result.forEach(list => {
						list.forEach(({ jobnum, score }) => {
							jobnum = jobnum.toUpperCase();
							if (jobnumObj[jobnum] === undefined) {
								jobnumObj[jobnum] = {};
							}

							jobnumObj[jobnum][keyArr[idx]] = score;
							jobnumObj[jobnum].score_total = (jobnumObj[jobnum].score_total || 0) + score;
						})
					})

					// 按总积分倒序
					const sqlArr = Object.entries(jobnumObj).sort((a, b) => {
						return b[1] - a[1];
					}).map(([jobnum, { score_total, score_training, score_checkin, score_service, score_visitor, score_others }], idx) => {
						jobnum = jobnum.toUpperCase();
						return `insert into ${databasePre}user_score_rank(factory_id, jobnum, year, score_total, score_training, score_checkin, score_service, score_visitor, score_others, rank) values(${id}, '${safeString(jobnum)}', '${year}', ${score_total}, ${score_training}, ${score_checkin}, ${score_service}, ${score_visitor}, ${score_others}, ${idx + 1}) on duplicate key update score_total=${score_total}, score_training=${score_training}, score_checkin=${score_checkin}, score_service=${score_service}, score_visitor=${score_visitor}, score_others=${score_others}, rank=${idx + 1}`;
					})

					execTrans(sqlArr, (err) => {
						if (err) {
							timerError(err);
							return;
						}
						timerSuccess(`工厂: ${id}, 更新完成-${year}`)
					})
				})
			}
		})
	})
}
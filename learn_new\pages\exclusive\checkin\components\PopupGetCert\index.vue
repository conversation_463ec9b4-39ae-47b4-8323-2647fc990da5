<template>
	<!-- 填写证书 -->
	<uni-popup
		class="popup-certname tac"
		ref="getCertRef"
		type="center"
		mask-background-color="rgba(0,0,0,0.5)"
		:is-mask-click="false">
		<view class="box">
			<view class="star star1"></view>
			<view class="star star2"></view>
			<view class="star star3"></view>

			<view class="title fwb f38 mt50 pt20">
				<view>恭喜你获得</view>
				<text>「</text>
				<text class="f_c_orange">{{ codeName }}</text>
				<text>」证书！</text>
			</view>
			<input
				type="text"
				placeholder="请输入姓名"
				maxlength="15"
				v-model="certName" />
			<view class="f32 c_a mt30 fwb">请输入你的姓名，获取专属证书。</view>
			<view
				class="submit ui-button-orange"
				@click="getCert">
				获取证书
			</view>
		</view>
	</uni-popup>

	<!-- 获得证书 -->
	<PopupCert
		:img-src="imgSrc"
		:cert-name="certName"
		mode="checkin"
		v-model="popupCertVisible" />
</template>

<script setup name="PopupGetCert">
/**
 * @description 知识卡片弹窗
 */
import { ref } from 'vue';
import { postError, Toast } from '@/utils/tools.js';
import PopupCert from '@/components/PopupCert/index.vue';

const props = defineProps({
	/** 证书的图片 */
	imgSrc: {
		type: String
	},

	/** 活动代号 */
	codeName: {
		type: String
	},

	/** 期数ID */
	periodId: {
		type: [Number, String]
	},

	/** 测试试卷ID */
	testId: {
		type: [Number, String]
	}
});
const emit = defineEmits(['update:modelValue']);

const getCertRef = ref();
const popupCertVisible = ref(false); // 是否展示证书
const certName = ref(''); // 证书名称

function open() {
	getCertRef.value.open();
}
defineExpose({ open });

// 获取证书
async function getCert() {
	if (!certName.value) {
		Toast('请输入您的姓名');
		return;
	}

	// 提交证书
	const { code, data, msg } = await ajax({
		url: 'checkin/getcert',
		method: 'post',
		showLoading: true,
		data: {
			cert_name: certName.value,
			period_id: props.periodId,
			test_id: props.testId
		}
	});

	// 异常处理
	if (code) {
		postError(msg)
			.then(getCert)
			.catch(() => {});
		return;
	}
	getCertRef.value.close();
	popupCertVisible.value = true;
}
</script>

<style lang="scss">
@import './index';
</style>

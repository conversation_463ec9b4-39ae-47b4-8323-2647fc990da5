/**
 * @description 百日打卡-每天的任务情况
 */

module.exports = (req, res) => {
	let json = {
		code: 0,
		data: {},
		msg: '',
	};
	
	const login_jobnum = safeString(req.headers.login_jobnum);
	const factoryid = parseInt(req.headers.factoryid);
	const period_id = parseInt(req.query.period_id);
	let days_num = parseInt(req.query.days_num) || 0; // 第几天
	
	if (!factoryid || !period_id) {
		json.code = 201;
		json.msg = '缺少参数';
		res.json(json);
		return;
	}

	querySql(`select * from ${databasePre}checkin_period_conf where id=${period_id} and state=1;

	select days_sum, create_time from ${databasePre}checkin_log where factory_id=${factoryid} and period_id=${period_id} and jobnum='${login_jobnum}' order by id desc;
	
	select * from ${databasePre}checkin_chapter where period_id=${period_id} and state=1 and is_del=0 order by sort_num asc;
	
	select * from ${databasePre}checkin_days_conf where period_id=${period_id} order by chapter_id asc, sort_num asc;`, (err, result, conn) => {
		if (err) {
			res.json(err);
			return;
		}

		const period = result[0][0];
		if (!period) {
			json.code = 202;
			json.msg = '未查询到数据';
			res.json(json);
			return;
		}

		const now = Date.now();
		const last = result[1][0];
		const first = result[1].length ? result[1][result[1].length - 1] : null;
		const days_total = period.days;
		const today = new Date(timeFormatter(undefined, 'Y-m-d 00:00:00')).getTime();
		const startTime = first ? new Date(timeFormatter(first.create_time.getTime(), 'Y-m-d 00:00:00')).getTime() : today;
		const endTime = first ? startTime + days_total * 24 * 3600 * 1000 : period.end_time.getTime();
		const diffDays = Math.ceil(((now >= endTime ? (endTime - 1) : now) - startTime) / (3600 * 24 * 1000)) // 活动已经开始的天数
		if (days_num > days_total || days_num > diffDays + 1) {
			json.code = 203;
			json.msg = '禁止查询数据';
			res.json(json);
			return;
		}

		// 返回第几天的任务数据
		if (!days_num) {
			days_num = last?.days_sum || 0;
		}

		// console.log('last?.days_sum', last?.days_sum);
		// console.log('diffDays', diffDays);
		const is_checked = last?.days_sum >= diffDays; // 最后一天是否打卡
		const disabled = now < period.start_time.getTime() || now >= endTime; // 是否不能打卡
		
		let sql = '';
		let tempDaysSum = 0;
		// console.log('result[2]', result[2]);
		// console.log('days_num', days_num);
		// console.log('is_checked', is_checked);
		let curChapter = {};
		let daysIdx = 0; // 章节的第几天
		for (let i = 0; i < result[2].length; i++) {
			const o = result[2][i];

			const flag = days_num >= tempDaysSum && (days_num < tempDaysSum + o.days_sum || (is_checked && days_num == tempDaysSum + o.days_sum));
			// console.log('flag', flag, days_num, tempDaysSum, tempDaysSum + o.days_sum, is_checked, days_total);

			if (flag) {
				curChapter = o;
				if (is_checked) {
					// 当天已经打卡、活动已结束
					// curChapter = result[2][i > 0 ? i - 1 : 0];
					daysIdx = days_num - tempDaysSum - 1;
					// if (days_num - tempDaysSum == 0) {
					// 	daysIdx--;
					// }
				} else {
					// curChapter = o;
					daysIdx = days_num - tempDaysSum;
				}
				break;
			} else {
				tempDaysSum += o.days_sum;
			}
		}
		// const curChapter = result[2].find(o => {
		// 	const flag = days_num >= tempDaysSum && (days_num < tempDaysSum + o.days_sum || (days_num === days_total && days_num == tempDaysSum + o.days_sum));
		// 	console.log('flag', flag, days_num, tempDaysSum, tempDaysSum + o.days_sum, days_total);
		// 	!flag && (tempDaysSum += o.days_sum);
		// 	return flag;
		// })
		// console.log('curChapter', curChapter);

		json.data = {
			is_checked,
			chapter_id: curChapter.id,
			chapter_title: curChapter.title,
			checkin_type: curChapter.checkin_type,
			days_type: curChapter.days_type,
			disabled,
		}
		
		// const diffDay = days_num - tempDaysSum;
		// const taskIdx = days_num === days_total ? diffDay - 1 : diffDay;
		if (curChapter.days_type === 1) {
			// 分别设置
			const daysInfo = result[3].filter(o => o.chapter_id === curChapter.id)[daysIdx] || {};
			delete daysInfo.sort_num;
			// console.log('daysInfo', daysInfo);
			json.data = {
				...json.data,
				...daysInfo,
			}
			if (daysInfo.task_type === 1) {
				// 音频练习
				res.json(json);
				return;
			} else {
				// 日常任务
				sql = `select * from ${databasePre}checkin_task where id in(${daysInfo.task_list})`;
			}
		} else {
			// 统一设置
			if (!curChapter.task_list) {
				res.json(json);
				return;
			}

			if (curChapter.checkin_type === 1) {
				// 固定任务
				sql = `select * from ${databasePre}checkin_task where id=${curChapter.task_list.split(',')[daysIdx]}`;
			} else {
				// 任选任务
				sql = `select * from ${databasePre}checkin_task where id in(${curChapter.task_list})`;
			}
		}

		conn.query(sql, (err, re) => {
			if (err) {
				res.json(err);
				return;
			}

			json.data.task_list = re;
			res.json(json);
		})
	})
}
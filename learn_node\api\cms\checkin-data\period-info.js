
module.exports = (req, res) => {
	let json = {
		code: 0,
		data: {},
		msg: '',
	};

	const user_group = req.headers.user_group === undefined ? 1 : parseInt(req.headers.user_group); // 用户组，0超级管理员，1工厂管理员

	/* 
		TODO:
		根据用户关联到的user_group和factory_id来鉴权
	 */
	let factory_id = '';
	if (user_group === 1) {
		// 工厂管理员、筛选当前工厂的
		factory_id = parseInt(req.headers.factoryid);
	} else {
		factory_id = parseInt(req.query.factory_id) || parseInt(req.body.factory_id) || 0;
	}

	const period_id = parseInt(req.query.period_id) || 0;
	if (!period_id || !factory_id) {
		json.code = 201;
		json.msg = '缺少参数';
		res.json(json);
		return;
	}

	let where = '';
	const arr = [
		`c.factory_id='${factory_id}'`,
		`c.id='${period_id}'`,
	];

	if (arr.length) {
		where = `where ${arr.join(' and ')}`;
	}

	querySql(`select c.code_name, c.id, c.start_time, c.end_time, c.days, (select count(distinct jobnum) from ${databasePre}checkin_log where period_id=c.id) as join_nums, (select count(distinct jobnum) from ${databasePre}cert where checkin_period_id=c.id and type=1) as complete_nums from ${databasePre}checkin_period_conf c ${where} order by id desc`, (err, result) => {
		if (err) {
			res.json(err);
			return;
		}

		json.data = result[0];
		res.json(json);
	});
}
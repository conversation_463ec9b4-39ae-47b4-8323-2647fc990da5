import { Controller, Get, Query } from '@nestjs/common';

import { CompleteQueryDto, InvolvementQueryDto, EmotionQueryDto } from '../dto/Checkin';
import { CheckinService } from '../services/Checkin';

@Controller('checkin')
export class CheckinController {
  constructor(private readonly checkinService: CheckinService) {}

  /** 员工完成情况 */
  @Get('complete')
  async getComplete(@Query() query: CompleteQueryDto): Promise<ResObj> {
    return this.checkinService.getComplete(query);
  }

  /** 心态评估测试 */
  @Get('test')
  async getTest(@Query() query: CompleteQueryDto): Promise<ResObj> {
    return this.checkinService.getTest(query);
  }

  /** 内容参与情况-明细 */
  @Get('logs')
  async getLogs(@Query() query: InvolvementQueryDto): Promise<ResObj> {
    return this.checkinService.getLogs(query);
  }

  /** 内容参与情况 */
  @Get('involvement')
  async getInvolvement(@Query() query: InvolvementQueryDto): Promise<ResObj> {
    return this.checkinService.getInvolvement(query);
  }

  /** 情绪打卡 */
  @Get('emotion')
  async getEmotion(@Query() query: EmotionQueryDto): Promise<ResObj> {
    return this.checkinService.getEmotion(query);
  }
}

/**
 * @description 积分与成就 - 勋章与证书
 */

module.exports = (req, res) => {
	let json = {
		code: 0,
		data: {},
		msg: '',
	};

	const user_group = req.headers.user_group === undefined ? 1 : parseInt(req.headers.user_group); // 用户组，0超级管理员，1工厂管理员

	/* 
		TODO:
		根据用户关联到的user_group和factory_id来鉴权
	 */
	let factory_id = '';
	if (user_group === 1) {
		// 工厂管理员、筛选当前工厂的
		factory_id = parseInt(req.headers.factoryid);
	} else {
		factory_id = parseInt(req.query.factory_id) || parseInt(req.body.factory_id) || 0;
	}
	if (!factory_id) {
		json.code = 210;
		json.msg = '缺少参数';
		res.json(json);
		return;
	}

	var page_num = parseInt(req.query.page_num) || 1;
	var page_size = parseInt(req.query.page_size) || 15;
	var jobnum = safeString(req.query.jobnum);
	var start_time = safeString(req.query.start_time);
	var end_time = safeString(req.query.end_time);
	
	var arrMedal = [`factory_id='${factory_id}'`];
	var arrCert = [`factory_id='${factory_id}'`];

	if (start_time) {
		arrMedal.push(`create_time>=STR_TO_DATE('${start_time}', '%Y-%m-%d')`);
		arrCert.push(`issue_time>=STR_TO_DATE('${start_time}', '%Y-%m-%d')`);
	}
	if (end_time) {
		arrMedal.push(`create_time<=STR_TO_DATE('${end_time} 23:59:59', '%Y-%m-%d %H:%i:%S')`);
		arrCert.push(`issue_time<=STR_TO_DATE('${end_time} 23:59:59', '%Y-%m-%d %H:%i:%S')`);
	}

	var whereMedal = `where ${arrMedal.join(' and ')}`;
	var whereCert = `where ${arrCert.join(' and ')}`;
	
	const userWhere = `where factory_id=${factory_id} and ${jobnum ? `jobnum='${jobnum}'` : `jobnum != ''`}`;
	querySql(`select count(1) as total from ${databasePre}user ${userWhere};
	select jobnum from ${databasePre}user ${userWhere} order by id limit ${(page_num - 1) * page_size}, ${page_size};`, (err, result, conn) => {
		if (err) {
			res.json(err);
			return;
		}
		
		const total = result[0][0]?.total || 0;
		const userList = result[1];
		if (!userList.length) {
			res.json(json);
			return;
		}
		const jobnumStr = userList.map(o => `'${o.jobnum}'`).join();

		conn.query(`SELECT m.* FROM ${databasePre}medal m
		JOIN (
			SELECT jobnum, type, MAX(id) AS max_id
			FROM ${databasePre}medal
			${whereMedal} and jobnum in(${jobnumStr})
			GROUP BY concat(jobnum, '-', type)
		) latest
		ON m.jobnum=latest.jobnum AND m.type=latest.type AND m.id=latest.max_id
		where m.factory_id=${factory_id};
		
		SELECT c.* FROM ${databasePre}cert c
		JOIN (
			SELECT jobnum, type, MAX(id) AS max_id
			FROM ${databasePre}cert
			${whereCert} and jobnum in(${jobnumStr})
			GROUP BY concat(jobnum, '-', type)
		) latest
		ON c.jobnum=latest.jobnum AND c.type=latest.type AND c.id=latest.max_id
		where c.factory_id=${factory_id};`, (err, result) => {
			if (err) {
				res.json(err);
				return;
			}

			const [medalList, certList] = result;
			const jobnumObj = {};
			userList.forEach(({ jobnum }) => {
				jobnumObj[jobnum] = {
					training: [],
					checkin: [],
					service: [],
				}
			});

			[...medalList, ...certList].forEach(o => {
				const obj = jobnumObj[o.jobnum];
				if (!obj) return;

				let keyMap = ['training', 'checkin', 'service'];
				obj[keyMap[o.type]].push({
					id: o.id,
					cover: o.cover,
					name: o.issue_time ? o.cert_name : o.medal_name,
					type: o.issue_time ? 'cert' : 'medal',
				})
			})

			const list = Object.entries(jobnumObj).map(([jobnum, obj]) => {
				obj.jobnum = jobnum;
				return obj;
			})
			json.data.total = total;
			json.data.page_num = page_num;
			json.data.page_size = page_size;
			json.data.list = list;
			res.json(json);
		});
	});
}
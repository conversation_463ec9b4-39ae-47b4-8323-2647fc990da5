/**
 * <AUTHOR>
 * @description 百日打卡-提交打卡
 */

module.exports = (req, res) => {
	let json = {
		code: 0,
		data: {},
		msg: '',
	};
	
	const login_jobnum = safeString(req.headers.login_jobnum);
	const factoryid = parseInt(req.headers.factoryid);
	const chapter_id = parseInt(req.body.chapter_id);
	const task_id = parseInt(req.body.task_id) || 0;
	const days_conf_id = parseInt(req.body.days_conf_id);
	const experience = safeString(req.body.experience);
	const img_keys = safeString(req.body.img_keys);
	
	if (
		!factoryid ||
		!chapter_id ||
		(!days_conf_id && !task_id) ||
		!login_jobnum ||
		(!experience.trim() && !img_keys)
	) {
		json.code = 101;
		json.msg = '缺少参数';
		res.json(json);
		return;
	}

	querySql(`select p.id, p.start_time, p.end_time, p.days, c.period_id, c.image_text_color, c.bg_img, c.complete_tips, c.card_img, c.title, m.title as medal_title, m.cover as medal_cover, m.intro as medal_intro, c.sort_num, c.days_sum, c.checkin_type, c.days_type, (select sum(days_sum) from ${databasePre}checkin_chapter where period_id=c.period_id and sort_num<=c.sort_num and is_del=0) as days_total from ${databasePre}checkin_period_conf p, ${databasePre}checkin_chapter c left join ${databasePre}checkin_medal m on m.id=c.medal_id where c.factory_id=${factoryid} and c.id=${chapter_id} and c.period_id=p.id and p.state=1;
	
	select l.days_sum, l.days_continue, l.checkin_date, l.create_time from ${databasePre}checkin_log l, ${databasePre}checkin_chapter c where c.id=${chapter_id} and l.period_id=c.period_id and l.jobnum='${login_jobnum}' and c.factory_id=${factoryid} order by l.id desc;
		
	select sum(l.points) as points_total from ${databasePre}checkin_log l, ${databasePre}checkin_chapter c where l.period_id=c.period_id and c.id=${chapter_id} and l.factory_id=${factoryid} and l.jobnum='${login_jobnum}';
	
	select complete_tips from ${databasePre}checkin_task where factory_id=${factoryid} and id=${task_id};
	
	select count(l.id) as count from ${databasePre}checkin_log l, ${databasePre}checkin_chapter c where l.period_id=c.period_id and c.id=${chapter_id} and l.factory_id=${factoryid} and l.jobnum='${login_jobnum}' and l.task_id=${task_id};
	
	select complete_tips from ${databasePre}checkin_complete_tips where factory_id=${factoryid} and chapter_id=${chapter_id} order by id asc;
	
	select days_sum, id from ${databasePre}checkin_chapter where period_id=(select period_id from ${databasePre}checkin_chapter where factory_id=${factoryid} and id=${chapter_id}) order by sort_num asc;
	
	${days_conf_id > 0 ? `select * from ${databasePre}checkin_days_conf where id=${days_conf_id};` : ''}`, (err, result, conn) => {
		if (err) {
			res.json(err);
			return;
		}

		const daysConf = result[7]?.[0] || {};
		
		if (daysConf.chapter_id !== chapter_id) {
			json.code = 201;
			json.msg = '章节不符';
			res.json(json);
			return;
		}

		const now = Date.now();
		const activity = result[0][0];
		const first = result[1].length ? result[1][result[1].length - 1] : null;
		const today = new Date(timeFormatter(undefined, 'Y-m-d 00:00:00')).getTime();
		const startTime = first ? new Date(timeFormatter(first.create_time.getTime(), 'Y-m-d 00:00:00')).getTime() : today;
		const endTime = first ? startTime + activity.days * 24 * 3600 * 1000 : activity.end_time.getTime();
		if (!activity || (activity.start_time.getTime() > now || endTime <= now)) {
			json.code = 202;
			json.msg = '活动未开放';
			res.json(json);
			return;
		}

		const checkinLog = result[1][0];
		let days_sum = 1; // 累计打卡
		let days_continue = 0; // 连续打卡
		let points = 1; // 获得的积分
		let type = 0; // 打开类型：0正常打卡 1补卡
		let checkin_date = timeFormatter(undefined, 'Y-m-d'); // 打哪一天的卡

		const diffDays = Math.ceil((now - startTime) / (3600 * 24 * 1000)) // 活动已经开始的天数

		if (checkinLog) {
			// 有打卡记录

			days_sum = checkinLog.days_sum + 1;
			if (days_sum > diffDays) {
				json.code = 203;
				json.msg = '该日还未解锁打卡';
				res.json(json);
				return;
			}

			if (diffDays - checkinLog.days_continue > 1) {
				// 中断过打卡

				if (diffDays === days_sum) {
					// 最后一天（打卡活动开始后的第N天）

					const today = new Date(timeFormatter(undefined, 'Y-m-d 00:00:00'));
					if (
						checkinLog.create_time >= today - 3600 * 24 * 1000 &&
						checkinLog.create_time < today
					) {
						// 上次打卡是昨天
						days_continue = checkinLog.days_continue + 1;
						points = days_continue;
					} else {
						// 上次打卡有中断
						days_continue = 1;
					}
				} else {
					// 非最后一天，补卡
					type = 1;
					checkin_date = timeFormatter(checkinLog.checkin_date.getTime() + 3600 * 24 * 1000, 'Y-m-d');
				}
			} else {
				// 一直都是连续打卡
				days_continue = checkinLog.days_continue + 1;
				points = days_continue;
			}
		} else {
			// 第一次打卡
			if (diffDays > 1) {
				// 开始几天后才开始打卡，补卡
				type = 1;
				checkin_date = timeFormatter(startTime, 'Y-m-d');
			} else {
				// 活动第一天
				days_continue = 1;
			}
		}

		// 最多10积分
		// if (days_continue > 10) {
		// 	points = 10;
		// }

		// 临时修改，最多改成5积分
		if (days_continue > 5) {
			points = 5;
		}

		// 如果章节和天数对应不上
		if (days_sum > activity.days_total || days_sum <= activity.days_total - activity.days_sum) {
		// if (days_sum > activity.sort_num * 10 || days_sum <= (activity.sort_num - 1) * 10) {
			json.code = 204;
			json.msg = '打卡章节不符';
			res.json(json);
			return;
		}

		// 插入打卡记录
		const tipsArr = result[5];
		let card_tips = result[3][0]?.complete_tips || '';
		if (activity.days_type === 1) {
			// 天数-分别设置
			if (tipsArr[daysConf.sort_num - 1]?.complete_tips) {
				card_tips = tipsArr[daysConf.sort_num - 1]?.complete_tips;
			}
			// if (daysConf.task_type === 1) {
			// 	// 音频练习（不用选任务）
			// 	if (result[5][0]?.complete_tips) {
			// 		card_tips = result[5][0].complete_tips;
			// 	}
			// }
		} else {
			// 天数-统一设置
			if (result[4][0]?.count >= 1 && tipsArr.length) {
				const idx = Math.floor(Math.random() * tipsArr.length);
				card_tips = tipsArr[idx].complete_tips;
			}
		}

		const points_total = (result[2][0].points_total || 0) + points;

		const sqlArr = [
			// 插入打卡记录
			`insert into ${databasePre}checkin_log(period_id, chapter_id, task_id, factory_id, jobnum, create_time, checkin_date, type, points, days_sum, days_continue, experience, img_keys, card_tips, days_type, task_type, days_conf_id) values(${activity.period_id}, ${chapter_id}, ${task_id || null}, ${factoryid}, '${login_jobnum}', now(), '${checkin_date}', ${type}, ${points}, ${days_sum}, ${days_continue}, '${experience}', '${img_keys}', '${card_tips}', ${activity.days_type}, ${daysConf.task_type}, ${days_conf_id || null});`,

			// 插入排名数据/更新总积分
			`insert into ${databasePre}checkin_rank(factory_id, jobnum, period_id, points_total, update_time, last_checkin_time) values(${factoryid}, '${login_jobnum}', '${activity.period_id}', '${points}', now(), now()) on duplicate key update points_total=points_total+${points}, last_checkin_time=now()`,

			// 更新全部用户的排名数据
			`update ${databasePre}checkin_rank a
			INNER JOIN (
				select t.id, @i:=@i+1 as rankNum from
				(select @i:=0) r,
				(select *	from ${databasePre}checkin_rank where factory_id=${factoryid} and period_id=${activity.period_id} order by points_total desc, last_checkin_time asc) as t
			) t1
			on a.id = t1.id 
			set rank = rankNum;`,
			
			`select rank from ${databasePre}checkin_rank where factory_id=${factoryid} and period_id=${activity.period_id} and jobnum='${login_jobnum}'`
		];

		// 计算完成当前章节需要的总天数
		let currentChapterCompleteDays = 0;
		for (const obj of result[6]) {
			currentChapterCompleteDays += obj.days_sum;
			if (obj.id === chapter_id) {
				break;
			}
		}

		let medalInfo = {};
		let chapter_complete = false;
		// 如果打卡完今天就完成本章节
		if (days_sum >= currentChapterCompleteDays) {
			chapter_complete = true;

			// 本章节有勋章
			if (activity.medal_cover) {
				medalInfo = {
					medal_title: activity.medal_title,
					medal_cover: activity.medal_cover,
					medal_intro: activity.medal_intro,
				}

				sqlArr.push(`insert into ${databasePre}medal(factory_id, jobnum, type, cover, medal_name, checkin_chapter_id, create_time) values(${factoryid}, '${login_jobnum}', 1, '${activity.medal_cover}', '${activity.medal_title}', '${chapter_id}', now())`);
			}
		}

		execTrans(sqlArr, (err, re) => {
			if (err) {
				res.json(err);
				return;
			}

			json.data = {
				period_id: activity.id,
				points_total,
				points,
				days_sum,
				sort_num: activity.sort_num,
				checkin_type: activity.checkin_type,
				days_continue,
				chapter_title: activity.title,
				complete_tips: activity.complete_tips,
				card_tips,
				image_text_color: activity.image_text_color,
				bg_img: activity.bg_img,
				card_img: activity.card_img,
				rank: re[3][0].rank,
				chapter_complete,
				...medalInfo,
			}
			
			res.json(json);
		})
	})
}
<template>
	<view
		class="visitor-topic swiper-question-wrap"
		:style="{ paddingTop: user.statusBarHeight }">
		<uni-icons
			type="back"
			class="nav-back"
			@click="goBack()"></uni-icons>

		<!-- 题目列表 -->
		<Question
			class="swiper-question"
			:list="questionList"
			@submit="submit"
			v-if="showQuestion" />

		<!-- 确认提交 弹窗 -->
		<uni-popup
			ref="confirmRef"
			type="center">
			<view class="popup-dialog">
				<text class="iconfont i-ring"></text>

				<view>提交后不可更改</view>

				<button
					class="iknow mt30"
					@click="doPost()">
					确认提交
				</button>
				<button
					class="iknow mt20"
					@click="confirmRef.close()">
					返回修改
				</button>
			</view>
		</uni-popup>

		<!-- 结果页 -->
		<uni-popup
			ref="resultRef"
			type="center">
			<view class="popup-fullscreen result-score">
				<view class="box">
					<view class="title">学习结果</view>
					<view class="score">
						<view
							class="nums"
							:class="{ full: score == 100 }">
							{{ score }}
						</view>
					</view>
					<view class="tips">
						{{ score >= 80 ? '恭喜你，心理健康的小达人 !' : '要对心理健康有更多了解哦 !' }}
					</view>
				</view>

				<view
					v-if="visitorDoneTimes == 2"
					class="button round light-reverse submit"
					@click="goSave()">
					<text class="ml10">一键下载【知识卡片】</text>
				</view>
				<view
					v-else
					class="button round light-reverse submit"
					@click="nextPage('resultRef', 'doneRef')">
					下一页
				</view>
			</view>
		</uni-popup>

		<!-- 完成弹框 -->
		<uni-popup
			ref="doneRef"
			type="center">
			<view class="popup-fullscreen jiexi done">
				<view class="title">学习完成</view>
				<view class="intro-box">
					<view class="answer">心理健康基础知识</view>
					<view class="info">你都做对了吗？通过知识点解析是否对心理健康多了一些了解？</view>
					<view class="again">让我们再来一次，测测你的学习效果吧！</view>
				</view>
				<view
					class="button small-submit"
					@click="nextPage('doneRef')">
					再来一次
				</view>
			</view>
		</uni-popup>

		<!-- 保存成功弹框 -->
		<uni-popup
			ref="savedRef"
			type="center">
			<view class="popup-fullscreen jiexi saved">
				<view class="title">感谢你的参与</view>
				<view class="intro-box">
					<view class="starts">
						<uni-icons
							type="heart-filled"
							size="32rpx"
							color="#79a7f2"></uni-icons>
						<uni-icons
							type="heart-filled"
							size="32rpx"
							color="#79a7f2"></uni-icons>
					</view>
					<view class="tips">
						<text>下载成功！</text>
						<view>【知识卡片】可保存学习，</view>
						<view>也可分享给你的同事亲友。</view>
						<view>感谢你的参与！</view>
					</view>
				</view>
				<view
					class="button small-submit"
					@click="goBack()">
					返回总目录
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { storeToRefs } from 'pinia';
import { useUserStore } from '@/stores/user';
import Question from '@/components/Question/index.vue';
import { goBack, Toast, setLocal, postError } from '@/utils/tools.js';

const { user } = storeToRefs(useUserStore());

const visitorDoneTimes = ref(uni.getStorageSync('visitorDoneTimes') || 0); // 已做完几遍
const questionList = ref([]);
const score = ref(0); // 得分
const showQuestion = ref(true);

const confirmRef = ref();
const resultRef = ref();
const doneRef = ref();
const answerList = ref([]); // 选择的答案
const savedRef = ref();
const refs = reactive({
	confirmRef,
	resultRef,
	doneRef,
	savedRef
});

async function getQuestionList(list) {
	if (!list) {
		const { code, data } = await ajax({
			url: 'api/visitor/test-info',
		});
		if (code) return;

		list = data;
	}

	questionList.value = list.map((o, i) => {
		// let obj = cloneDeep(o);
		let obj = JSON.parse(JSON.stringify(o));
		let chossNum = 0;
		answerMap.forEach((v) => {
			if (obj[v]) {
				chossNum++;
			} else {
				delete obj[v];
			}
		});
		obj.chossNum = chossNum;
		obj.tihao = i + 1;
		obj.topic = obj.title || obj.topic;
		delete obj.title;

		if (visitorDoneTimes.value === 0) {
			obj.jiexi = obj.analysis;
		} else {
			delete obj.jiexi;
		}
		delete obj.analysis;

		return obj;
	});	
}
getQuestionList();

function nextPage(current, next) {
	if (next) {
		refs[next].open();
		if (next === 'doneRef') {
			showQuestion.value = false;
		}
	} else {
		// 再次进入答题
		getQuestionList(questionList.value);
		showQuestion.value = true;
	}
	refs[current].close();
}

function submit(arr) {
	answerList.value = arr.map((o) => {
		return {
			id: o.id,
			value: o.checked[0],
		};
	});

	if (visitorDoneTimes.value > 0) {
		confirmRef.value.open();
	} else {
		doPost();
	}
}

// 游客数据提交
async function doPost() {	
	const { code, msg, data } = await ajax({
		url: 'api/visitor/test-submit',
		method: 'post',
		contentType: 'application/json',
		noLoad: false,
		repeat: false,
		data: {
			type: visitorDoneTimes.value > 0 ? 1 : 0,
			answer_list: answerList.value,
		}
	});

	// 异常处理
	if (code) {
		postError(msg)
			.then(() => {
				doPost();
			})
			.catch(() => {});
	} else {
		if (visitorDoneTimes.value < 2) {
			visitorDoneTimes.value++;
		}
		setLocal('visitorDoneTimes', visitorDoneTimes.value);

		score.value = data;
		confirmRef.value.close();
		resultRef.value.open();
	}
}

function goSave() {
	uni.getSetting({
		complete(res) {
			const auth = res.authSetting['scope.writePhotosAlbum'];
			if (auth) {
				// 已授权
				saveImage();
			} else if (auth === false) {
				// 已拒绝授权
				uni.showModal({
					title: '提示',
					content: '授权后才能保存，现在去授权？',
					success(res) {
						if (res.confirm) {
							uni.openSetting();
						}
					}
				});
			} else {
				// 未授权
				uni.authorize({
					scope: 'scope.writePhotosAlbum',
					success() {
						saveImage();
					},
					fail() {
						// 拒绝了
						uni.showModal({
							title: '提示',
							content: '同意授权才能保存哦',
							showCancel: false,
							success(res) {
							}
						});
					}
				});
			}
		}
	});
}

// 保存多张海报
function saveImage() {
	const imgList = [
		'https://www.employeehealth.cn/images/card1.jpg',
		'https://www.employeehealth.cn/images/card2.jpg',
		'https://www.employeehealth.cn/images/card3.jpg',
		'https://www.employeehealth.cn/images/card4.jpg',
		'https://www.employeehealth.cn/images/card5.jpg',
		'https://www.employeehealth.cn/images/card6.jpg',
		'https://www.employeehealth.cn/images/card7.jpg',
		'https://www.employeehealth.cn/images/card8.jpg',
		'https://www.employeehealth.cn/images/card9.jpg',
		'https://www.employeehealth.cn/images/card10.jpg'
	];
	const failedObj = {}; // 失败对象
	const fun = function (idx) {
		const url = imgList[idx];
		const filePath = `${wx.env.USER_DATA_PATH}/${Date.now()}${idx}.jpg`;
		uni.downloadFile({
			url,
			filePath,
			complete(res) {
				if (res.statusCode !== 200) {
					failedObj[idx] = true;
					if (idx < imgList.length - 1) {
						Toast(res.errMsg);
						fun(++idx);
					} else {
						uni.hideLoading();
						if (Object.keys(failedObj).length) {
							uni.showModal({
								title: '提示',
								content: '部分卡片保存失败',
								showCancel: false
							});
						} else {
							nextPage('resultRef', 'savedRef');
						}
					}
					return;
				}

				uni.saveImageToPhotosAlbum({
					filePath: res.filePath,
					success() {
						/* 删除缓存 */
						uni.getFileSystemManager().unlink({
							filePath,
							success(r) {}
						});
					},
					fail(err) {
						failedObj[idx] = true;
						Toast(err.errMsg);
					},
					complete() {
						if (idx < imgList.length - 1) {
							fun(++idx);
						} else {
							uni.hideLoading();
							if (Object.keys(failedObj).length) {
								uni.showModal({
									title: '提示',
									content: '部分卡片保存失败',
									showCancel: false
								});
							} else {
								nextPage('resultRef', 'savedRef');
							}
						}
					}
				});
			}
		});
	};
	fun(0);
	uni.showLoading({
		title: '下载中...',
		mask: true
	});
}
</script>

<style lang="scss">
@import 'index.scss';
</style>

<template>
	<scroll-view
		class="exclusive-checkin-task por"
		scroll-y
		:bounces="false"
		style="height: 100vh"
		enhanced>
		<Navback />

		<!-- 常规打卡 -->
		<template v-if="mode === 'normal'">
			<view
				class="ui-fixed-bg"
				:style="{ backgroundImage: realImgSrc(forwardInfo.chapter_img) ? `url('${realImgSrc(forwardInfo.chapter_img)}')` : 'none' }"></view>
			<view
				class="main-title"
				:style="{ marginTop: user.safePadHeight, color: forwardInfo.image_text_color }">
				<!-- 分别设置 -->
				<template v-if="taskInfo.days_type === 1">
					<view class="main-title__h1">Day {{ Number(taskInfo.chapter_diff_days || 0) + Number(taskInfo.sort_num) }}</view>
					<view class="main-title__h2">{{ taskInfo.chapter_title }}</view>
				</template>
				<!-- 统一设置 -->
				<template v-else>
					<view class="main-title__h1">{{ taskInfo.title }}</view>
					<view class="main-title__h2">今天是打卡任务第 {{ checkinInfo.days_sum + 1 }} 天</view>
				</template>
			</view>

			<view class="content-wrap">
				<!-- <view class="process-title">关卡进度</view>
				<view class="process">
					<view
						class="process-line"
						:style="{ width: (100 * (checkinInfo.days_sum - taskInfo.chapter_acc_days + taskInfo.chapter_days_sum)) / taskInfo.chapter_days_sum + '%' }"></view>
				</view>
				<view class="process-tips">当前任务为{{ forwardInfo.title }}</view> -->

				<!-- 今日打卡任务 -->
				<view class="task-detail ui-box-radius">
					<!-- <view class="title">今日学习</view> -->
					<view class="intro">
						{{ taskInfo.days_type === 1 ? `请收听【${taskInfo.audio_intro_name}】音频。` : taskInfo.intro }}
						<view v-if="taskInfo.task_type === 1 && taskInfo.audio_practice_name">请收听【{{ taskInfo.audio_practice_name }}】音频。</view>
						<view v-if="!multiVideo">完成后请点击【{{ taskInfo.task_type === 1 ? (taskInfo.audio_practice ? '进入练习' : '完成打卡') : '查看任务' }}】</view>
					</view>
					<!-- 音频 -->
					<view
						class="video df col c-c normal"
						v-if="!multiVideo && (taskInfo.intro_voice || taskInfo.audio_intro)">
						<!-- 进度条 -->
						<view class="process f24 df s-c">
							<text>{{ second2str(videoState[curVideoSrc]?.currentTime) }}</text>
							<slider
								class="slider"
								:value="videoState[curVideoSrc]?.currentTime || 0"
								@change="sliderChange(curVideoSrc, $event)"
								@changing="sliderChanging(curVideoSrc, $event)"
								block-size="16"
								background-color="#fff"
								active-color="#f19f67"
								block-color="#f19f67"
								min="0"
								:max="Math.round(videoState[curVideoSrc]?.duration) || 1" />
							<text>{{ second2str(videoState[curVideoSrc]?.duration) }}</text>
						</view>
						<!-- 播放器 -->
						<view class="df c-c">
							<view
								class="iconfont i-sub15"
								:class="{ disabled: !playVideoDone }"
								@click="seekVideo('minus', taskInfo.days_type === 1 ? taskInfo.audio_intro : taskInfo.intro_voice, taskInfo.title || taskInfo.chapter_title)"></view>
							<view
								class="iconfont play"
								:class="[videoState.isPlay ? 'i-pause' : 'i-play']"
								@click="playVideo(taskInfo.days_type === 1 ? taskInfo.audio_intro : taskInfo.intro_voice, taskInfo.title || taskInfo.chapter_title)"></view>
							<view
								class="iconfont i-add15"
								:class="{ disabled: !playVideoDone }"
								@click="seekVideo('add', taskInfo.days_type === 1 ? taskInfo.audio_intro : taskInfo.intro_voice, taskInfo.title || taskInfo.chapter_title)"></view>
						</view>
					</view>
				</view>

				<!-- 封面图 -->
				<image
					v-if="taskInfo.audio_intro_img && (taskInfo.task_type === 0 || !taskInfo.audio_practice)"
					:src="realImgSrc(taskInfo.audio_intro_img)"
					mode="widthFix"
					class="practice-img"></image>

				<view class="checkin-submit">
					<view class="checkin-submit_btn-wrap">
						<view
							class="checkin-submit_btn fwb"
							:class="{ disabled: taskInfo.disabled || (!multiVideo && taskInfo.days_type === 1 && !playVideoDone) }"
							@click="goCheckin">
							{{ multiVideo || playVideoDone ? (taskInfo.task_type === 1 ? (taskInfo.audio_practice ? '进入练习 ›' : '完成打卡') : '查看任务 ›') : '请完成音频收听' }}
						</view>
					</view>
				</view>
			</view>

			<!-- 打卡弹框 -->
			<uni-popup
				ref="popupRef"
				type="center"
				:is-mask-click="false">
				<view class="upload-popup ui-box-radius">
					<view class="title mb10">记录今日打卡心得</view>
					<view class="c_c mb30">(请至少完成一项)</view>
					<uni-easyinput
						type="textarea"
						:maxlength="250"
						v-model="experience"
						placeholder="请输入打卡心得"
						:styles="{ height: '320rpx' }"></uni-easyinput>

					<view class="mt30 upload">
						<uni-file-picker
							ref="fileUploadRef"
							file-mediatype="image"
							limit="6"
							title="上传照片（最多6张）"
							:sizeType="['compressed']"
							:auto-upload="false"
							@select="onSelectImg"
							@delete="onDeleteImg"></uni-file-picker>
					</view>

					<view
						class="submit"
						@click="submit">
						确认打卡
					</view>
				</view>
				<view
					class="iconfont i-close ui-popup-close"
					@click="popupRef.close()"></view>
			</uni-popup>
		</template>

		<!-- 打卡回顾 -->
		<template v-else>
			<view
				class="ui-fixed-bg"
				:style="{ backgroundImage: realImgSrc(forwardInfo.review_img) ? `url('${realImgSrc(forwardInfo.review_img)}')` : 'none' }"></view>
			<view
				class="main-title"
				:style="{ marginTop: user.safePadHeight, color: forwardInfo.image_text_color }">
				<view class="main-title__h1">训练营</view>
				<!-- <view class="main-title__h1">第 {{ taskInfo.days_sum }} 天</view> -->
				<view class="main-title__h1">Day {{ taskInfo.days_sum }}</view>
			</view>

			<view class="content-wrap review">
				<!-- 每天统一设置 -->
				<template v-if="taskInfo.days_type === 0">
					<view class="review-title">打卡任务回顾</view>
					<view class="review-intro">
						{{ taskInfo.intro }}
					</view>

					<view
						class="video df c-c"
						v-if="taskInfo.intro_voice">
						<view
							class="iconfont i-sub15"
							@click="seekVideo('minus', taskInfo.intro_voice, taskInfo.title || taskInfo.chapter_title)"></view>
						<view
							class="iconfont play"
							:class="[videoState.isPlay ? 'i-pause' : 'i-play']"
							@click="playVideo(taskInfo.intro_voice, taskInfo.title || taskInfo.chapter_title)"></view>
						<view
							class="iconfont i-add15"
							@click="seekVideo('add', taskInfo.intro_voice, taskInfo.title || taskInfo.chapter_title)"></view>
					</view>
				</template>

				<!-- 每天分别设置 -->
				<template v-else-if="taskInfo.days_type === 1">
					<view class="review-title">学习回顾</view>
					<view
						class="task-list f32"
						v-if="taskInfo.audio_intro">
						收听【{{ taskInfo.audio_intro_name }}】音频
					</view>

					<!-- 进度条 -->
					<view class="process f24 df s-c">
						<text>{{ second2str(videoState[taskInfo.audio_intro]?.currentTime) }}</text>
						<slider
							class="slider"
							:value="videoState[taskInfo.audio_intro]?.currentTime || 0"
							@change="sliderChange(taskInfo.audio_intro, $event)"
							@changing="sliderChanging(taskInfo.audio_intro, $event)"
							block-size="16"
							background-color="#E0D7CD"
							active-color="#f19f67"
							block-color="#f19f67"
							min="0"
							:max="Math.round(videoState[taskInfo.audio_intro]?.duration) || 1" />
						<text>{{ second2str(videoState[taskInfo.audio_intro]?.duration) }}</text>
					</view>
					<!-- 播放器 -->
					<view
						class="video df c-c"
						v-if="taskInfo.audio_intro">
						<view
							class="iconfont i-sub15"
							@click="seekVideo('minus', taskInfo.audio_intro, taskInfo.audio_intro_name)"></view>
						<view
							class="iconfont play"
							:class="[videoState.isPlay && curVideoSrc === taskInfo.audio_intro ? 'i-pause' : 'i-play']"
							@click="playVideo(taskInfo.audio_intro, taskInfo.audio_intro_name)"></view>
						<view
							class="iconfont i-add15"
							@click="seekVideo('add', taskInfo.audio_intro, taskInfo.audio_intro_name)"></view>
					</view>

					<view class="review-title mt30">练习回顾</view>
					<view
						class="task-list f32"
						v-if="taskInfo.audio_practice">
						完成【{{ taskInfo.audio_practice_name }}】练习
					</view>
					<!-- 任务列表 -->
					<template v-if="taskInfo.task_type === 0">
						<view class="task-list f32">完成日常任务（{{ NumberCN[taskInfo.task_list.length - 1] }}选一）</view>
						<view
							class="task-list-item"
							v-for="(o, i) in taskInfo.task_list"
							:key="i">
							{{ o.title }}：{{ o.intro }}
						</view>
					</template>

					<!-- 练习音频 -->
					<template v-else-if="taskInfo.audio_practice">
						<!-- 进度条 -->
						<view class="process f24 df s-c">
							<text>{{ second2str(videoState[taskInfo.audio_practice]?.currentTime) }}</text>
							<slider
								class="slider"
								:value="videoState[taskInfo.audio_practice]?.currentTime || 0"
								@change="sliderChange(taskInfo.audio_practice, $event)"
								@changing="sliderChanging(taskInfo.audio_practice, $event)"
								block-size="16"
								background-color="#E0D7CD"
								active-color="#f19f67"
								block-color="#f19f67"
								min="0"
								:max="Math.round(videoState[taskInfo.audio_practice]?.duration) || 1" />
							<text>{{ second2str(videoState[taskInfo.audio_practice]?.duration) }}</text>
						</view>
						<!-- 播放器 -->
						<view class="video df c-c">
							<view
								class="iconfont i-sub15"
								@click="seekVideo('minus', taskInfo.audio_practice, taskInfo.audio_practice_name)"></view>
							<view
								class="iconfont play"
								:class="[videoState.isPlay && curVideoSrc === taskInfo.audio_practice ? 'i-pause' : 'i-play']"
								@click="playVideo(taskInfo.audio_practice, taskInfo.audio_practice_name)"></view>
							<view
								class="iconfont i-add15"
								@click="seekVideo('add', taskInfo.audio_practice, taskInfo.audio_practice_name)"></view>
						</view>
					</template>
				</template>

				<!-- 打卡心得 -->
				<view class="task-detail ui-box-radius mt30">
					<!-- 日常任务 -->
					<view
						class="title fwb"
						v-if="taskInfo.days_type === 1 && taskInfo.task_type === 0">
						您选择的任务：{{ taskInfo.task_list.find((o) => o.id === taskInfo.task_id).title }}
					</view>
					<view class="title fwb mt10">打卡心得</view>
					<view class="intro">
						{{ taskInfo.experience || '未填写' }}
					</view>
					<view class="imgs">
						<image
							class="img"
							:src="realImgSrc(v)"
							mode="aspectFit"
							v-for="(v, i) in taskInfo.img_keys"
							:key="v"
							@click="viewPhoto(i)"></image>
					</view>
				</view>

				<view
					class="view-card"
					:style="{ backgroundImage: forwardInfo.card_thumb ? `url(${realImgSrc(forwardInfo.card_thumb)})` : 'none', color: forwardInfo.image_text_color }"
					@click="showCardPopup = true">
					查看今日知识卡片 ›
				</view>
			</view>

			<!-- 相册预览 -->
			<CoverList
				:img-list="taskInfo.img_keys"
				theme="white"
				ref="coverListRef" />
		</template>
	</scroll-view>

	<!-- 知识卡片 -->
	<PopupCard
		v-if="taskInfo.card_img"
		v-model="showCardPopup"
		:cardBg="taskInfo.card_img"
		:cardTips="taskInfo.card_tips"
		:chapterTitle="taskInfo.chapter_title"
		:tipsColor="taskInfo.image_text_color" />
</template>

<script setup name="CheckinTask">
/**
 * @description 打卡任务页
 */
import { onLoad, onUnload, onShow, onHide } from '@dcloudio/uni-app';
import { computed, nextTick, onMounted, reactive, ref, watchEffect } from 'vue';
import { storeToRefs } from 'pinia';
import { useUserStore } from '@/stores/user';
import { useCheckinInfoStore } from '@/stores/checkin-info.js';
import { goBack, navigate, redirect, Toast, realImgSrc, getLocal, setLocal, isCurPage } from '@/utils/tools.js';
import { NumberCN } from '@/utils/data-map.js';
import { encodeData, decodeData } from '@/utils/forward-data.js';
import { useQiniuUptoken } from '@/stores/qiniu-uptoken';
import { useLoading } from '@/hooks/loading.js';
import second2str from '@/utils/second-to-str.js';

import Navback from '@/components/Navback/index.vue';
import CoverList from '@/components/CoverList/index.vue';
import PopupCard from '../components/PopupCard/index.vue';

const { user } = storeToRefs(useUserStore());
const { checkinInfo } = storeToRefs(useCheckinInfoStore());

const { uptoken } = storeToRefs(useQiniuUptoken());
const { getUptoken } = useQiniuUptoken();
const { loadStart, loadEnd } = useLoading();

const mode = ref('normal'); // 展示模式：normal|review(回顾)
const taskInfo = ref({});
const showCardPopup = ref(false); // 知识卡片弹框是否显示

const PagePath = 'pages/exclusive/checkin/task/index';

const coverListRef = ref();
const popupRef = ref();
const fileUploadRef = ref();

// 是否展示成多音频页面
const multiVideo = computed(() => {
	return taskInfo.value.days_type === 1 && taskInfo.value.task_type === 1 && taskInfo.value.audio_practice;
});

// 获取打卡任务详情
async function getTaskInfo() {
	const { code, data } = await ajax({
		url: 'api/checkin/task-info',
		data: {
			task_id: taskId.value,
			days_conf_id: daysConfId.value,
			chapter_id: chapterId.value
		}
	});
	if (code) return;

	forwardInfo.value.chapter_img = data.chapter_img;
	forwardInfo.value.image_text_color = data.image_text_color;
	data.days_conf_id = daysConfId.value;
	data.chapter_id = chapterId.value;
	taskInfo.value = data;

	if (data.days_type === 1) {
		curVideoSrc.value = data.audio_intro;
		videoState[data.audio_intro] = {
			duration: data.audio_intro_duration
		};
	} else {
		curVideoSrc.value = data.intro_voice;
		videoState[data.intro_voice] = {
			duration: data.intro_voice_duration
		};
	}

	videoState[curVideoSrc.value].currentTime = processVideo.value;
	// 便于在点播放按钮时判断
	videoState[curVideoSrc.value].ended = true;
}
// 获取打卡回顾详情
async function getCheckinReview() {
	const { code, data } = await ajax({
		url: 'checkin/review-info',
		data: {
			log_id: logId.value
		}
	});
	if (code) return;

	forwardInfo.value = {
		review_img: data.review_img,
		image_text_color: data.image_text_color,
		card_thumb: data.card_thumb
	};

	data.img_keys = data.img_keys ? data.img_keys.split(',') : [];
	taskInfo.value = data;

	// 初始化总时长
	if (data.days_type === 1) {
		// initVideo(data.audio_intro, data.audio_intro_name);
		videoState[data.audio_intro] = {
			duration: data.audio_intro_duration
		};
		if (data.audio_practice) {
			videoState[data.audio_practice] = {
				duration: data.audio_practice_duration
			};
		}
		// if (data.audio_practice) {
		// 	// 需要等第一个音频加载完
		// 	(function fun() {
		// 		if (!videoState[data.audio_intro]?.duration) {
		// 			setTimeout(fun, 100);
		// 			return;
		// 		}
		// 		setTimeout(() => {
		// 			videoState.isPlay = null; // 避免第二个音频播放中一闪而过
		// 			initVideo(data.audio_practice, data.audio_practice_name);
		// 		}, 120);
		// 	})();
		// }
	} else {
		// initVideo(data.intro_voice, data.title || data.chapter_title);
		videoState[data.intro_voice] = {
			duration: data.intro_voice_duration
		};
	}
}

const experience = ref(''); // 打卡心得
const uploadImgs = ref([]); // 上传的图片
function goCheckin() {
	if (taskInfo.value.disabled || (!multiVideo.value && taskInfo.value.days_type === 1 && !playVideoDone.value)) {
		return;
	}

	if (taskInfo.value.days_type === 1 && (taskInfo.value.task_type === 0 || taskInfo.value.audio_practice)) {
		// 分别设置 && (日常任务 or 有练习音频)
		// 跳任务选择/音频收听
		redirect('/exclusive/checkin/task-audio', {
			forward_info: encodeData({
				...taskInfo.value,
				chapter_id: chapterId.value
			})
		});
	} else {
		// 统一设置 or 没填练习音频
		popupRef.value.open();
	}
}
function onSelectImg(e) {
	e.tempFiles.forEach((o, i) => {
		uni.compressImage({
			src: o.path,
			quality: 90,
			compressedWidth: o.image.width > 750 ? 750 : o.image.width,
			success: (res) => {
				res.sourcePath = o.path;
				uploadImgs.value.push(res);
			}
		});
	});
}
function onDeleteImg(e) {
	const delIdx = uploadImgs.value.findIndex((o) => o.sourcePath === e.tempFilePath);
	if (delIdx > -1) {
		uploadImgs.value.splice(delIdx, 1);
	}
}

let posting = false; // 是否正在上传中
function submit() {
	async function post() {
		const { code, data } = await ajax({
			method: 'post',
			url: 'api/checkin/submit',
			data: {
				days_conf_id: daysConfId.value,
				task_id: taskId.value,
				chapter_id: chapterId.value,
				img_keys: uploadImgs.value.map((o) => o.imgkey).join(),
				experience: experience.value.trim().length > 250 ? experience.value.trim().substring(0, 250) : experience.value.trim()
			}
		});
		loadEnd();
		posting = false;
		if (code) return;

		redirect('/exclusive/checkin/complete', {
			chapter_id: chapterId.value,
			forward_info: encodeData({
				...forwardInfo.value,
				...data
			})
		});
	}

	if (posting) return;

	if (!experience.value.trim().length && !uploadImgs.value.length) {
		Toast('请至少完成一项');
		return;
	}

	posting = true;
	loadStart({ title: '上传中' });

	if (uploadImgs.value.length) {
		const now = Date.now();
		let uploaded = 0;
		uploadImgs.value.forEach((obj, idx) => {
			const key = `${now}${idx}`;
			uni
				.uploadFile({
					url: VITE_UPLOAD_HOST,
					filePath: obj.tempFilePath,
					name: 'file',
					formData: {
						token: uptoken.value,
						key
					}
				})
				.then((res) => {
					if (res.statusCode === 200) {
						obj.imgkey = key;
						uploaded++;

						if (uploaded >= uploadImgs.value.length) {
							post();
						}
					}
				})
				.catch((e) => {
					console.log('catch', e);
				});
		});
	} else {
		post();
	}
}

/** 播放音频相关 */
const curVideoSrc = ref(); // 当前正在播放的音频
const playVideoDone = ref(false);
const videoState = reactive({
	isPlay: null // 是否正在播放，初始null为了避免界面闪一下播放中的状态
});
const videoCtx = uni.getBackgroundAudioManager();
const processVideo = ref(0); // 音频播放进度
const holdingProcess = ref(false); // 是否正在调整进度
// const hasPlayed = ref(false); // 执行过onplay
// 是否初始化完
// const inited = computed(() => {
// 	if (taskInfo.value.days_type === 1) {
// 		// 分别设置
// 		if (mode.value === 'review' && taskInfo.value.audio_practice) {
// 			return videoState[taskInfo.value.audio_practice]?.duration && videoState[taskInfo.value.audio_intro]?.duration;
// 		} else {
// 			return videoState[taskInfo.value.audio_intro]?.duration;
// 		}
// 	} else {
// 		// 统一设置
// 		return videoState[taskInfo.value.intro_voice]?.duration;
// 	}
// });

// 调整播放进度结束
function sliderChange(src, e) {
	holdingProcess.value = false;

	let newTime = Math.round(e.detail.value);
	if (!playVideoDone.value && mode.value === 'normal' && newTime > processVideo.value) {
		newTime = processVideo.value;
	}
	videoState[src].currentTime = newTime;
	if (src !== curVideoSrc.value) {
		videoCtx.pause();
		playVideo(src, videoState[src].title);
		setTimeout(() => {
			seekVideo(newTime, src, videoState[src].title);
		}, 50);
	} else {
		seekVideo(newTime, src, videoState[src].title);
	}
}
// 调整播放进度中
function sliderChanging(src, e) {
	holdingProcess.value = true;
	videoState[src].currentTime = Math.round(e.detail.value);
}

// 初始化播放音乐
function initVideo(src, title) {
	if (!taskInfo.value.intro_voice && !taskInfo.value.audio_intro && !taskInfo.value.audio_practice) return;

	curVideoSrc.value = src;
	// console.log('videoState.isPlay + curVideoSrc.value', videoState.isPlay, curVideoSrc.value);

	videoCtx.src = realImgSrc(src);
	videoCtx.title = title;

	videoCtx.onCanplay(() => {
		if (!isCurPage(PagePath)) return;

		// 只有初始化的时候才执行
		// if (videoCtx.currentTime) return;
		// videoState[src].hadPlayed = true;

		// (function fun() {
		// 	if (!isCurPage(PagePath)) return;

		// videoState[curVideoSrc.value] = {
		// 	currentTime: Math.round(videoCtx.currentTime),
		// 	title: videoCtx.title
		// };

		// 从上次的地方开始播放
		// if (mode.value === 'normal' && !playVideoDone.value && processVideo.value > 0) {
		// 	videoCtx.seek(processVideo.value);
		// 	videoState[curVideoSrc.value].currentTime = processVideo.value;
		// 	console.log('videoCtx.seek', processVideo.value);
		// } else {
		// 	videoCtx.seek(0.001);
		// 	videoState.currentTime = 0;
		// 	console.log('videoCtx.seek', 0.001);
		// }

		// (function fun() {
		// 	console.log('hasPlayed.value', hasPlayed.value);
		// 	if (!hasPlayed.value) {
		// 		setTimeout(fun, 20);
		// 		return;
		// 	}
		// 	// setTimeout(
		// 	// 	() => {
		// 	// 		(videoState.isPlay === null || videoState.isPlay === true) && videoCtx.pause();
		// 	// 	},
		// 	// 	user.value.osName === 'ios' ? 200 : 0
		// 	// );

		// 	// 回顾的时候 & 有练习音频 & 且练习音频未初始化过 = 重置未初始化过
		// 	if (mode.value === 'review' && taskInfo.value.audio_practice && !videoState[taskInfo.value.audio_practice]?.duration) {
		// 		hasPlayed.value = false;
		// 	}
		// })();
		// })();
	});
	// 停止的时候,改变icon状态
	videoCtx.onEnded(() => {
		if (!isCurPage(PagePath)) return;
		videoState.isPlay = false;
		// console.log('isPlay-onEnded', videoState.isPlay, videoCtx.src);
		videoState[src].ended = true;
		playVideoDone.value = true;
		videoState[src].currentTime = videoState[src].duration;

		// 正常打卡，听完自动点按钮
		if (mode.value === 'normal') {
			setLocal('playVideo1Done-' + daysConfId.value, true);
			goCheckin();
		}
	});
	videoCtx.onPause(() => {
		console.log('isPlay-onPause1', videoState.isPlay, videoCtx.src);
		if (!isCurPage(PagePath)) return;
		videoState.isPlay = false;
		console.log('isPlay-onPause2', videoState.isPlay, videoCtx.src);
	});
	videoCtx.onStop(() => {
		if (!isCurPage(PagePath)) return;
		console.log('isPlay-onStop1', videoState.isPlay, videoCtx.src, videoCtx);
		if ((videoState.isPlay === null && mode.value === 'review') || !videoState[curVideoSrc.value]?.duration) return;
		console.log('isPlay-onStop2', videoState.isPlay, videoCtx.src);
		videoState.isPlay = false;
		videoState[src].ended = true;
		// 关闭屏幕常亮
		uni.setKeepScreenOn({
			keepScreenOn: false
		});
	});
	videoCtx.onPlay(() => {
		if (!isCurPage(PagePath)) return;

		// 从上次的地方开始播放
		if (videoState.isPlay === null) {
			if (mode.value === 'normal') {
				if (!playVideoDone.value && processVideo.value > 0) {
					console.log('videoCtx.seek', processVideo.value);
					videoCtx.seek(processVideo.value);
					videoState[curVideoSrc.value].currentTime = processVideo.value;
				}
			} else {
				if (videoState[curVideoSrc.value].currentTime > 0) {
					console.log('videoCtx.seek', videoState[curVideoSrc.value].currentTime);
					videoCtx.seek(videoState[curVideoSrc.value].currentTime);
				}
			}
		}

		console.log('isPlay-onPlay', videoState.isPlay, videoCtx.src);
		videoState.isPlay = true;
		videoState[src].ended = false;
		// 设置屏幕常亮
		uni.setKeepScreenOn({
			keepScreenOn: true
		});
	});
	videoCtx.onTimeUpdate(() => {
		if (!isCurPage(PagePath)) return;
		// 没有正在手动改进度的话，自动更新播放进度
		if (!holdingProcess.value && videoState.isPlay) {
			let curTime = Math.round(videoCtx.currentTime);
			if (curTime <= 0) {
				curTime = 0.001;
			} else if (curTime > videoState[curVideoSrc.value].duration) {
				curTime = videoState[curVideoSrc.value].duration;
			}

			videoState[curVideoSrc.value].currentTime = curTime;

			// 未播放完成时,记录播放进度
			if (mode.value === 'normal' && !playVideoDone.value && curTime > processVideo.value) {
				setLocal('processVideo1-' + daysConfId.value, curTime);
				processVideo.value = curTime;
			}
		}
	});
}

// 播放
function playVideo(src, title) {
	if (videoState[src].ended || src !== curVideoSrc.value) {
		// console.log('initVideo');
		initVideo(src, title);
		videoState.isPlay = null;
		if (videoState[src].ended) {
			videoState[src].currentTime = 0.001;
		}
		videoCtx.play();
	} else {
		console.log('videoState.isPlay', videoState.isPlay);
		// console.log('videoCtx', videoCtx);
		// console.log('videoCtx.src', videoCtx.src);
		if (!videoState.isPlay) {
			videoState.isPlay = true;
			videoCtx.play();
		} else {
			videoState.isPlay = false;
			videoCtx.pause();
		}
	}
}
function seekVideo(type, src, title) {
	if (mode.value === 'normal' && !playVideoDone.value) return;

	let newTime;
	if (typeof type == 'string') {
		if (type == 'add') {
			// 前进15s
			newTime = videoCtx.currentTime + 15;
		} else if (type == 'minus') {
			// 后退15s
			newTime = videoCtx.currentTime - 15;
		}
	} else {
		newTime = type;
	}

	if (newTime <= 0) {
		newTime = 0.001;
	} else if (newTime > videoState[src].duration) {
		newTime = videoState[src].duration;
	}
	if (src !== curVideoSrc.value) {
		initVideo(src, title);
	}
	videoCtx.seek(newTime);
	videoState.currentTime = newTime;
	videoCtx.play();
}

// 预览图片
function viewPhoto(idx) {
	coverListRef.value.show(idx);
}

const logId = ref(); // 打卡记录ID
const taskId = ref(); // 任务ID
const daysConfId = ref(); // 天数配置ID
const chapterId = ref(); // 章节ID
const forwardInfo = ref({}); // 上一个页面带过来的数据
onLoad((opts) => {
	logId.value = opts.log_id;
	taskId.value = opts.task_id;
	daysConfId.value = opts.days_conf_id;
	playVideoDone.value = getLocal('playVideo1Done-' + daysConfId.value);
	processVideo.value = getLocal('processVideo1-' + daysConfId.value) || 0;
	chapterId.value = opts.chapter_id;
	forwardInfo.value = decodeData(opts.forward_info);
});
onMounted(() => {
	if (logId.value) {
		// 打卡回顾
		mode.value = 'review';
		getCheckinReview();
	} else {
		// 正常进入打卡
		mode.value = 'normal';
		getUptoken();
		getTaskInfo();
	}
});
// 页面销毁时,停止播放音乐并销毁
onUnload(() => {
	videoCtx.src = realImgSrc('unused');
	videoCtx.stop();
});
</script>

<style lang="scss">
@import 'index.scss';
</style>

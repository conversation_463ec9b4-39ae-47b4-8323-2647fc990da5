<template>
	.
	<!-- <view
		class="home-index"
		:style="{ display: 'none', paddingTop: user.safePadHeight }">
		<view class="title fwb f56">
			<view>欢迎来到</view>
			<view>员工学习园地</view>
			<view
				class="tologin"
				@click="navigate('/login')">
				登录解锁完整功能 ›
			</view>
		</view>

		<ActivityList visitor />

		<LearnList visitor />

		<button
			class="ui-button-blue"
			@click="navigate('/login')">
			登录
		</button>
	</view>

	<PreloadImage :list="preloadImage" /> -->
</template>

<script setup>
/**
 * @description 首页
 */
import { storeToRefs } from 'pinia';
import { useUserStore } from '@/stores/user';
import { useStartParams } from '@/stores/start-params';
import { UserType } from '@/utils/data-map';
import { redirect } from '@/utils/tools';
import { useIsCheckinPreview } from '@/hooks/is-checkin-preview';

const { updateUserInfo } = useUserStore();
const { startParams } = storeToRefs(useStartParams());
const { isCheckinPreview } = useIsCheckinPreview();

// 如果是打卡活动预览模式，直接进入
if (isCheckinPreview.value && startParams.value?.factory_id && startParams.value?.jobnum) {
	updateUserInfo('userType', UserType['志愿者']);
	updateUserInfo('loginJobnum', startParams.value.jobnum);

	ajax({
		url: 'api/checkin/simple-detail',
		noLoad: false,
		data: {
			period_id: startParams.value.period_id
		}
	}).then(({ code, data }) => {
		if (code) {
			redirect('/login');
		} else {
			redirect('/exclusive/checkin/index', {
				...startParams.value,
				...data
			});
		}
	});
} else {
	redirect('/login');
}

// import ActivityList from '@/components/ActivityList/index.vue';
// import LearnList from '@/components/LearnList/index.vue';
// import PreloadImage from '@/components/PreloadImage/index.vue';
// const preloadImage = ref(['learn/title.woff2']);

// const { user } = storeToRefs(useUserStore());
// const { updateUserInfo } = useUserStore();

// 每次进入小程序，清空用户信息
// onLoad(() => {
// updateUserInfo('userid', undefined);
// updateUserInfo('userType', undefined);
// updateUserInfo('loginJobnum', undefined);
// redirect('/login');
// });
</script>

<style lang="scss">
@import 'index.scss';
</style>

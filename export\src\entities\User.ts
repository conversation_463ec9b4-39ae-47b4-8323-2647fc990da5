import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column } from 'typeorm';

import { DEFAULT_USER_TYPE } from '../constants';

@Entity('psychotest_user')
export class UserEntity {
  @PrimaryGeneratedColumn()
    id: number;

  @Column()
    jobnum: string;

  @Column({ default: () => 0 })
    age: number;

  @Column()
    sex: string;

  @Column({ default: () => DEFAULT_USER_TYPE })
    user_type: string;

  @Column({ type: 'timestamp' })
    create_time: Date;

  @Column({ type: 'timestamp' })
    update_time: Date;

  @Column()
    factory_id: number;

  @Column()
    wx_openid: string;

  @Column({ default: () => 1 })
    state: number;

  @Column({
    type: 'timestamp',
    nullable: true
  })
    last_login_time: Date;
}

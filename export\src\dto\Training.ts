import { IsString, IsInt, IsOptional, IsDateString, IsEnum } from 'class-validator';

import { UserTypeMap } from '../types/enums';

/** 模块学习情况-查询参数 */
export class ModuleQueryDto {
  /** 工号 */
  @IsOptional()
  @IsString()
    jobnum: string;

  /** 工厂ID */
  @IsInt()
    factory_id: number;
  
  /** 工厂名称 */
  @IsString()
    factory_name: string;

  /** 最后提交-开始时间 */
  @IsOptional()
  @IsDateString()
    start_time: string;
  
  /** 最后提交-结束时间 */
  @IsOptional()
  @IsDateString()
    end_time: string;
}

/** 志愿者培训数据统计-查询参数 */
export class DataQueryDto {
  /** 工厂ID */
  @IsInt()
    factory_id: number;

  /** 工厂名称 */
  @IsString()
    factory_name: string;
}

/** 志愿者培训数据统计-查询参数-超管 */
export class DataAdminQueryDto {
  /** 用户身份 */
  @IsOptional()
  @IsEnum(UserTypeMap)
    user_type: UserTypeMap;
}
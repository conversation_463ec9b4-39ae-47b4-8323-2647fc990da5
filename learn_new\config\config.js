let host;

const env = JSON.parse(process.env.UNI_CUSTOM_DEFINE || '{}');
if (env.ENV === 'dev') {
	// 开发服务器
	host = {
		api: 'https://node.employeehealth.cn:20200/',
		node: 'https://node.employeehealth.cn:20055/',
		upload: 'https://upload-z2.qiniup.com',
		img_host: 'http://imgdev.employeehealth.cn/',
		wap: 'https://www.employeehealth.cn/mdev/',
	};
} else if (process.env.NODE_ENV === 'development') {
	// 本地开发
	host = {
		upload: 'https://upload-z2.qiniup.com',
		img_host: 'http://imgdev.employeehealth.cn/',

		// api: 'http://localhost:20200/',
		// node: 'http://localhost:20050/',
		// wap: 'http://*************:5273/',

		// node: 'http://*************:20050/',
		// wap: 'http://*************:5273/',

		api: 'http://*************:20200/',
		node: 'http://*************:20050/',
		wap: 'http://*************:5273/',

		// node: 'http://*************:20050/',
		// node: 'http://*************:20050/',
		// wap: 'http://*************:5273/',

		// api: 'https://node.employeehealth.cn:20200/',
		// node: 'https://node.employeehealth.cn:20055/',
		// wap: 'https://www.employeehealth.cn/mdev/',

		// api: 'https://node.employeehealth.cn:20201/',
		// node: 'https://node.employeehealth.cn:20056/',
		// wap: 'https://www.employeehealth.cn/m/',
	};
} else if (process.env.NODE_ENV === 'production') {
	// 生产服务器
	host = {
		api: 'https://node.employeehealth.cn:20201/',
		node: 'https://node.employeehealth.cn:20056/',
		upload: 'https://upload-z2.qiniup.com',
		img_host: 'http://img.employeehealth.cn/',
		wap: 'https://www.employeehealth.cn/m/',
	};
}

export default host
/**
 * @description 表格字段
 */
import { userTypeMap } from '@/function/name-map';

// 筛选字段
export const searchField = [
  {
    label: '工厂：',
    prop: 'factory_id',
    formOpts: {
      componentName: 'ElSelect',
      optsList: [],
      props: {
        style: 'width:160px',
        filterable: true,
        'default-first-option': true,
      },
    },
  },
  {
    label: '工号：',
    prop: 'jobnum',
    width: '190px',
    formOpts: { props: { clearable: true } },
  },
  {
    label: '身份：',
    prop: 'user_type',
    formOpts: {
      componentName: 'ElSelect',
      optsList: userTypeMap.map((label, index) => ({
        label,
        value: index ? label : ''
      })),
      props: {
        style: 'width:160px',
        clearable: true
      },
    },
  },
  {
    label: '获取时间：',
    prop: 'create_time',
    formOpts: {
      componentName: 'ElDatePicker',
      props: {
        style: 'width:240px',
        type: 'daterange',
        'range-separator': '至',
        'start-placeholder': '开始日期',
        'end-placeholder': '结束日期',
        'value-format': 'yyyy-MM-dd',
        clearable: true,
      },
    },
  },
  {
    label: '',
    prop: 'add',
    defaultValue: '新增',
    formOpts: {
      componentName: 'ElButton',
      props: {
        type: 'primary',
        icon: 'el-icon-plus',
      },
      events: {},
    },
  },
  // {
  //   label: '',
  //   prop: 'export',
  //   customClass: 'poa export',
  //   defaultValue: '明细',
  //   formOpts: {
  //     componentName: 'a',
  //     props: {
  //       class: 'i-export vam',
  //       title: '导出明细',
  //     },
  //     events: {},
  //   },
  // }
];

function formatter(row, col, val) {
  return val ?? 0;
}
// 表格中的字段
export const tableCols = [
  // {
  //   prop: 'factory_id',
  //   label: '所属工厂',
  //   minWidth: '90px',
  //   align: 'center'
  // },
  {
    prop: 'jobnum',
    label: '工号',
    minWidth: '100px',
    align: 'center'
  },
  {
    prop: 'user_type',
    label: '当前身份',
    minWidth: '100px',
    align: 'center'
  },
  {
    prop: 'score_total',
    label: '总积分',
    minWidth: '90px',
    align: 'center',
    formatter,
  },
  {
    prop: 'rank',
    label: '积分排名',
    minWidth: '90px',
    align: 'center'
  },
  {
    prop: 'score_training',
    label: '志愿者培训',
    minWidth: '90px',
    align: 'center',
    formatter,
  },
  {
    prop: 'score_checkin',
    label: '打卡活动',
    minWidth: '90px',
    align: 'center',
    formatter,
  },
  {
    prop: 'score_service',
    label: '服务记录',
    minWidth: '90px',
    align: 'center',
    formatter,
  },
  {
    prop: 'score_visitor',
    label: '小课堂',
    minWidth: '90px',
    align: 'center',
    formatter,
  },
  {
    prop: 'score_others',
    label: '其它积分',
    minWidth: '90px',
    align: 'center',
    formatter,
  },
];
<template>
<view class="formal-model4" :class="{ 'swiper-question-wrap': questionStep }" :style="{ paddingTop: user.statusBarHeight }">
	<uni-icons type="back" class="nav-back" @click="goBack()"></uni-icons>
	
	<!-- 题目列表 -->
	<view class="nav-title" v-if="questionStep" :style="{ top: user.statusBarHeight }">案例题</view>
	<Question v-if="questionStep" v-show="showQuestion" ref="questionRef" class="swiper-question" :short="shortQuestion" :list="questionList" @midComplete="midComplete" prevText="上一页" nextText="下一页" @submit="submit" />
	
	<!-- 菜单 -->
	<view v-else v-show="showMenu">
		<view class="formal-menu short">
			<view class="button block plain title f34">我们将从以下四个方面，学习如何帮助遇到心理困扰的同事</view>
			<view class="border-box mt30">
				<view class="button block light f36 pl20 pr20" :class="{ complete: completeStep >= i + 1, disabled: completeStep < i }" @click="menuClick(i)" v-for="(v, i) in menuArr" :key="i">{{ v }}</view>
			</view>
		</view>
		<view class="button small-submit" style="width:300rpx" @click="goBack()">返回总目录</view>
	</view>
	
	
	<!-- ===============================Q1 -->
	<uni-popup ref="q11Ref" type="center">
		<view class="popup-fullscreen intro">
			<view class="intro-ticket">
				<view class="dots">
					<text class="dot" v-for="v in 14" :key="v"></text>
				</view>
				<view class="button text">
					<view>通过《认识自我》的学习，我们已经筑牢了自我的基石，由己及人，在接下来的学习中，我们将展开帮助他人的翅膀。</view>
					<view class="mt20">首先带大家回顾重要的知识点：</view>
					<view>1.我们都可能处于心理困扰的状态，如焦虑、愤怒、悲伤等。</view>
					<view>2.心理困扰的状态是可以恢复的。</view>
					<view>3.大家可通过转换思维方式、深呼吸等调节情绪问题，更可以通过倾诉自己的困扰缓解心理困扰。</view>
				</view>
			</view>
			<view class="button plain round small-submit" @click="nextPage('q1-1', 'q1')">下一页</view>
		</view>
	</uni-popup>
	
	
	<!-- ===============================Q2 -->
	<uni-popup ref="q21Ref" type="center">
		<view class="popup-fullscreen question">
			<view class="button plain block btn-tit">学习RTH模型</view>
			
			<view class="button border block tips">我们需要掌握必要的心理方法与技巧，以更好地帮助同事应对心理困扰。</view>
			
			<view class="button block round main-btn" @click="nextPage('q2-1', 'q2')">下一页</view>
		</view>
	</uni-popup>
	
	<uni-popup ref="q22Ref" type="center">
		<view class="popup-fullscreen question q2-2">
			<view class="button plain block btn-tit">关怀他人的一般过程包括三个步骤，即RTH</view>
			
			<view class="button border block tips">
				<view>
					<text class="c_yellow mr10">1.R</text>即Recognize，识别；
				</view>
				<view>
					<text class="c_yellow mr10">2.T</text>即Talk，谈心；
				</view>
				<view>
					<text class="c_yellow mr10">3.H</text>即Help，帮助。
				</view>
			</view>
			
			<view class="button block round main-btn" @click="nextPage('q2-2', 'q2-3')">下一页</view>
		</view>
	</uni-popup>
	
	<uni-popup ref="q23Ref" type="center">
		<view class="nav-title" :style="{ top: user.statusBarHeight }">案例题</view>
		<view class="popup-fullscreen question q2-3">
			<ConnectLine :info="lines1" @complete="lineComplete.lines1 = true" />
			<view class="button small-submit" @click="nextPage('q2-3', 'q2-4')">提交</view>
		</view>
	</uni-popup>
	
	<uni-popup ref="q24Ref" type="center">
		<view class="popup-fullscreen jiexi">
			<view class="title">答案解析</view>
			<view class="intro-box">
				<view class="answer">连线正确</view>
				<view class="subtit">解析</view>
				<view class="info">带着清晰的目标，我们能够更好的去关注、理解和支持需要帮助的员工。</view>
			</view>
			<view class="button small-submit" @click="submit()">我知道了</view>
		</view>
	</uni-popup>
	
	
	<!-- ===============================Q3 -->
	<uni-popup ref="q31Ref" type="center">
		<view class="popup-fullscreen question q3-1">
			<view class="button plain block btn-tit">识别需要关爱的同事【识别（R）】</view>
			
			<view class="button border block tips">员工在工作中表现出来的明显变化可能是其遇到心理困扰的征兆。</view>
			
			<view class="button block round main-btn" @click="nextPage('q3-1', 'q3-2')">下一页</view>
		</view>
	</uni-popup>
	
	<uni-popup ref="q32Ref" type="center">
		<view class="popup-fullscreen question">
			<view class="button plain block btn-tit">需要注意识别四类变化</view>
			
			<view class="button border block tips">
				<view>
					<text class="c_yellow">情绪变化：</text>负面情绪增多，正面情绪减少。
				</view>
				<view>
					<text class="c_yellow">行为变化：</text>非理性或者消极行为增多。
				</view>
				<view>
					<text class="c_yellow">身体变化：</text>身体出现原因不明的不适、疼痛。
				</view>
				<view>
					<text class="c_yellow">语言变化：</text>表达负面想法的语言增多。
				</view>
			</view>
			
			<view class="button block round main-btn" @click="nextPage('q3-2', 'q3-3')">下一页</view>
		</view>
	</uni-popup>
	
	<uni-popup ref="q33Ref" type="center">
		<view class="nav-title" :style="{ top: user.statusBarHeight }">案例题</view>
		<view class="popup-fullscreen q3-3" v-if="questionStep == 'q3-3'">
			<DropCategory :info="dropCate1" @complete="nextPage('q3-3', 'q3-4')" />
		</view>
	</uni-popup>
	
	<uni-popup ref="q34Ref" type="center">
		<view class="popup-fullscreen jiexi">
			<view class="title">答案解析</view>
			<view class="intro-box">
				<view class="subtit">解析</view>
				<view class="info">
					<view>心理困扰引起的变化可能体现在情绪、行为、身体与语言各方面，我们需要从多角度观察员工的状态。</view>
					<view>· 负面情绪增多，正面情绪减少。</view>
					<view>· 非理性或者消极行为增多。</view>
					<view>· 身体出现原因不明的不适、疼痛。</view>
					<view>· 表达负面想法的语言增多。</view>
				</view>
			</view>
			<view class="button small-submit" @click="submit()">我知道了</view>
		</view>
	</uni-popup>
	
	
	<!-- ===============================Q4 -->
	<uni-popup ref="q41Ref" type="center">
		<view class="popup-fullscreen intro">
			<view class="intro-ticket">
				<view class="dots">
					<text class="dot" v-for="v in 14" :key="v"></text>
				</view>
				<view class="button text">
					<view>我们在识别心理困扰征兆后，需要通过有技巧的谈心谈话才能了解员工的具体问题与需求。</view>
				</view>
			</view>
			<view class="button plain round small-submit" @click="nextPage('q4-1', 'q4-2')">下一页</view>
		</view>
	</uni-popup>
	
	<!-- Q4-2 弹框 -->
	<uni-popup ref="q42Ref" type="center">
		<view class="popup-fullscreen q4-2">
			<view class="formal-menu">
				<view class="button block plain title f34">走入同事内心【谈心（T）】</view>
				<view class="border-box mt50">
					<view class="button block light f36" :class="{ complete: completeStep > 2 }" @click="nextPage('q4-2', 'q4-3-1')">开启谈心</view>
					<view class="button block light f36" :class="{ complete: completeStep > 2.5, disabled: completeStep < 2.5 }" @click="completeStep >= 2.5 && nextPage('q4-2', 'q4-4-1-2')">
						专注倾听
					</view>
					<view class="button block light f36" :class="{ complete: completeStep > 2.6, disabled: completeStep < 2.6 }" @click="completeStep >= 2.6 && nextPage('q4-2', 'q4-4-2-1')">
						表达同理心
					</view>
					<view class="button block light f36" :class="{ complete: completeStep > 2.7, disabled: completeStep < 2.7 }" @click="completeStep >= 2.7 && nextPage('q4-2', 'q44')">
						开放式询问
					</view>
				</view>
			</view>
		</view>
	</uni-popup>
	
	<uni-popup ref="q431Ref" type="center">
		<view class="popup-fullscreen">
			<Dialogue v-if="questionStep == 'q4-3-1'" :list="dialogue1" @submit="nextPage('q4-3-1', 'q41')" />
		</view>
	</uni-popup>
	
	<uni-popup ref="q432Ref" type="center">
		<view class="popup-fullscreen">
			<view class="nav-title" :style="{ top: user.statusBarHeight }">正确答案</view>
			<Dialogue v-if="questionStep == 'q4-3-2'" :list="dialogue2" @submit="nextPage('q4-3-2', 'q4-3-3')" />
		</view>
	</uni-popup>
	
	<uni-popup ref="q433Ref" type="center">
		<view class="popup-fullscreen question">
			<view class="button plain block btn-tit">开启谈心时，以下做法可提高员工的接受度</view>
			
			<view class="button border block tips">
				<view>介绍身份，征求同意：向对方表达你很关心TA，并愿意提供帮助。</view>
				<view class="mt30 mb30">承诺保密：郑重告知对方，你会对所有谈话内容保密，除非涉及自伤和伤人的情况。</view>
				<view>约定谈心时间和地点：在谈论心理困扰时，不会被其他人打扰的时间和私密地点让员工更能安心倾诉。</view>
			</view>
			
			<view class="button block round main-btn" @click="submit()">下一页</view>
		</view>
	</uni-popup>
	
	<uni-popup ref="q4412Ref" type="center">
		<view class="nav-title" :style="{ top: user.statusBarHeight }">案例题</view>
		<view class="popup-fullscreen q4-4-1-2" v-if="questionStep == 'q4-4-1-2'">
			<DropCategory :info="dropCate2" @complete="nextPage('q4-4-1-2', 'q4-4-1-3')" />
		</view>
	</uni-popup>
	
	<uni-popup ref="q4413Ref" type="center">
		<view class="popup-fullscreen jiexi">
			<view class="title">答案解析</view>
			<view class="intro-box">
				<view class="subtit">解析</view>
				<view class="info">
					<view>良好的倾听姿态能降低同事的防备心。</view>
					<view>助人过程中听比说更重要，专注地倾听对方，本身就是一种安慰与支持，适当回应能鼓励对方倾诉，让我们获得更多信息，为之后的帮助打好基础。</view>
				</view>
			</view>
			<view class="button small-submit" @click="nextPage('q4-4-1-3', 'q4-4-1-4')">下一页</view>
		</view>
	</uni-popup>
	
	<uni-popup ref="q4414Ref" type="center">
		<view class="nav-title" :style="{ top: user.statusBarHeight }">案例题</view>
		<view class="popup-fullscreen question q4-4-1-4">
			<ConnectLine :info="lines2" @complete="lineComplete.lines2 = true" />
			<view class="button small-submit" @click="nextPage('q4-4-1-4', 'q4-4-1-5')">提交</view>
		</view>
	</uni-popup>
	
	<uni-popup ref="q4415Ref" type="center">
		<view class="popup-fullscreen jiexi">
			<view class="title">答案解析</view>
			<view class="intro-box">
				<!-- <view class="answer">听出重点信息</view> -->
				<view class="subtit">解析</view>
				<view class="info">
					<!-- <view>对谈心中接收到的信息，我们需要做出梳理、分析与判断，为接下来的帮助环节打好基础。</view>
					<view>我们还要特别留意危机信号，如果发现求助者可能存在心理障碍，甚至是自杀的危险信号，需要及时联系专业人员，具体操作会在下一模块进行介绍。</view> -->
					<view>在倾听过程中，我们要抓住三个重点，为接下来的帮助环节打好基础。</view>
					<view class="mt30">
						<text class="fwb">挑战：</text>员工需要应对的挑战或解决的问题，这也是志愿者的帮助目标；
					</view>
					<view>
						<text class="fwb">已有行动：</text>员工已经做了哪些努力，志愿者需要判断其中哪些值得鼓励；
					</view>
					<view>
						<text class="fwb">需求：</text>员工当下最迫切的需求，志愿者可以据此给出进一步的建议。
					</view>
				</view>
			</view>
			<view class="button small-submit" @click="submit()">下一页</view>
		</view>
	</uni-popup>
	
	<uni-popup ref="q4421Ref" type="center">
		<!-- <view class="nav-title" :style="{ top: user.statusBarHeight }">案例题</view> -->
		<view class="popup-fullscreen question q4-4-2-1">
			<ConnectLine :info="lines3" @complete="lineComplete.lines3 = true" />
			<view class="button small-submit" @click="nextPage('q4-4-2-1', 'q4-4-2-2')">提交</view>
		</view>
	</uni-popup>
	
	<uni-popup ref="q4422Ref" type="center">
		<view class="popup-fullscreen jiexi">
			<view class="title">答案解析</view>
			<view class="intro-box">
				<view class="subtit">解析</view>
				<view class="info">在倾听时表达同理心不仅能引导对方进一步倾诉，也是一种重要的心理支持。表达同理心并不容易，在尽力尝试的过程中，请注意不随意评判对方、不否定对方，不高高在上地可怜对方，多站在对方地角度去思考和感受。</view>
			</view>
			<view class="button small-submit" @click="submit()">下一页</view>
		</view>
	</uni-popup>
	
	<uni-popup ref="q4431Ref" type="center">
		<view class="nav-title" :style="{ top: user.statusBarHeight }">案例题</view>
		<view class="popup-fullscreen q4-4-3-1" v-if="questionStep == 'q4-4-3-1'">
			<DropCategory :info="dropCate3" @complete="nextPage('q4-4-3-1', 'q4-4-3-2')" />
		</view>
	</uni-popup>
	
	<uni-popup ref="q4432Ref" type="center">
		<view class="popup-fullscreen jiexi">
			<view class="title">答案解析</view>
			<view class="intro-box">
				<view class="subtit">解析</view>
				<view class="info">
					<view>与开放式询问相反的封闭式询问，回答往往为简单的“是”或“不” ，难以促使求助者敞开心扉。</view>
					<view>在引导员工倾诉时，我们 要多使用“什么、因何、如何、怎样”等开放式句式进行询问。</view>
				</view>
			</view>
			<view class="button small-submit" @click="nextPage('q4-4-3-2', 'q4-4-3-3')">下一页</view>
		</view>
	</uni-popup>
	
	<uni-popup ref="q4433Ref" type="center">
		<view class="popup-fullscreen">
			<Dialogue v-if="questionStep == 'q4-4-3-3'" :list="dialogue3" :title="dialogueTitle3" type="choose" @submit="nextPage('q4-4-3-3', 'q4-4-3-4')" />
		</view>
	</uni-popup>
	
	<uni-popup ref="q4434Ref" type="center">
		<view class="popup-fullscreen jiexi">
			<view class="title">答案解析</view>
			<view class="intro-box">
				<view class="answer">正确答案：A</view>
				<view class="subtit">解析</view>
				<view class="info">
					<view>在进行开放式询问时，要避免让员工产生被窥探隐私或者被质问的感受。所以，请注意以下三点：</view>
					<view>语气语调要带着关怀之意</view>
					<view>避免轻浮猎奇、咄咄逼人或责问</view>
					<view>涉及一些隐私问题时尤其要谨慎</view>
				</view>
			</view>
			<view class="button small-submit" @click="submit()">我知道了</view>
		</view>
	</uni-popup>
	
	
	<!-- ===============================Q5 -->
	<uni-popup ref="q51Ref" type="center">
		<view class="popup-fullscreen question q5-1">
			<view class="button plain block btn-tit">
				<view>提供适当的帮助</view>
				<view>【帮助（H）】</view>
			</view>
			
			<view class="button border block tips">
				<view>根据谈心获得的信息后，我们需要据此提供以下三方面的心理支持：</view>
				<view class="c_yellow">· 肯定 <text class="ml50 mr50">· 建议</text> · 推荐</view>
			</view>
			
			<view class="button block round main-btn" @click="nextPage('q5-1', 'q5')">下一页</view>
		</view>
	</uni-popup>
	
	<uni-popup ref="q52Ref" type="center">
		<view class="popup-fullscreen question">
			<view class="button plain block btn-tit">心理支持一：肯定</view>
			
			<view class="button border block tips">
				<view>肯定能强化员工的正面感受、情绪与行为，能引导员工进行正向思维并增强员工的信心。</view>
				<view>在肯定时，要注意以下两点：</view>
				<view>
					<text class="c_yellow">具体：</text>避免笼统，指出具体哪里做得好
				</view>
				<view>
					<text class="c_yellow">真诚：</text>发自内心地肯定对方
				</view>
			</view>
			
			<view class="button block round main-btn" @click="nextPage('q5-2', 'q51')">下一页</view>
		</view>
	</uni-popup>
	
	<uni-popup ref="q53Ref" type="center">
		<view class="popup-fullscreen question">
			<view class="button plain block btn-tit">心理支持二：建议</view>
			
			<view class="button border block tips">在面对员工的挑战和需求时，我们可以通过建议引导员工寻找或创造对自己而言有效的应对方式。</view>
			
			<view class="button block round main-btn" @click="nextPage('q5-3', 'q5-4')">下一页</view>
		</view>
	</uni-popup>
	
	<uni-popup ref="q54Ref" type="center">
		<view class="popup-fullscreen question q5-4">
			<view class="button plain block btn-tit">在给出建议的时候，我们需要注意：</view>
			
			<view class="button border block tips">
				<view>· 避免使用命令的语气，以免引起员工反感；</view>
				<view>· 给出两三个可选项，并倾听员工的意见和偏好；</view>
				<view>· 如果员工不采纳建议，包容和尊重员工的想法；</view>
				<view>· 和员工一起探讨、挖掘真正适合其的方式。</view>
				<view class="mt30">
					我们也不要给自己太多压力，即使没有最终找到最有效的方式，探讨的过程对员工也有一定帮助。
				</view>
			</view>
			
			<view class="button block round main-btn" @click="nextPage('q5-4', 'q52')">下一页</view>
		</view>
	</uni-popup>
	
	<uni-popup ref="q55Ref" type="center">
		<view class="popup-fullscreen question">
			<view class="button plain block btn-tit">心理支持三：推荐</view>
			
			<view class="button border block tips">经过肯定、鼓励与建议后，仍然无法有效帮助员工应对心理困扰时，或者员工需要更专业的帮助时，可以推荐员工使用专业的心理咨询。</view>
			
			<view class="button block round main-btn" @click="nextPage('q5-5', 'q5-6')">下一页</view>
		</view>
	</uni-popup>
	
	<uni-popup ref="q56Ref" type="center">
		<view class="popup-fullscreen question">
			<view class="button plain block btn-tit">推荐员工寻求专业帮助</view>
			
			<view class="button border block tips">
				<view>以下是可以推荐给员工的专业心理援助资源。</view>
				<view class="c_yellow">公益热线：</view>
				<view>· 共青团中央青少年心理咨询和法律援助全国统一热线电话：12355</view>
				<view>· 全国统一卫计委免费卫生热线：12320</view>
				<view>· 本地的心理咨询热线</view>
				<view class="c_yellow">社区服务：</view>
				<view>· 当地街道办事处/居民社区成立的心理服务中心</view>
			</view>
			
			<view class="button block round main-btn" @click="submit()">我知道了</view>
		</view>
	</uni-popup>
	
	<!-- ===============================Q6 -->
	<uni-popup ref="q61Ref" type="center">
		<view class="popup-fullscreen question">
			<view class="button plain block btn-tit">课后练习</view>
			
			<view class="button border block tips">志愿者/管理者之间可以先通过角色扮演的方式练习培训中的助人技巧，以便更准确地去识别需要关怀的员工，真诚地与其谈话，并且恰当地提供帮助。</view>
			
			<view class="button block round main-btn" @click="openComplete()">我知道了</view>
		</view>
	</uni-popup>
	
	
	<!-- 完成进度 弹窗 -->
	<Process ref="processRef" :total="5" :step="completeStep" modelName="第三" :jobNum="user.loginJobnum" :isComplete="questionStep == 'q6-1'" @complete="goBack()" />
</view>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue';
import { storeToRefs } from 'pinia';
import { useUserStore } from '@/stores/user';

import Question from '@/components/Question/index.vue';
import ConnectLine from '@/components/ConnectLine/index.vue';
import DropCategory from '@/components/DropCategory/index.vue';
import Dialogue from '@/components/Dialogue/index.vue';
import Process from '@/components/Process/index.vue';

import formatQuestion from '@/utils/format-formal-question.js';
import {
	goBack,
	getLocal,
	setLocal,
	postError,
	Toast,
	modelComplete,
} from '@/utils/tools.js';

const { user } = storeToRefs(useUserStore());

const modelKey = 'model4-complete';
const completeStep = ref(getLocal(modelKey) || 0); // 已完成的模块step
const questionList = ref([]);
const shortQuestion = ref(false);
const questionStep = ref(null);
const showQuestion = ref(true);
const showMenu = ref(false);
const menuArr = ref([
	'学习RTH模型',
	'识别需要关爱的同事-R',
	'走入同事内心-T',
	'提供适当的帮助-H',
	'小结',
]);

const lineComplete = reactive({
	lines1: false,
	lines2: false,
	lines3: false,
})
// 连线对象1
const lines1 = ref({
	title: '请将助人步骤与对应的目标连线',
	lineArr: [
		'识别(R)',
		'了解员工的挑战与需求',
		'谈心(T)',
		'根据需求，提供恰当支持',
		'帮助(H)',
		'发现遇到心理困扰的员工',
	],
	rightConn: [
		'0-5',
		'1-2',
		'3-4',
	]
});
// 连线对象2
const lines2 = ref({
	title: '员工：我常年在外工作，很愧疚不能陪伴妻子、孩子。虽然他们也都理解，就是每次视频聊天都不知道说啥。',
	lineArr: [
		'员工的挑战',
		'利用视频聊天沟通',
		'员工的已有行动',
		'想和家人有更多话题',
		'员工的需求',
		'远离家人，沟通出现障碍',
	],
	rightConn: [
		'0-5',
		'1-2',
		'3-4',
	]
});
// 连线对象3
const lines3 = ref({
	title: '员工：我想离婚，但一想到孩子还小，就下不了决心，很苦恼。',
	lineArr: [
		'那就不要离婚好了',
		'随意下结论',
		'苦恼也没有什么用啊',
		'否定对方的需求',
		'为了孩子，你必须坚强一些',
		'表达同理心',
		'我理解你的苦恼',
		'同情对方',
		'你真可怜，我为你感到难过',
		'无视对方的情绪',
	],
	rightConn: [
		'0-1',
		'2-3',
		'4-9',
		'5-6',
		'7-8',
	]
});


// 拖动归类对象1
const dropCate1 = ref({
	title: '请将以下变化进行归类',
	totalProps: 12,
	category: [{
		name: '情绪变化',
		list: [
			'闷闷不乐',
			'暴躁激动',
			'焦虑不安',
		]
	}, {
		name: '行为变化',
		list: [
			'迟到早退',
			'停止娱乐',
			'减少社交',
		]
	}, {
		name: '身体变化',
		list: [
			'身体疼痛',
			'胃口不佳',
			'面容憔悴',
		]
	}, {
		name: '语言变化',
		list: [
			'“我真没用”',
			'“没有希望”',
			'“不想工作”',
		]
	}],
});
// 拖动归类对象2
const dropCate2 = ref({
	title: '如何倾听，能让员工有更好的感受呢？请进行归类',
	totalProps: 10,
	category: [{
		name: '好的倾听',
		list: [
			'双手自然',
			'眼神交流',
			'放下其他事',
			'适时点头',
			'适当回应',
		]
	}, {
		name: '不好的倾听',
		list: [
			'抱紧双臂',
			'中途回短信',
			'闭口不言',
			'随意打断',
			'埋头做记录',
		]
	}],
});
// 拖动归类对象3
const dropCate3 = ref({
	title: '为区分开放式询问与封闭式询问，请将以下询问进行归类。',
	totalProps: 6,
	category: [{
		name: '开放式询问',
		list: [
			'你是如何看待这件事的？',
			'是什么原因让你不愿意和大家聊天？',
			'你当时的感受是什么？',
		]
	}, {
		name: '封闭式询问',
		list: [
			'你现在心情好吗？',
			'你是不是经常走神？',
			'你要不要全勤奖？',
		]
	}],
});

// 对话列表1
const dialogue1 = ref([{
	user: 0,
	msg: '你最近总迟到，想聊一下吗？',
}, {
	user: 1,
	msg: '你是谁啊？',
}, {
	user: 0,
	msg: '来帮助你的志愿者',
}, {
	user: 1,
	msg: '我不放心告诉你',
}, {
	user: 0,
	msg: '想去茶水间聊聊吗？',
}, {
	user: 1,
	msg: '不想！',
}]);
// 对话列表2
const dialogue2 = ref([{
	user: 0,
	msg: '你好，我是志愿者XX，你最近总迟到，遇到什么困难，能跟你聊聊吗？',
}, {
	user: 1,
	msg: '我不放心告诉你',
}, {
	user: 0,
	msg: '没关系，我会为你保密的',
}, {
	user: 1,
	msg: '那好吧',
}, {
	user: 0,
	msg: '我们下午找个人少的地方？',
}, {
	user: 1,
	msg: '好的',
}]);
// 对话列表3
const dialogueTitle3 = ref('员工：我最近心情不好|以下哪一个开放式询问更合适？');
const dialogue3 = ref([{
	user: 0,
	msg: '能说说具体的原因吗？说出来也许好受些。',
}, {
	user: 1,
	msg: '哎呀，你是跟男友吵架了吗？为啥呀？',
}]);

const q11Ref = ref();

const q21Ref = ref();
const q22Ref = ref();
const q23Ref = ref();
const q24Ref = ref();

const q31Ref = ref();
const q32Ref = ref();
const q33Ref = ref();
const q34Ref = ref();

const q41Ref = ref();
const q42Ref = ref();
const q431Ref = ref();
const q432Ref = ref();
const q433Ref = ref();
const q4412Ref = ref();
const q4413Ref = ref();
const q4414Ref = ref();
const q4415Ref = ref();
const q4421Ref = ref();
const q4422Ref = ref();
const q4431Ref = ref();
const q4432Ref = ref();
const q4433Ref = ref();
const q4434Ref = ref();

const q51Ref = ref();
const q52Ref = ref();
const q53Ref = ref();
const q54Ref = ref();
const q55Ref = ref();
const q56Ref = ref();

const q61Ref = ref();

const processRef = ref();
const questionRef = ref();

const refs = reactive({
	q11Ref,

	q21Ref,
	q22Ref,
	q23Ref,
	q24Ref,

	q31Ref,
	q32Ref,
	q33Ref,
	q34Ref,

	q41Ref,
	q42Ref,
	q431Ref,
	q432Ref,
	q433Ref,
	q4412Ref,
	q4413Ref,
	q4414Ref,
	q4415Ref,
	q4421Ref,
	q4422Ref,
	q4431Ref,
	q4432Ref,
	q4433Ref,
	q4434Ref,

	q51Ref,
	q52Ref,
	q53Ref,
	q54Ref,
	q55Ref,
	q56Ref,

	q61Ref,
})


function menuClick(i) {
	if (completeStep.value < i) return;
	
	if (i === 1 || i === 4) {
		nextPage('', 'q' + (i + 2));
	} else {
		refs[`q${i + 2}1Ref`].open();
	}
}

async function nextPage(current, next, noSwipe) {
	if ((current === 'q2-3' && !lineComplete.lines1)
	|| (current === 'q4-4-1-4' && !lineComplete.lines2)
	|| (current === 'q4-4-2-1' && !lineComplete.lines3)) {
		Toast('请先完成连线');
		return;
	}

	// 格式化ref字符
	let currentRef = '';
	if (current?.includes('-')) {
		currentRef = current.replace(/-/g, '') + 'Ref';
	}
	let nextRef = '';
	if (next?.includes('-')) {
		nextRef = next.replace(/-/g, '') + 'Ref';
	}
	
	if (next.includes('-')) {
		questionStep.value = next;
		refs[nextRef].open();
		refs[currentRef].close();
	} else {
		if (showQuestion.value) {
			// 首次进入答题
			
			// 让组件重新渲染
			if (['q43'].includes(next)) {
				setTimeout(() => {
					questionStep.value = null;
					setTimeout(() => {
						questionStep.value = next;
					}, 10)
				}, 0)
			}
			
			const list = await ajax(`question/${next.replace('q', 'model4-')}.json`);
			questionList.value = formatQuestion(list, (obj, idx) => {
				// 插入中途切出的跳转
				if (next === 'q3') {
					if ([0, 2].includes(idx)) {
						obj.midComplete = true;
					} else if ([1, 3].includes(idx)) {
						obj.hidePrev = true;
					}
				// } else if (next === 'q42') {
				// 	shortQuestion.value = true;
				} else if (next === 'q5') {
					obj.midComplete = true;
					obj.hidePrev = true;
				}
			});
		} else {
			// 答题中途切换出去，然后切回来
			showQuestion.value = true;
			if (!noSwipe) {
				questionRef.value.addSwiperIdx();
			}
		}
		questionStep.value = next;
		refs[currentRef]?.close();
	}
}

// 做题中途切出
function midComplete() {
	if (questionStep.value === 'q5') {
		questionStep.value = 'q52Ref';
	} else if (questionStep.value === 'q51') {
		questionStep.value = 'q53Ref';
	}
	refs[questionStep.value].open();
	
	setTimeout(() => {
		showQuestion.value = false;
	}, 300)
}

// 全部做完
async function submit(arr) {
	switch(questionStep.value) {
		case 'q1':
			questionStep.value = null;
			showMenu.value = true;
			showQuestion.value = true;
			setLocal('answer-model4-1', arr.map(o => o.checked.join('')).join('-'));
		break;
		
		case 'q2':
			questionStep.value = 'q2-2';
			q22Ref.value.open();
			setLocal('answer-model4-2', arr.map(o => o.checked.join('')).join('-'));
		break;
		
		case 'q3':
		case 'q44':
		case 'q52':
			if (questionStep.value === 'q3') {
				questionStep.value = 'q3-1';
				setLocal('answer-model4-3', arr.map(o => o.checked.join('')).join('-'));
			} else if (questionStep.value === 'q44') {
				setLocal('answer-model4-44', arr.map(o => o.checked.join('')).join('-'));
				questionStep.value = 'q4-4-3-1';
			} else if (questionStep.value === 'q52') {
				questionStep.value = 'q5-5';
				setLocal('answer-model4-5', arr.map(o => o.checked.join('')).join('-'));
			}
			refs[questionStep.value.replace(/-/g, '') + 'Ref']?.open();
			setTimeout(() => {
				showQuestion.value = false;
			}, 300)
		break;
		
		case 'q2-4':
		case 'q3-4':
		case 'q41':
		// case 'q42':
		case 'q43':
		case 'q4-3-3':
		case 'q4-4-1-5':
		case 'q4-4-2-2':
		case 'q4-4-3-4':
		case 'q5-6':
		case 'q6':
			showMenu.value = true;
			if (questionStep.value === 'q2-4') {
				modelComplete(modelKey, 1, completeStep);
				processRef.value.open();
			} else if (questionStep.value === 'q3-4') {
				modelComplete(modelKey, 2, completeStep);
				processRef.value.open();
			} else if (questionStep.value === 'q41') {
				// showQuestion.value = true;
				setLocal('answer-model4-41', arr.map(o => o.checked.join('')).join('-'));
				q432Ref.value.open();
				setTimeout(() => {
					questionStep.value = 'q4-3-2';
				}, 0)
			} else if (['q4-3-3', 'q4-4-1-5', 'q4-4-2-2'].includes(questionStep.value)) {
				if (questionStep.value === 'q4-3-3') {
					modelComplete(modelKey, 2.5, completeStep);
				} else if (questionStep.value === 'q4-4-1-5') {
					modelComplete(modelKey, 2.6, completeStep);
				} else if (questionStep.value === 'q4-4-2-2') {
					modelComplete(modelKey, 2.7, completeStep);
				}
				q42Ref.value.open();
				setTimeout(() => {
					questionStep.value = 'q4-2';
				}, 0)
			// } else if (questionStep.value === 'q42') {
			// 	// questionStep.value = 'q4-4-1';
			// 	setLocal('answer-model4-42', arr.map(o => o.checked.join('')).join('-'));
			// 	this.$refs['q4-4-1'].open();
			// 	setTimeout(() => {
			// 		questionStep.value = 'q4-4-1';
			// 	}, 0)
			} else if (questionStep.value === 'q43') {
				// questionStep.value = 'q4-4-1-4';
				setLocal('answer-model4-43', arr.map(o => o.checked.join('')).join('-'));
				q4414Ref.value.open();
				setTimeout(() => {
					questionStep.value = 'q4-4-1-4';
				}, 0)
			} else if (questionStep.value === 'q4-4-3-4') {
				modelComplete(modelKey, 3, completeStep);
				processRef.value.open();
			} else if (questionStep.value === 'q5-6') {
				modelComplete(modelKey, 4, completeStep);
				processRef.value.open();
				// showQuestion.value = true;
			} else if (questionStep.value === 'q6') {
				// 提交结果
				const { code, error } = await ajax({
					// url: 'api/api.php?a=putModule4',
					url: 'learn/submit',
					method: 'post',
					showLoading: true,
					data: {
						module: 4,
						jobnum: user.value.loginJobnum,
						op1: [
							getLocal('answer-model4-1'),
							getLocal('answer-model4-2'),
							getLocal('answer-model4-3'),
							getLocal('answer-model4-41'),
							getLocal('answer-model4-44'),
							getLocal('answer-model4-5'),
						].join('-'),
						op2: '情绪变化:闷闷不乐,暴躁激动,焦虑不安|行为变化:迟到早退,停止娱乐,减少社交|身体变化:身体疼痛,胃口不佳,面容憔悴|语言变化:我真没用,没有希望,不想工作',
						op3: '好的倾听:双手自然,眼神交流,放下其他事,适时点头,适当回应|不好的倾听:抱紧双臂,中途回短信,闭口不言,随意打断,埋头做记录',
						op4: '开放式询问:你是如何看待这件事的？,是什么原因让你不愿意和大家聊天？,你当时的感受是什么？|封闭式询问:你现在心情好吗？,你是不是经常走神？,你要不要全勤奖？',
						op5: arr.map(o => o.checked.join('')).join('-'),
					}
				});
	
				// 异常处理
				if (code || error) {
					postError().then(() => {
						submit(arr);
					}).catch(() => {})
				} else {
					modelComplete(modelKey, 5, completeStep);
					q61Ref.value.open();
					showQuestion.value = true;
					refs[questionStep.value.replace(/-/g, '') + 'Ref']?.close();
					questionStep.value = null;
				}
				return;
			}
			showQuestion.value = true;
			refs[questionStep.value.replace(/-/g, '') + 'Ref']?.close();
			questionStep.value = null;
		break;
	}
}

function openComplete() {
	// 需要等复制完成以后再打开弹框，避免isComplete=false
	questionStep.value = 'q6-1';
	nextTick(() => {
		processRef.value.open();
	})
}


onMounted(() => {
	q11Ref.value.open();
})
</script>

<style lang="scss">
	@import 'index.scss';
</style>
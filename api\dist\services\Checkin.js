"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CheckinService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const constants_1 = require("../constants");
const utils_1 = require("../utils");
let CheckinService = class CheckinService {
    constructor(entityManager) {
        this.entityManager = entityManager;
    }
    async activityInfo(headers) {
        const { login_jobnum, factoryid } = headers;
        try {
            const activeActivity = await this.entityManager.query(`
        select c.id as period_id, code_name, activity_desc, start_time, end_time, days
        from ${constants_1.DB_PRE}checkin_period_conf c, ${constants_1.DB_PRE}user u
        where c.factory_id=${factoryid}
        and u.factory_id=c.factory_id
        and u.jobnum='${login_jobnum}'
        and c.user_type like concat('%[', u.user_type ,']%')
        and c.state=1
        and c.start_time<=now()
        and c.end_time>now()
        order by c.id desc
      `);
            const emotionLog = await this.entityManager.query(`
        select count(1) as emotion_log
        from ${constants_1.DB_PRE}checkin_emotion_log
        where factory_id=${factoryid}
        and jobnum='${login_jobnum}'
        and create_time>=STR_TO_DATE('${(0, utils_1.timeFmt)(undefined, 'Y-M-D 00:00:00')}', '%Y-%m-%d %H:%i:%S')
      `);
            const recentActivity = await this.entityManager.query(`
        select c.id as period_id, code_name, activity_desc, start_time, end_time, c.days, max(l.create_time) as last_checkin_time
        from ${constants_1.DB_PRE}checkin_period_conf c, ${constants_1.DB_PRE}user u, ${constants_1.DB_PRE}checkin_log l
        where l.jobnum=u.jobnum
        and l.period_id=c.id
        and c.factory_id=${factoryid}
        and u.factory_id=c.factory_id
        and u.jobnum='${login_jobnum}'
        and c.user_type like concat('%[', u.user_type ,']%')
        and c.state=1
        and c.end_time<=now()
        GROUP BY l.period_id
        order by c.end_time desc
      `);
            const list = [];
            [...activeActivity, ...recentActivity].forEach(info => {
                info.start_time = info.start_time.getTime();
                info.end_time = info.end_time.getTime();
                info.last_checkin_time = info.last_checkin_time?.getTime() || 0;
                info.emotion_log = emotionLog[0].emotion_log;
                if (info.end_time > Date.now()) {
                    list.push(info);
                }
                else {
                    if (info.last_checkin_time + info.days * 24 * 3600 * 1000 > Date.now() - 30 * 24 * 3600 * 1000) {
                        delete info.last_checkin_time;
                        list.push(info);
                    }
                }
            });
            return (0, utils_1.resOk)(list);
        }
        catch (error) {
            return (0, utils_1.resErr)(500, error.message);
        }
    }
    async activityDetail(query, headers) {
        const { period_id } = query;
        const { login_jobnum, factoryid, is_checkin_preview } = headers;
        try {
            let sql = `select c.activity_desc, c.start_time, c.end_time, c.days, l.create_time
        from (${constants_1.DB_PRE}checkin_period_conf c, ${constants_1.DB_PRE}user u)
        left join ${constants_1.DB_PRE}checkin_log l on l.jobnum=u.jobnum and l.period_id=c.id
        where c.factory_id=${factoryid}
        and u.jobnum='${login_jobnum}'
        and c.id=${period_id}
        and u.factory_id=c.factory_id
        and c.user_type like concat('%[', u.user_type ,']%')
        and c.state=1
        and c.start_time<=now()
        order by l.id asc
        limit 0,1`;
            if (is_checkin_preview) {
                sql = `select c.activity_desc, c.start_time, c.end_time, c.days, l.create_time
        from ${constants_1.DB_PRE}checkin_period_conf c
        left join ${constants_1.DB_PRE}checkin_log l on l.jobnum='${login_jobnum}' and l.period_id=c.id 
        where c.factory_id=${factoryid}
        and c.id=${period_id}
        and c.state=1
        order by l.id asc
        limit 0,1`;
            }
            const activityDetail = await this.entityManager.query(sql);
            if (!activityDetail?.length) {
                return (0, utils_1.resErr)(202, '活动不存在');
            }
            if (activityDetail[0].create_time) {
                const startTime = new Date((0, utils_1.timeFmt)(activityDetail[0].create_time.getTime(), 'Y-M-D 00:00:00')).getTime();
                const endTime = startTime + activityDetail[0].days * 24 * 3600 * 1000;
                activityDetail[0].start_time = startTime;
                activityDetail[0].end_time = endTime;
            }
            else {
                activityDetail[0].start_time = activityDetail[0].start_time.getTime();
                activityDetail[0].end_time = activityDetail[0].end_time.getTime();
            }
            delete activityDetail[0].create_time;
            return (0, utils_1.resOk)(activityDetail[0]);
        }
        catch (error) {
            return (0, utils_1.resErr)(500, error.message);
        }
    }
    async simpleDetail(query) {
        const { period_id } = query;
        try {
            const activityDetail = await this.entityManager.query(`
        select code_name, activity_desc
        from ${constants_1.DB_PRE}checkin_period_conf
        where id=${period_id}
        and state=1
      `);
            if (!activityDetail?.length) {
                return (0, utils_1.resErr)(202, '活动不存在');
            }
            return (0, utils_1.resOk)(activityDetail[0]);
        }
        catch (error) {
            return (0, utils_1.resErr)(500, error.message);
        }
    }
    async testSubmit(body, headers) {
        const { period_id, test_id, answer_list } = body;
        const { login_jobnum, factoryid } = headers;
        try {
            const testInfo = await this.entityManager.query(`select * from ${constants_1.DB_PRE}checkin_test_conf
        where factory_id=? and id=? and period_id=? and state=1`, [
                factoryid,
                test_id,
                period_id
            ]);
            if (!testInfo?.length) {
                return (0, utils_1.resErr)(202, '问卷不存在');
            }
            const submittedCount = await this.entityManager.query(`select count(1) as count
        from ${constants_1.DB_PRE}checkin_test_log
        where factory_id=? and jobnum=? and test_id=? and period_id=?`, [
                factoryid,
                login_jobnum,
                test_id,
                period_id
            ]);
            if (submittedCount[0].count > 0) {
                return (0, utils_1.resErr)(203, '请勿重复提交');
            }
            const topicList = await this.entityManager.query(`select title, type, dimension, is_scored
        from ${constants_1.DB_PRE}checkin_test_topic
        where test_id=?
        order by sort_num asc`, [test_id]);
            if (topicList.length !== answer_list.length) {
                return (0, utils_1.resErr)(204, '题目数量不对');
            }
            const dimension = {};
            const rows = [];
            const columns = [
                'factory_id',
                'jobnum',
                'period_id',
                'test_id',
                'topic_id',
                'dimension',
                'answer',
                'score',
                'is_scored',
                'create_time',
                'test_title',
                'topic_title',
                'topic_type',
                'test_day_num',
                'test_type'
            ];
            topicList.forEach((topic, index) => {
                const answer = answer_list[index];
                if (topic.is_scored) {
                    if (!dimension[topic.dimension]) {
                        dimension[topic.dimension] = [];
                    }
                    dimension[topic.dimension].push(answer.score);
                }
                rows.push([
                    factoryid,
                    login_jobnum,
                    period_id,
                    test_id,
                    answer.topic_id,
                    topic.dimension || '',
                    answer.value || '',
                    answer.score !== undefined ? answer.score : null,
                    topic.is_scored,
                    new Date(),
                    testInfo[0].title,
                    topic.title,
                    topic.type,
                    testInfo[0].day_num,
                    testInfo[0].type
                ]);
            });
            await (0, utils_1.insertValues)(this.entityManager, 'checkin_test_log', columns, rows);
            const dimension_list = [];
            const entries = Object.entries(dimension);
            entries.forEach(([label, scores]) => {
                const calculateScore = new Function('scores', `return ${testInfo[0].score_formula?.replace(/\{list\}/g, 'scores')}`);
                const obj = {
                    label,
                    value: calculateScore(scores)
                };
                dimension_list.push(obj);
            });
            const periodInfo = await this.entityManager.query(`select code_name from ${constants_1.DB_PRE}checkin_period_conf where id=?`, [period_id]);
            return (0, utils_1.resOk)({
                cert_src: testInfo[0].cert_src,
                dimension_list,
                code_name: periodInfo[0]?.code_name,
                analysis: {
                    recap_json: testInfo[0].recap_json,
                    score_json: testInfo[0].score_json,
                    intro_json: testInfo[0].intro_json,
                    suggest_json: testInfo[0].suggest_json,
                    score_tips: testInfo[0].score_tips,
                    recap_prev: testInfo[0].recap_prev,
                    score_split: testInfo[0].score_split,
                }
            });
        }
        catch (error) {
            return (0, utils_1.resErr)(205, error.message);
        }
    }
    async userInfo(query, headers) {
        const { period_id } = query;
        const { login_jobnum, factoryid, is_checkin_preview } = headers;
        if (!factoryid || !login_jobnum || !period_id) {
            return (0, utils_1.resErr)(201, '缺少参数');
        }
        const now = Date.now();
        try {
            const activityInfo = await this.entityManager.query(`
        select id, days, start_time, end_time
        from ${constants_1.DB_PRE}checkin_period_conf
        where factory_id=${factoryid}
        and state=1
        and id=${period_id}
      `);
            if (!activityInfo[0]?.id) {
                return (0, utils_1.resErr)(101, '活动不存在');
            }
            if (!is_checkin_preview && activityInfo[0].start_time > now) {
                return (0, utils_1.resErr)(102, '活动未开始');
            }
            const logInfo = await this.entityManager.query(`
        select l.days_sum, l.chapter_id, c.days_sum as chapter_days_sum, l.create_time
        from ${constants_1.DB_PRE}checkin_log l, ${constants_1.DB_PRE}checkin_chapter c
        where l.factory_id=${factoryid}
        and l.period_id=${period_id}
        and l.jobnum='${login_jobnum}'
        and l.chapter_id=c.id
        order by l.id asc
      `);
            const testList = await this.entityManager.query(`
        select c.id, c.day_num, c.type, c.title, c.tooltips,
        (select count(1) from ${constants_1.DB_PRE}checkin_test_log where test_id=c.id and jobnum='${login_jobnum}' and period_id=${period_id}) as complete,
        c.cert_src
        from ${constants_1.DB_PRE}checkin_test_conf c
        where c.factory_id=${factoryid}
        and c.state=1
        and c.period_id=${period_id}
        order by c.day_num asc
      `);
            const chapterList = await this.entityManager.query(`
        select task_list, days_sum, checkin_type, days_type, id
        from ${constants_1.DB_PRE}checkin_chapter
        where factory_id=${factoryid}
        and period_id=${period_id}
        and state=1
        order by sort_num asc
      `);
            const daysList = await this.entityManager.query(`
        select *
        from ${constants_1.DB_PRE}checkin_days_conf
        where factory_id=${factoryid}
        and period_id=${period_id}
        order by chapter_id asc, sort_num asc
      `);
            const certInfo = await this.entityManager.query(`
        select count(1) as count
        from ${constants_1.DB_PRE}cert
        where jobnum='${login_jobnum}'
        and factory_id='${factoryid}'
        and type=1
        and checkin_period_id=${period_id}
      `);
            const days_total = activityInfo[0].days;
            const lastLogInfo = logInfo[logInfo.length - 1] || {};
            const gotCert = certInfo[0].count > 0;
            if (lastLogInfo.days_sum === undefined) {
                lastLogInfo.days_sum = 0;
            }
            let task_id = null;
            let days_conf_id = null;
            let daysSum = 0;
            let days_type = 0;
            for (let index = 0; index < chapterList.length; index++) {
                const chapter = chapterList[index];
                daysSum += chapter.days_sum;
                if (lastLogInfo.days_sum < daysSum) {
                    const idx = lastLogInfo.days_sum - daysSum + chapter.days_sum;
                    if (chapter.days_type === 1) {
                        const daysConfigs = daysList.filter((o) => o.chapter_id === chapter.id);
                        days_conf_id = daysConfigs[idx]?.id;
                    }
                    else {
                        const taskIds = chapter.task_list?.split(',') || [];
                        task_id = taskIds[idx] || null;
                    }
                    days_type = chapter.days_type;
                    break;
                }
            }
            const data = {
                days_sum: lastLogInfo.days_sum,
                days_total,
                task_id,
                days_conf_id,
                days_type,
                task_evaluate: null,
                test_info: null,
                getcert: null,
            };
            const testInfo = testList.find((o) => {
                let num = 0;
                if (o.type === 0) {
                    num = 1;
                }
                return lastLogInfo.days_sum + num >= o.day_num && !Number(o.complete);
            });
            const endTime = logInfo.length
                ? new Date((0, utils_1.timeFmt)(logInfo[0]?.create_time.getTime(), 'Y-m-d 00:00:00')).getTime() + activityInfo[0].days * 24 * 3600 * 1000
                : activityInfo[0].end_time.getTime();
            if (testInfo && (Date.now() < endTime || testInfo.type === 1)) {
                data.test_info = {
                    test_id: testInfo.id,
                    title: testInfo.title,
                    tooltips: testInfo.tooltips,
                };
            }
            const getcert = testList.find((o) => Number(o.complete) && o.cert_src);
            if (!gotCert && getcert) {
                data.getcert = {
                    cert_src: getcert.cert_src,
                    test_id: getcert.id,
                };
            }
            return (0, utils_1.resOk)(data);
        }
        catch (error) {
            return (0, utils_1.resErr)(500, error.message);
        }
    }
    async logInfo(query, headers) {
        const { period_id } = query;
        const { login_jobnum, factoryid, is_checkin_preview } = headers;
        try {
            let sql = `select id, step_len, days from ${constants_1.DB_PRE}checkin_period_conf where factory_id=? and state=1 and id=? and start_time<=now()`;
            if (is_checkin_preview) {
                sql = `select id, step_len, days from ${constants_1.DB_PRE}checkin_period_conf where factory_id=? and state=1 and id=?`;
            }
            const activityResult = await this.entityManager.query(sql, [factoryid, period_id]);
            const activity = activityResult[0];
            if (!activity?.id) {
                return (0, utils_1.resErr)(101, '活动未开始');
            }
            const logResult = await this.entityManager.query(`select id, type, chapter_id, days_continue, points, checkin_date, create_time
        from ${constants_1.DB_PRE}checkin_log
        where factory_id=? and period_id=? and jobnum=?
        order by id asc`, [
                factoryid,
                period_id,
                login_jobnum
            ]);
            const chapterResult = await this.entityManager.query(`select id, days_sum, checkin_type, days_type
        from ${constants_1.DB_PRE}checkin_chapter
        where factory_id=? and period_id=? and state=1
        order by sort_num asc`, [factoryid, period_id]);
            const daysConfResult = await this.entityManager.query(`select chapter_id, id
        from ${constants_1.DB_PRE}checkin_days_conf
        where factory_id=? and period_id=?
        order by chapter_id asc, sort_num asc`, [factoryid, period_id]);
            const list = [];
            const now = Date.now();
            const today = new Date((0, utils_1.timeFmt)(undefined, 'Y-M-D 00:00:00')).getTime();
            const days_sum = logResult.length;
            let startTime = today;
            let points_total = 0;
            let days_continue = 0;
            if (logResult.length) {
                const last = logResult[logResult.length - 1];
                if (new Date(last.checkin_date).getTime() >= today - 24 * 3600 * 1000) {
                    days_continue = last.days_continue;
                }
                startTime = new Date((0, utils_1.timeFmt)(new Date(logResult[0].create_time).getTime(), 'Y/M/D 00:00:00')).getTime();
            }
            const daysConf = daysConfResult;
            for (let index = 0; index < activity.days; index++) {
                const row = logResult[index];
                const obj = {};
                if (row) {
                    obj.checked = true;
                    obj.supplement = row.type === 1;
                    obj.chapter_id = row.chapter_id;
                    obj.log_id = row.id;
                    obj.points = row.points;
                    points_total += row.points;
                }
                else {
                    let chapter_id = 0;
                    let days = 0;
                    let checkin_type = 0;
                    let days_type = 0;
                    for (let i = 0; i < chapterResult.length; i++) {
                        const info = chapterResult[i];
                        days += info.days_sum;
                        if (index < days) {
                            chapter_id = info.id;
                            checkin_type = info.checkin_type;
                            days_type = info.days_type;
                            if (days_type === 1 && daysConf.length) {
                                const dayArr = daysConf.filter((o) => o.chapter_id === chapter_id);
                                if (dayArr[index - days + info.days_sum]) {
                                    obj.days_conf_id = dayArr[index - days + info.days_sum].id;
                                }
                            }
                            break;
                        }
                    }
                    obj.chapter_id = chapter_id;
                    obj.checkin_type = checkin_type;
                    obj.days_type = days_type;
                    if (startTime + index * 3600 * 24 * 1000 <= now) {
                        obj.notcheck = true;
                    }
                    else {
                        obj.locked = true;
                    }
                }
                list.push(obj);
            }
            return (0, utils_1.resOk)({
                step_len_list: activity.step_len?.split(',').map((v) => parseInt(v)) || [],
                list,
                days_sum,
                points_total,
                days_continue,
            });
        }
        catch (error) {
            return (0, utils_1.resErr)(500, error.message);
        }
    }
    async submit(body, headers) {
        const { chapter_id, task_id = 0, days_conf_id, experience = '', img_keys = '' } = body;
        const { login_jobnum, factoryid } = headers;
        if (!factoryid ||
            !chapter_id ||
            (!days_conf_id && !task_id) ||
            !login_jobnum ||
            (!experience.trim() && !img_keys)) {
            return (0, utils_1.resErr)(101, '缺少参数');
        }
        try {
            const activityInfo = await this.entityManager.query(`
        select p.id, p.start_time, p.end_time, p.days, c.period_id, c.image_text_color, c.bg_img, c.complete_tips, c.card_img, c.title,
        m.title as medal_title, m.cover as medal_cover, m.intro as medal_intro, c.sort_num, c.days_sum, c.checkin_type, c.days_type,
        (select sum(days_sum) from ${constants_1.DB_PRE}checkin_chapter where period_id=c.period_id and sort_num<=c.sort_num and is_del=0) as days_total
        from ${constants_1.DB_PRE}checkin_period_conf p, ${constants_1.DB_PRE}checkin_chapter c
        left join ${constants_1.DB_PRE}checkin_medal m on m.id=c.medal_id
        where c.factory_id=${factoryid} and c.id=${chapter_id} and c.period_id=p.id and p.state=1
      `);
            const userLogs = await this.entityManager.query(`
        select l.days_sum, l.days_continue, l.checkin_date, l.create_time
        from ${constants_1.DB_PRE}checkin_log l, ${constants_1.DB_PRE}checkin_chapter c
        where c.id=${chapter_id} and l.period_id=c.period_id and l.jobnum='${login_jobnum}' and c.factory_id=${factoryid}
        order by l.id desc
      `);
            const pointsTotal = await this.entityManager.query(`
        select sum(l.points) as points_total
        from ${constants_1.DB_PRE}checkin_log l, ${constants_1.DB_PRE}checkin_chapter c
        where l.period_id=c.period_id and c.id=${chapter_id} and l.factory_id=${factoryid} and l.jobnum='${login_jobnum}'
      `);
            const taskTips = await this.entityManager.query(`
        select complete_tips
        from ${constants_1.DB_PRE}checkin_task
        where factory_id=${factoryid} and id=${task_id}
      `);
            const taskCount = await this.entityManager.query(`
        select count(l.id) as count
        from ${constants_1.DB_PRE}checkin_log l, ${constants_1.DB_PRE}checkin_chapter c
        where l.period_id=c.period_id and c.id=${chapter_id} and l.factory_id=${factoryid} and l.jobnum='${login_jobnum}' and l.task_id=${task_id}
      `);
            const chapterTips = await this.entityManager.query(`
        select complete_tips
        from ${constants_1.DB_PRE}checkin_complete_tips
        where factory_id=${factoryid} and chapter_id=${chapter_id}
        order by id asc
      `);
            const allChapters = await this.entityManager.query(`
        select days_sum, id
        from ${constants_1.DB_PRE}checkin_chapter
        where period_id=(select period_id from ${constants_1.DB_PRE}checkin_chapter where factory_id=${factoryid} and id=${chapter_id})
        order by sort_num asc
      `);
            let daysConf = {};
            if (days_conf_id > 0) {
                const daysConfResult = await this.entityManager.query(`
          select *
          from ${constants_1.DB_PRE}checkin_days_conf
          where id=${days_conf_id}
        `);
                daysConf = daysConfResult[0] || {};
            }
            if (daysConf.chapter_id && daysConf.chapter_id !== chapter_id) {
                return (0, utils_1.resErr)(201, '章节不符');
            }
            const now = Date.now();
            const activity = activityInfo[0];
            const first = userLogs.length ? userLogs[userLogs.length - 1] : null;
            const today = new Date((0, utils_1.timeFmt)(undefined, 'Y-m-d 00:00:00')).getTime();
            const startTime = first ? new Date((0, utils_1.timeFmt)(first.create_time.getTime(), 'Y-m-d 00:00:00')).getTime() : today;
            const endTime = first ? startTime + activity.days * 24 * 3600 * 1000 : activity.end_time.getTime();
            if (!activity || (activity.start_time.getTime() > now || endTime <= now)) {
                return (0, utils_1.resErr)(202, '活动未开放');
            }
            const checkinLog = userLogs[0];
            let days_sum = 1;
            let days_continue = 0;
            let points = 1;
            let type = 0;
            let checkin_date = (0, utils_1.timeFmt)(undefined, 'Y-m-d');
            const diffDays = Math.ceil((now - startTime) / (3600 * 24 * 1000));
            if (checkinLog) {
                days_sum = checkinLog.days_sum + 1;
                if (days_sum > diffDays) {
                    return (0, utils_1.resErr)(203, '该日还未解锁打卡');
                }
                if (diffDays - checkinLog.days_continue > 1) {
                    if (diffDays === days_sum) {
                        const today = new Date((0, utils_1.timeFmt)(undefined, 'Y-m-d 00:00:00'));
                        if (checkinLog.create_time >= today.getTime() - 3600 * 24 * 1000 &&
                            checkinLog.create_time < today.getTime()) {
                            days_continue = checkinLog.days_continue + 1;
                            points = days_continue;
                        }
                        else {
                            days_continue = 1;
                        }
                    }
                    else {
                        type = 1;
                        checkin_date = (0, utils_1.timeFmt)(checkinLog.checkin_date.getTime() + 3600 * 24 * 1000, 'Y-m-d');
                    }
                }
                else {
                    days_continue = checkinLog.days_continue + 1;
                    points = days_continue;
                }
            }
            else {
                if (diffDays > 1) {
                    type = 1;
                    checkin_date = (0, utils_1.timeFmt)(startTime, 'Y-m-d');
                }
                else {
                    days_continue = 1;
                }
            }
            if (days_continue > 5) {
                points = 5;
            }
            if (days_sum > activity.days_total || days_sum <= activity.days_total - activity.days_sum) {
                return (0, utils_1.resErr)(204, '打卡章节不符');
            }
            const tipsArr = chapterTips;
            let card_tips = taskTips[0]?.complete_tips || '';
            if (activity.days_type === 1) {
                if (tipsArr[daysConf.sort_num - 1]?.complete_tips) {
                    card_tips = tipsArr[daysConf.sort_num - 1]?.complete_tips;
                }
            }
            else {
                if (taskCount[0]?.count >= 1 && tipsArr.length) {
                    const idx = Math.floor(Math.random() * tipsArr.length);
                    card_tips = tipsArr[idx].complete_tips;
                }
            }
            const points_total = (pointsTotal[0].points_total || 0) + points;
            let currentChapterCompleteDays = 0;
            for (const obj of allChapters) {
                currentChapterCompleteDays += obj.days_sum;
                if (obj.id === chapter_id) {
                    break;
                }
            }
            let medalInfo = {};
            let chapter_complete = false;
            if (days_sum >= currentChapterCompleteDays) {
                chapter_complete = true;
                if (activity.medal_cover) {
                    medalInfo = {
                        medal_title: activity.medal_title,
                        medal_cover: activity.medal_cover,
                        medal_intro: activity.medal_intro,
                    };
                }
            }
            return this.entityManager.transaction(async (manager) => {
                await manager.query(`
          insert into ${constants_1.DB_PRE}checkin_log(period_id, chapter_id, task_id, factory_id, jobnum, create_time, checkin_date, type, points, days_sum, days_continue, experience, img_keys, card_tips, days_type, task_type, days_conf_id)
          values(${activity.period_id}, ${chapter_id}, ${task_id || null}, ${factoryid}, '${login_jobnum}', now(), '${checkin_date}', ${type}, ${points}, ${days_sum}, ${days_continue}, '${experience}', '${img_keys}', '${card_tips}', ${activity.days_type}, ${daysConf.task_type || 0}, ${days_conf_id || null})
        `);
                await manager.query(`
          insert into ${constants_1.DB_PRE}checkin_rank(factory_id, jobnum, period_id, points_total, update_time, last_checkin_time)
          values(${factoryid}, '${login_jobnum}', '${activity.period_id}', '${points}', now(), now())
          on duplicate key update points_total=points_total+${points}, last_checkin_time=now()
        `);
                await manager.query(`
          update ${constants_1.DB_PRE}checkin_rank a
          INNER JOIN (
            select t.id, @i:=@i+1 as rankNum from
            (select @i:=0) r,
            (select * from ${constants_1.DB_PRE}checkin_rank where factory_id=${factoryid} and period_id=${activity.period_id} order by points_total desc, last_checkin_time asc) as t
          ) t1
          on a.id = t1.id
          set rank = rankNum
        `);
                if (chapter_complete && activity.medal_cover) {
                    await manager.query(`
            insert into ${constants_1.DB_PRE}medal(factory_id, jobnum, type, cover, medal_name, checkin_chapter_id, create_time)
            values(${factoryid}, '${login_jobnum}', 1, '${activity.medal_cover}', '${activity.medal_title}', '${chapter_id}', now())
          `);
                }
                const rankResult = await manager.query(`
          select rank
          from ${constants_1.DB_PRE}checkin_rank
          where factory_id=${factoryid} and period_id=${activity.period_id} and jobnum='${login_jobnum}'
        `);
                return (0, utils_1.resOk)({
                    period_id: activity.id,
                    points_total,
                    points,
                    days_sum,
                    sort_num: activity.sort_num,
                    checkin_type: activity.checkin_type,
                    days_continue,
                    chapter_title: activity.title,
                    complete_tips: activity.complete_tips,
                    card_tips,
                    image_text_color: activity.image_text_color,
                    bg_img: activity.bg_img,
                    card_img: activity.card_img,
                    rank: rankResult[0].rank,
                    chapter_complete,
                    ...medalInfo,
                });
            }).catch(err => {
                return (0, utils_1.resErr)(500, err.message);
            });
        }
        catch (error) {
            return (0, utils_1.resErr)(500, error.message);
        }
    }
    async daysInfo(query, headers) {
        const { period_id, days_num: queryDaysNum } = query;
        const { login_jobnum, factoryid } = headers;
        let days_num = queryDaysNum || 0;
        if (!factoryid || !period_id) {
            return (0, utils_1.resErr)(201, '缺少参数');
        }
        try {
            const periodResult = await this.entityManager.query(`select * from ${constants_1.DB_PRE}checkin_period_conf where id=? and state=1`, [period_id]);
            const period = periodResult[0];
            if (!period) {
                return (0, utils_1.resErr)(202, '未查询到数据');
            }
            const logResult = await this.entityManager.query(`select days_sum, create_time from ${constants_1.DB_PRE}checkin_log
        where factory_id=? and period_id=? and jobnum=?
        order by id desc`, [
                factoryid,
                period_id,
                login_jobnum
            ]);
            const chapterResult = await this.entityManager.query(`select * from ${constants_1.DB_PRE}checkin_chapter
        where period_id=? and state=1 and is_del=0
        order by sort_num asc`, [period_id]);
            const daysConfResult = await this.entityManager.query(`select * from ${constants_1.DB_PRE}checkin_days_conf
        where period_id=?
        order by chapter_id asc, sort_num asc`, [period_id]);
            const now = Date.now();
            const last = logResult[0];
            const first = logResult.length ? logResult[logResult.length - 1] : null;
            const days_total = period.days;
            const today = new Date((0, utils_1.timeFmt)(undefined, 'Y-M-D 00:00:00')).getTime();
            const startTime = first ? new Date((0, utils_1.timeFmt)(new Date(first.create_time).getTime(), 'Y-M-D 00:00:00')).getTime() : today;
            const endTime = first ? startTime + days_total * 24 * 3600 * 1000 : new Date(period.end_time).getTime();
            const diffDays = Math.ceil(((now >= endTime ? (endTime - 1) : now) - startTime) / (3600 * 24 * 1000));
            if (days_num > days_total || days_num > diffDays + 1) {
                return (0, utils_1.resErr)(203, '禁止查询数据');
            }
            if (!days_num) {
                days_num = last?.days_sum || 0;
            }
            const is_checked = last?.days_sum >= diffDays;
            const disabled = now < new Date(period.start_time).getTime() || now >= endTime;
            let tempDaysSum = 0;
            let curChapter = {};
            let daysIdx = 0;
            for (let i = 0; i < chapterResult.length; i++) {
                const o = chapterResult[i];
                const flag = days_num >= tempDaysSum && (days_num < tempDaysSum + o.days_sum || (is_checked && days_num === tempDaysSum + o.days_sum));
                if (flag) {
                    curChapter = o;
                    if (is_checked) {
                        daysIdx = days_num - tempDaysSum - 1;
                    }
                    else {
                        daysIdx = days_num - tempDaysSum;
                    }
                    break;
                }
                else {
                    tempDaysSum += o.days_sum;
                }
            }
            const data = {
                is_checked,
                chapter_id: curChapter.id,
                chapter_title: curChapter.title,
                checkin_type: curChapter.checkin_type,
                days_type: curChapter.days_type,
                disabled,
            };
            let sql = '';
            let params = [];
            if (curChapter.days_type === 1) {
                const daysInfo = daysConfResult.filter((o) => o.chapter_id === curChapter.id)[daysIdx] || {};
                delete daysInfo.sort_num;
                Object.assign(data, daysInfo);
                if (daysInfo.task_type === 1) {
                    return (0, utils_1.resOk)(data);
                }
                else {
                    sql = `select * from ${constants_1.DB_PRE}checkin_task where id in (${daysInfo.task_list})`;
                }
            }
            else {
                if (!curChapter.task_list) {
                    return (0, utils_1.resOk)(data);
                }
                if (curChapter.checkin_type === 1) {
                    const taskId = curChapter.task_list.split(',')[daysIdx];
                    sql = `select * from ${constants_1.DB_PRE}checkin_task where id=?`;
                    params = [taskId];
                }
                else {
                    sql = `select * from ${constants_1.DB_PRE}checkin_task where id in (${curChapter.task_list})`;
                }
            }
            if (sql) {
                const taskResult = await this.entityManager.query(sql, params);
                data.task_list = taskResult;
            }
            return (0, utils_1.resOk)(data);
        }
        catch (error) {
            return (0, utils_1.resErr)(500, error.message);
        }
    }
};
exports.CheckinService = CheckinService;
exports.CheckinService = CheckinService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectEntityManager)()),
    __metadata("design:paramtypes", [typeorm_2.EntityManager])
], CheckinService);
//# sourceMappingURL=Checkin.js.map
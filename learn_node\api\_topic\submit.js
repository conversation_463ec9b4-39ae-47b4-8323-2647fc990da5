/**
 * <AUTHOR>
 * @description 提交答题数据
 * @param type 1游客 2申请志愿者 3志愿者（正式学习模块）
 */

module.exports = async (req, res) => {
	let json = {
		code: 0,
		data: {},
		msg: '',
	};

	const factoryid = safeString(req.headers.factoryid);
	// const wxopenid = safeString(req.headers.wxopenid);
	const jobnum = safeString(req.headers.login_jobnum);
	const type = parseInt(req.body.type);

	if (!type || !factoryid || !jobnum) {
		json.code = 201;
		json.msg = '缺少参数';
		res.json(json);
		return;
	}

	let sql = '';
	switch(type) {
		// 游客
		case 1:
			const op1 = safeString(req.body.op1);
			const op2 = safeString(req.body.op2);

			if (!op1 && !op2) {
				json.code = 202;
				json.msg = '缺少参数';
				res.json(json);
				return;
			}
			
			const ip = await getIp(req);
			const values = [];
			querySql(`SELECT id, answer FROM ${databasePre}quelib_1 WHERE switch = '1'`, (err, result, conn) => {
				if (err) {
					res.json(err);
					return;
				}

				const avg = parseInt(100 / result.length);
				let sum1 = 0;
				let sum2 = 0;
				let pointStr1 = '';
				let pointStr2 = '';
				result.forEach((obj, idx) => {
					// 前测
					if (op1) {
						let point = 0;

						if (obj.answer == op1[idx]) {
							point = avg;
						} else {
							point = 0;
						}
						sum1 += point;
						pointStr1 += point + ' | ';
		
						values.push(`('${factoryid}', '${jobnum}', '${ip}', '1', '${idx + 1}', '${point}', now())`);
					}
		
					// 后测
					if (op2) {
						let point = 0;

						if (obj.answer == op2[idx]) {
							point = avg;
						} else {
							point = 0;
						}

						sum2 += point;
						pointStr2 += point + ' | ';
		
						values.push(`('${factoryid}', '${jobnum}', '${ip}', '2', '${idx + 1}', '${point}', now())`);
					}
				})
				
				
				conn.query(`insert into ${databasePre}youke_log(factory_id, jobnum, ip, type, que_id, point, create_time) values${values.join()};
				
				insert into ${databasePre}youke_data(factory_id, jobnum, ip, sum1, sum2, option_str1, option_str2, create_time) values('${factoryid}', '${jobnum}', '${ip}', '${sum1}', '${sum2}', '${pointStr1.substring(0, pointStr1.length - 3)}', '${pointStr2.substring(0, pointStr2.length - 3)}', now())`, (err, result) => {
					if (err) {
						res.json(err);
						return;
					}

					res.json(json);
				})
			})
			break;
	}
}
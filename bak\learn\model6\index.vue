<template>
	<view
		class="formal-model6"
		:class="{ 'swiper-question-wrap': questionStep }"
		:style="{ paddingTop: user.statusBarHeight }">
		<uni-icons
			type="back"
			class="nav-back"
			@click="goBack()"></uni-icons>

		<!-- 题目列表 -->
		<view
			class="nav-title"
			v-if="questionStep"
			:style="{ top: user.statusBarHeight }">
			案例题
		</view>
		<Question
			v-if="questionStep"
			v-show="showQuestion"
			ref="questionRef"
			class="swiper-question"
			:short="shortQuestion"
			:list="questionList"
			@midComplete="midComplete"
			prevText="上一页"
			nextText="下一页"
			@submit="submit" />

		<!-- 菜单 -->
		<view
			v-else
			v-show="showMenu">
			<view class="formal-menu">
				<view class="button block plain title f34">构建健康心环境</view>
				<view class="border-box mt30">
					<view
						class="button block light f36"
						:class="{ complete: completeStep >= i + 1, disabled: completeStep < i }"
						@click="menuClick(i)"
						v-for="(v, i) in menuArr"
						:key="i">
						{{ v }}
					</view>
				</view>
			</view>
			<view
				class="button small-submit"
				style="width: 300rpx"
				@click="goBack()">
				返回总目录
			</view>
		</view>

		<!-- ===============================Q1 -->
		<uni-popup
			ref="q11Ref"
			type="center">
			<view class="popup-fullscreen intro">
				<view class="intro-ticket">
					<view class="dots">
						<text
							class="dot"
							v-for="v in 14"
							:key="v"></text>
					</view>
					<view class="button text">在管理当中，我们除了需要关注员工的心理健康状态，及时发现员工的心理困扰与心理障碍，还需要通过积极管理为员工构建良好的心理环境。</view>
				</view>
				<view
					class="button plain round small-submit"
					@click="nextPage('q1-1', 'q2-1')">
					下一页
				</view>
			</view>
		</uni-popup>

		<!-- ===============================Q2 -->
		<uni-popup
			ref="q21Ref"
			type="center">
			<view class="popup-fullscreen">
				<Dialogue
					v-if="questionStep == 'q2-1'"
					:list="dialogue1"
					:title="dialogueTitle1"
					type="choose"
					@submit="nextPage('q2-1', 'q2-2')" />
			</view>
		</uni-popup>

		<uni-popup
			ref="q22Ref"
			type="center">
			<view class="popup-fullscreen jiexi">
				<view class="title">答案解析</view>
				<view class="intro-box">
					<view class="subtit">解析</view>
					<view class="info">
						<view>A选项：</view>
						<view>这种管理方式可能让员工心理压力增大，导致员工与管理者之间的关系越来越紧张，使上下级沟通进一步受阻，工作也就难以推进。</view>
						<view class="mt20">B选项：</view>
						<view>这种管理方式更加积极，能缓解员工的心理压力，改善员工与管理者之间的关系，促进上下级沟通，提升团队效率。</view>
					</view>
				</view>
				<view
					class="button small-submit"
					@click="nextPage('q2-2', 'q2-3')">
					下一页
				</view>
			</view>
		</uni-popup>

		<uni-popup
			ref="q23Ref"
			type="center">
			<view class="popup-fullscreen question">
				<view class="button plain block btn-tit">1.认识积极管理</view>

				<view class="button border block tips">
					管理者与员工朝夕相处，会对员工的心理状态产生直接而持续的影响。如果管理者采取积极的管理方式，能减少员工的心理压力，提升员工的心理健康水平。
				</view>

				<view
					class="button block round main-btn"
					@click="nextPage('q2-3', 'q2')">
					下一页
				</view>
			</view>
		</uni-popup>

		<!-- ===============================Q3 -->
		<uni-popup
			ref="q31Ref"
			type="center">
			<view class="popup-fullscreen question q3-1">
				<view class="button plain block btn-tit">2.积极管理的好处</view>

				<view class="button border block tips">积极管理能改善管理者-员工之间的关系，促进管理者与员工之间的积极交流，还能够营造员工之间的和谐关系，促进员工工作能力的提升。</view>

				<view
					class="button block round main-btn"
					@click="nextPage('q3-1', 'q3')">
					下一页
				</view>
			</view>
		</uni-popup>

		<!-- ===============================Q4 -->
		<uni-popup
			ref="q41Ref"
			type="center">
			<view class="popup-fullscreen question">
				<view class="button plain block btn-tit">怎么进行积极管理</view>
				<view class="formal-menu mt30">
					<view class="border-box">
						<view
							class="button block light f34"
							:class="{ complete: completeStep > 2 }"
							@click="nextPage('q4-1', 'q4-1-1')">
							有效地鼓励和肯定员工
						</view>
						<view
							class="button block light f34"
							:class="{ complete: completeStep > 2.1, disabled: completeStep < 2.1 }"
							@click="completeStep >= 2.1 && nextPage('q4-1', 'q4-2-1')">
							有效反馈员工的不足
						</view>
						<view
							class="button block light f34"
							:class="{ complete: completeStep > 2.2, disabled: completeStep < 2.2 }"
							@click="completeStep >= 2.2 && nextPage('q4-1', 'q4-3-1')">
							为员工提供支持与指导
						</view>
					</view>
				</view>
			</view>
		</uni-popup>

		<uni-popup
			ref="q411Ref"
			type="center">
			<view class="popup-fullscreen">
				<Dialogue
					v-if="questionStep == 'q4-1-1'"
					:list="dialogue2"
					:title="dialogueTitle2"
					type="choose"
					@submit="nextPage('q4-1-1', 'q4-1-2')" />
			</view>
		</uni-popup>

		<uni-popup
			ref="q412Ref"
			type="center">
			<view class="popup-fullscreen jiexi">
				<view class="title">答案解析</view>
				<view class="intro-box">
					<view class="subtit">解析</view>
					<view class="info">
						<view>A选项：</view>
						<view>一味强调小唐工作上的不足，忽视她其他方面的有点，可能会让小唐感到受挫、无助与压抑，使其工作压力剧增，以致于更容易出错。</view>
						<view class="mt20">B选项：</view>
						<view>全面地关注小唐的表现，发现与肯定小唐的优点，能让小唐更自信、愉快地工作，这样不仅有利于她工作能力的提升，还能使她的乐观态度感染其他员工。</view>
					</view>
				</view>
				<view
					class="button small-submit"
					@click="nextPage('q4-1-2', 'q4-1-3')">
					下一页
				</view>
			</view>
		</uni-popup>

		<uni-popup
			ref="q413Ref"
			type="center">
			<view class="popup-fullscreen question">
				<view class="button plain block btn-tit">1.多关注员工的优点</view>

				<view class="button border block tips mt30">
					大部分人都具有“向阳”的本性，希望得到肯定，也希望变得更优秀。员工也是如此，如果能获得管理者的肯定，他们的心理状态会更健康并且更具有工作活力。
				</view>

				<view
					class="button block round main-btn"
					@click="nextPage('q4-1-3', 'q4')">
					下一页
				</view>
			</view>
		</uni-popup>

		<uni-popup
			ref="q414Ref"
			type="center">
			<view class="popup-fullscreen intro">
				<view class="intro-ticket">
					<view class="dots">
						<text
							class="dot"
							v-for="v in 14"
							:key="v"></text>
					</view>
					<view class="button text">除了多关注和肯定员工的优点，管理者还需要注意用有效的方法来反馈他们的不足。</view>
				</view>
				<view
					class="button plain round small-submit"
					@click="submit()">
					下一页
				</view>
			</view>
		</uni-popup>

		<uni-popup
			ref="q421Ref"
			type="center">
			<view class="popup-fullscreen question">
				<view class="button plain block btn-tit">2.有效反馈员工的不足</view>

				<view class="button border block tips mt30">通过管理者的有效反馈，大多数员工能认识和接纳自己的不足，并主动改变。</view>

				<view
					class="button block round main-btn"
					@click="nextPage('q4-2-1', 'q4-2-2')">
					下一页
				</view>
			</view>
		</uni-popup>

		<uni-popup
			ref="q422Ref"
			type="center">
			<view class="popup-fullscreen">
				<Dialogue
					v-if="questionStep == 'q4-2-2'"
					:list="dialogue3"
					:title="dialogueTitle3"
					type="choose"
					@submit="nextPage('q4-2-2', 'q4-2-3')" />
			</view>
		</uni-popup>

		<uni-popup
			ref="q423Ref"
			type="center">
			<view class="popup-fullscreen jiexi">
				<view class="title">答案解析</view>
				<view class="intro-box">
					<view class="answer">正确答案：不能够</view>
					<view class="subtit">解析</view>
					<view class="info">夸张的形容、主观的评价、主观的揣测、情绪化的表达都容易引起员工反驳和自我辩护。如实、客观、理智地反馈问题才能让员工感到心服口服。</view>
				</view>
				<view
					class="button small-submit"
					@click="nextPage('q4-2-3', 'q41')">
					下一页
				</view>
			</view>
		</uni-popup>

		<uni-popup
			ref="q431Ref"
			type="center">
			<view class="popup-fullscreen question">
				<view class="button plain block btn-tit">3.为员工提供支持与指导</view>

				<view class="button border block tips mt30">有的员工虽然很重视自己的问题，也想要改变，却不知道应该如何改变，需要管理者提供进一步的支持与指导。</view>

				<view
					class="button block round main-btn"
					@click="nextPage('q4-3-1', 'q4-3-2')">
					下一页
				</view>
			</view>
		</uni-popup>

		<uni-popup
			ref="q432Ref"
			type="center">
			<view class="popup-fullscreen">
				<Dialogue
					v-if="questionStep == 'q4-3-2'"
					:list="dialogue4"
					:title="dialogueTitle4"
					type="choose"
					@submit="nextPage('q4-3-2', 'q4-3-3')" />
			</view>
		</uni-popup>

		<uni-popup
			ref="q433Ref"
			type="center">
			<view class="popup-fullscreen jiexi">
				<view class="title">答案解析</view>
				<view class="intro-box">
					<view class="answer">正确答案：不能够</view>
					<view class="subtit">解析</view>
					<view class="info">
						管理者需要意识到员工的认知、技能和经验往往都与自己具有差距，所以应该理解员工遇到的各类困难，并且在与员工进行充分交流与沟通后，提供员工所需要的支持与指导。弱化员工的困难或者提供不适合员工的建议都可能让员工感到更迷茫、无助。
					</view>
				</view>
				<view
					class="button small-submit"
					@click="nextPage('q4-3-3', 'q42')">
					下一页
				</view>
			</view>
		</uni-popup>

		<!-- ===============================Q5 -->
		<uni-popup
			ref="q51Ref"
			type="center">
			<view class="popup-fullscreen question">
				<view class="button plain block btn-tit">练习</view>

				<view class="button border block tips">在接下来一周中，请每天记录至少3条员工的良好表现，培养自己的积极管理思维。</view>

				<view
					class="button block round main-btn"
					@click="openComplete()">
					我知道了
				</view>
			</view>
		</uni-popup>

		<!-- 完成进度 弹窗 -->
		<Process
			ref="processRef"
			:total="4"
			:step="completeStep"
			:jobNum="user.loginJobnum"
			modelName="第五"
			:isComplete="questionStep == 'q5-1'"
			@complete="goBack()" />
	</view>
</template>

<script setup>
import { ref, reactive, nextTick } from 'vue';
import { storeToRefs } from 'pinia';
import { useUserStore } from '@/stores/user';

import Question from '@/components/Question/index.vue';
import Dialogue from '@/components/Dialogue/index.vue';
import Process from '@/components/Process/index.vue';

import formatQuestion from '@/utils/format-formal-question.js';
import { goBack, getLocal, setLocal, postError, modelComplete } from '@/utils/tools.js';

const { user } = storeToRefs(useUserStore());

const modelKey = 'model6-complete';
const completeStep = ref(getLocal(modelKey) || 0); // 已完成的模块step
const questionList = ref([]);
const shortQuestion = ref(false);
const questionStep = ref(null);
const showQuestion = ref(true);
const showMenu = ref(true);
const menuArr = ref(['认识积极管理', '积极管理的好处', '怎么进行积极管理', '小结']);

// 对话列表1
const dialogue1 = ref([
	{
		user: 0,
		msg: '更严厉地管理，让所有人服气'
	},
	{
		user: 1,
		msg: '更多地关注员工的优点与想法'
	}
]);
const dialogueTitle1 = ref(
	'小赵刚升为组长，感到管理工作很有挑战，在沉重的生产压力下，她希望用严格管理的方式提升全组绩效，可大家却不理解和配合。|你认为，以下哪种管理方式更有助于小赵的工作呢？'
);
// 对话列表2
const dialogue2 = ref([
	{
		user: 0,
		msg: '你应该把更多心思都放在工作上，别只顾着和人搞好关系。'
	},
	{
		user: 1,
		msg: '你性格好、乐于助人，我们都喜欢你，工作上也多问问大家，会进步得更快。'
	}
]);
const dialogueTitle2 = ref('小唐刚参加工作不久，她开朗、乐观、善良，乐于助人，但在工作上还缺乏经验，效率低，易出错。|你认为管理者应该如何与小唐沟通？');
// 对话列表3
const dialogue3 = ref([
	{
		user: 0,
		msg: '能够'
	},
	{
		user: 1,
		msg: '不能够'
	}
]);
const dialogueTitle3 = ref('小黄这个月迟到次数增多。管理者以下的反馈方式，能够让小黄真正认识到自身问题吗？|“你怎么老是迟到。”|“你是不是不想干了。”|“你是我们组最不自律的人。”');
// 对话列表4
const dialogue4 = ref([
	{
		user: 0,
		msg: '不能够'
	},
	{
		user: 1,
		msg: '能够'
	}
]);
const dialogueTitle4 = ref(
	'小宇也知道自己容易犯同一个错误，但就是不知道如何避免。你认为以下说法能够为小宇提供帮助吗？|“这事明明很简单啊。”|“不是已经教过你怎么做了吗？”|“你自己好好琢磨一下吧。”'
);

const answerModel63 = ref([]); // 第三章节的答案

const q11Ref = ref();

const q21Ref = ref();
const q22Ref = ref();
const q23Ref = ref();

const q31Ref = ref();

const q41Ref = ref();
const q411Ref = ref();
const q412Ref = ref();
const q413Ref = ref();
const q414Ref = ref();
const q421Ref = ref();
const q422Ref = ref();
const q423Ref = ref();
const q431Ref = ref();
const q432Ref = ref();
const q433Ref = ref();

const q51Ref = ref();

const processRef = ref();
const questionRef = ref();

const refs = reactive({
	q11Ref,

	q21Ref,
	q22Ref,
	q23Ref,

	q31Ref,

	q41Ref,
	q411Ref,
	q412Ref,
	q413Ref,
	q414Ref,
	q421Ref,
	q422Ref,
	q423Ref,
	q431Ref,
	q432Ref,
	q433Ref,

	q51Ref
});

function menuClick(i) {
	if (completeStep.value < i) return;

	if (i === 0) {
		q11Ref.value.open();
	} else if (i === 3) {
		nextPage('', 'q' + (i + 2));
	} else {
		questionStep.value = `q${i + 2}-1`;
		refs[questionStep.value.replace(/-/g, '') + 'Ref'].open();
	}
}

async function nextPage(current, next, noSwipe) {
	// 格式化ref字符
	let currentRef = '';
	if (current?.includes('-')) {
		currentRef = current.replace(/-/g, '') + 'Ref';
	}
	let nextRef = '';
	if (next?.includes('-')) {
		nextRef = next.replace(/-/g, '') + 'Ref';
	}

	if (next.includes('-')) {
		questionStep.value = next;
		refs[nextRef].open();
		refs[currentRef]?.close();
	} else {
		if (showQuestion.value) {
			if (next === 'q2' || next === 'q41') {
				shortQuestion.value = true;
			} else {
				shortQuestion.value = false;
			}

			// 首次进入答题
			const list = await ajax(`question/${next.replace('q', 'model6-')}.json`);
			questionList.value = formatQuestion(list);
		} else {
			// 答题中途切换出去，然后切回来
			showQuestion.value = true;
			if (!noSwipe) {
				questionRef.value.addSwiperIdx();
			}
		}
		questionStep.value = next;
		refs[currentRef]?.close();
	}
}

// 做题中途切出
function midComplete() {
	// if (questionStep.value === 'q4') {
	// 	questionStep.value = 'q4-1-2';
	// }
	// refs[questionStep.value].open();
	// setTimeout(() => {
	// 	showQuestion.value = false;
	// }, 300)
}

// 全部做完
async function submit(arr) {
	switch (questionStep.value) {
		case 'q4':
			questionStep.value = 'q4-1-4';
			q414Ref.value.open();
			setLocal('answer-model6-31', { answer: arr.map((o) => o.checked.join('')).join('-'), rights: arr.filter((o) => o.correct).length });
			setTimeout(() => {
				showQuestion.value = false;
			}, 300);
			break;

		case 'q41':
			modelComplete(modelKey, 2.2, completeStep);
			showQuestion.value = true;
			q41Ref.value.open();
			setLocal('answer-model6-32', { answer: arr.map((o) => o.checked.join('')).join('-'), rights: arr.filter((o) => o.correct).length });
			setTimeout(() => {
				showQuestion.value = false;
			}, 300);
			break;

		case 'q2':
		case 'q3':
		case 'q4-1-4':
		case 'q42':
		case 'q5':
			showMenu.value = true;
			if (questionStep.value === 'q2') {
				modelComplete(modelKey, 1, completeStep);
				processRef.value.open();
				setLocal('answer-model6-1', { answer: arr.map((o) => o.checked.join('')).join('-'), rights: arr.filter((o) => o.correct).length });
			} else if (questionStep.value === 'q3') {
				modelComplete(modelKey, 2, completeStep);
				processRef.value.open();
				setLocal('answer-model6-2', { answer: arr.map((o) => o.checked.join('')).join('-'), rights: arr.filter((o) => o.correct).length });
			} else if (questionStep.value === 'q4-1-4') {
				modelComplete(modelKey, 2.1, completeStep);
				showQuestion.value = true;
				q41Ref.value.open();
				setTimeout(() => {
					questionStep.value = 'q4-1';
				}, 250);
			} else if (questionStep.value === 'q42') {
				modelComplete(modelKey, 3, completeStep);
				showQuestion.value = true;
				processRef.value.open();
				setLocal('answer-model6-33', { answer: arr.map((o) => o.checked.join('')).join('-'), rights: arr.filter((o) => o.correct).length });
			} else if (questionStep.value === 'q5') {
				// 提交结果
				const op4 = JSON.stringify({ answer: arr.map((o) => o.checked.join('')).join('-'), rights: arr.filter((o) => o.correct).length });
				const model631 = getLocal('answer-model6-31');
				const model632 = getLocal('answer-model6-32');
				const model633 = getLocal('answer-model6-33');
				const { code, error } = await ajax({
					// url: 'api/api.php?a=putModule6',
					url: 'learn/submit',
					method: 'post',
					showLoading: true,
					data: {
						module: 6,
						jobnum: user.value.loginJobnum,
						op1: JSON.stringify(getLocal('answer-model6-1')),
						op2: JSON.stringify(getLocal('answer-model6-2')),
						op3: JSON.stringify({
							answer: [model631.answer, model632.answer, model633.answer].join('-'),
							rights: model631.rights + model632.rights + model633.rights
						}),
						op4
					}
				});

				// 异常处理
				if (code || error) {
					postError()
						.then(() => {
							submit(arr);
						})
						.catch(() => {});
				} else {
					modelComplete(modelKey, 4, completeStep);
					q51Ref.value.open();
					refs[questionStep.value.replace(/-/g, '') + 'Ref']?.close();
					setTimeout(() => {
						questionStep.value = null;
						setTimeout(() => {
							questionStep.value = 'q5-1';
						}, 50);
					}, 200);
				}
			}
			refs[questionStep.value.replace(/-/g, '') + 'Ref']?.close();
			setTimeout(() => {
				questionStep.value = null;
			}, 200);
			break;
	}
}

function openComplete() {
	// 需要等复制完成以后再打开弹框，避免isComplete=false
	questionStep.value = 'q5-1';
	nextTick(() => {
		processRef.value.open();
	});
}
</script>

<style lang="scss">
@import 'index.scss';
</style>

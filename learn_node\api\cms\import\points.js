/**
 * @description 导入 - 其它积分
 */

module.exports = (req, res) => {
	let json = {
		code: 0,
		data: {},
		msg: '',
	};

	const user_group = req.headers.user_group === undefined ? 1 : parseInt(req.headers.user_group); // 用户组，0超级管理员，1工厂管理员
	const file = req.file;
	var factory_id = parseInt(req.body.factory_id);

	if (!file) {
		json.code = 201;
		json.msg = '缺少参数';
		res.json(json);
		return;
	}

	if (!user_group && !factory_id) {
		json.code = 202;
		json.msg = '请先选择要导入的工厂';
		res.json(json);
		return;
	}

	const xlsx = require('node-xlsx').default;
	const excel = xlsx.parse(file.buffer);

	if (user_group === 1) {
		// 工厂管理员、筛选当前工厂的
		factory_id = req.headers.factoryid;
	}

	let valuesArr = [];
	excel[0].data.forEach((value, index) => {
		if (!index) return; // 表头直接跳过

		// 取出每列的数据
		let [jobnum, score, remark] = value;

		if (!jobnum || !jobnum.toString().trim() || !parseInt(score)) return;
		valuesArr.push(`(${factory_id}, '${jobnum.toString().trim()}', 0, ${parseInt(score)}, '${safeString(remark)}', now())`);
	});

	querySql(`insert into ${databasePre}user_points(factory_id, jobnum, type, score, remark, create_time) values${valuesArr.join()}`, (err, result) => {
		if (err) {
			res.json(err);
			return;
		}

		res.json(json);
	});
}
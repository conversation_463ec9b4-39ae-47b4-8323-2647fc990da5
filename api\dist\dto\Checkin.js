"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CheckinLogInfoDto = exports.UserInfoQueryDto = exports.ActivityDetailQueryDto = exports.CheckinTestSubmitDto = exports.CheckinTestAnswerDto = void 0;
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const validate_1 = require("../validate");
class CheckinTestAnswerDto {
}
exports.CheckinTestAnswerDto = CheckinTestAnswerDto;
__decorate([
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], CheckinTestAnswerDto.prototype, "topic_id", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], CheckinTestAnswerDto.prototype, "score", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CheckinTestAnswerDto.prototype, "value", void 0);
class CheckinTestSubmitDto {
}
exports.CheckinTestSubmitDto = CheckinTestSubmitDto;
__decorate([
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], CheckinTestSubmitDto.prototype, "period_id", void 0);
__decorate([
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], CheckinTestSubmitDto.prototype, "test_id", void 0);
__decorate([
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ArrayNotEmpty)(),
    (0, validate_1.IsArrayOfObjects)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => CheckinTestAnswerDto),
    __metadata("design:type", Array)
], CheckinTestSubmitDto.prototype, "answer_list", void 0);
class ActivityDetailQueryDto {
}
exports.ActivityDetailQueryDto = ActivityDetailQueryDto;
__decorate([
    (0, class_transformer_1.Transform)(({ value }) => Number(value)),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], ActivityDetailQueryDto.prototype, "period_id", void 0);
class UserInfoQueryDto {
}
exports.UserInfoQueryDto = UserInfoQueryDto;
__decorate([
    (0, class_transformer_1.Transform)(({ value }) => Number(value)),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], UserInfoQueryDto.prototype, "period_id", void 0);
class CheckinLogInfoDto {
}
exports.CheckinLogInfoDto = CheckinLogInfoDto;
__decorate([
    (0, class_transformer_1.Transform)(({ value }) => Number(value)),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], CheckinLogInfoDto.prototype, "period_id", void 0);
//# sourceMappingURL=Checkin.js.map
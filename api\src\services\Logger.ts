import { Injectable } from '@nestjs/common';
import { createLogger, format, transports } from 'winston';
import 'winston-daily-rotate-file';

import { timeFmt } from '../utils/tools';

const transport = new transports.DailyRotateFile({
  filename: 'logs/%DATE%.log', // 日志文件名格式
  datePattern: 'YYYYMMDD-HH', // 按天分隔
  zippedArchive: true, // 是否压缩归档文件
  maxSize: '30m', // 单个日志文件的最大大小
  maxFiles: '30d', // 保留最近 30 天的日志
});

@Injectable()
export class LoggerService {
  private logger = createLogger({
    level: 'info',
    format: format.combine(
      format.timestamp(),
      format.printf(({ timestamp, message }) => {
        const ts = new Date(timestamp as string).getTime();
        return `${timeFmt(ts, 'I:S.ms')} ${message}`;
      }),
    ),
    transports: [transport],
  });

  log(message: string) {
    this.logger.info(message);
  }

  error(message: string) {
    this.logger.error(message);
  }
}
import { Injectable } from '@nestjs/common';
import { InjectEntityManager } from '@nestjs/typeorm';
import { EntityManager } from 'typeorm';

import { DB_PRE } from '../constants';
import { resOk, resErr } from '../utils';
import { CustomHeadersDto } from '../dto';

@Injectable()
export class UserService {
  constructor(@InjectEntityManager() private readonly entityManager: EntityManager) {}

  /**
   * 用户积分数据
   */
  async scoreInfo(headers: CustomHeadersDto): Promise<ResObj> {
    const { login_jobnum, factoryid } = headers;
    
    if (!factoryid || !login_jobnum) {
      return resErr(201, '缺少参数');
    }

    try {
      // 志愿者培训
      const learnScore = await this.entityManager.query(`
        select sum(ifnull(module1, 0) + ifnull(module2, 0) + ifnull(module3, 0) + ifnull(module4, 0) + ifnull(module5, 0) + ifnull(module6, 0) + ifnull(module7, 0)) as score 
        from ${DB_PRE}allpoint_data 
        where factory_id='${factoryid}' and jobnum='${login_jobnum}'
      `);

      // 打卡活动
      const checkinScore = await this.entityManager.query(`
        select ifnull(sum(points_total), 0) as score, count(id) as times 
        from ${DB_PRE}checkin_rank 
        where factory_id='${factoryid}' and jobnum='${login_jobnum}'
      `);

      // 服务记录
      const serviceScore = await this.entityManager.query(`
        select ifnull(sum(points), 0) as score, count(id) as times 
        from ${DB_PRE}service_log 
        where factory_id='${factoryid}' and jobnum='${login_jobnum}' and is_del=0 and check_status<2
      `);

      // 其它积分
      const otherScore = await this.entityManager.query(`
        select ifnull(sum(score), 0) as score 
        from ${DB_PRE}user_points 
        where factory_id='${factoryid}' and jobnum='${login_jobnum}' and type=0
      `);

      const data = {
        training: { score: learnScore[0]?.score || 0 },
        checkin: checkinScore[0],
        service: serviceScore[0],
        other: otherScore[0],
      };

      return resOk(data);
    } catch (error) {
      return resErr(500, error.message);
    }
  }
}

import { Controller, Get, Query } from '@nestjs/common';

import { ModuleQueryDto, DataQueryDto, DataAdminQueryDto } from '../dto/Training';
import { TrainingService } from '../services/Training';

@Controller('training')
export class TrainingController {
  constructor(private readonly service: TrainingService) {}

  /** 模块学习情况 */
  @Get('module')
  async getModule(@Query() query: ModuleQueryDto): Promise<ResObj> {
    return this.service.getModule(query);
  }

  /** 志愿者培训-前后测数据 */
  @Get('test-data')
  async getTestData(@Query() query: DataQueryDto): Promise<ResObj> {
    return this.service.getTestData(query);
  }

  /** 志愿者培训数据统计 */
  @Get('data')
  async getData(@Query() query: DataQueryDto): Promise<ResObj> {
    return this.service.getData(query);
  }

  /** 志愿者培训统计数据-超管 */
  @Get('data-admin')
  async getDataAdmin(): Promise<ResObj> {
    return this.service.getDataAdmin();
  }
}

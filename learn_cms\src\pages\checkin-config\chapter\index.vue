<!--
 * <AUTHOR>
 * @LastEditTime: 2023-11-27 22:10:04
 * @description 打卡活动-章节管理
-->
<template>
  <div class="checkin-chapter ui-layout-col">
    <afc-filter-bar
      class="mb10"
      size="small"
      :field="searchField"
      :init-filter="searchForm"
      :show-button="false"
      @valueChange="searchForm = $event; handelFilter()" />

    <div class="flex1">
      <x-table
        :data-set="dataSet"
        :row-class-name="tableRowClassName"
        :load-list-failed="loadFailed"
        @reload="init">
        <template v-for="obj in tableCols">
          <el-table-column
            v-if="obj.prop === 'factory_id'"
            v-bind="obj"
            :key="obj.prop">
            <template slot-scope="scope">
              {{ factoryList.find(o => o.value === scope.row.factory_id) ? factoryList.find(o => o.value === scope.row.factory_id).label : scope.row.factory_id }}
            </template>
          </el-table-column>
          <el-table-column
            v-else-if="obj.prop === 'directory_img'"
            v-bind="obj"
            :key="obj.prop">
            <template slot-scope="scope">
              <el-image
                style="width: 100px; height: 58px"
                :src="imgHost + scope.row.directory_img"
                :preview-src-list="[imgHost + scope.row.directory_img]" />
            </template>
          </el-table-column>
          <el-table-column
            v-else-if="obj.prop === 'sort'"
            v-bind="obj"
            :key="obj.prop">
            <template slot-scope="scope">
              <div class="sort-icons">
                <!-- <div class="sort-icons__icon">
                <a class="i-top" :class="{ disable: scope.row.sort_num === 1 }" title="置顶"></a>
              </div> -->
                <div
                  class="sort-icons__icon"
                  @click="sort(scope.row, -1)">
                  <a
                    class="i-arrow"
                    :class="{ disable: scope.row.sort_num === 1 }"
                    title="上移"></a>
                </div>
                <div
                  class="sort-icons__icon"
                  @click="sort(scope.row, 1)">
                  <a
                    class="i-arrow down"
                    :class="{ disable: scope.row.sort_num == dataSet.list.length }"
                    title="下移"></a>
                </div>
              <!-- <div class="sort-icons__icon">
                <a class="i-top down" :class="{ disable: scope.row.sort_num == dataSet.list.length }" title="置尾"></a>
              </div> -->
              </div>
            </template>
          </el-table-column>
          <el-table-column
            v-else-if="obj.prop === 'operation'"
            v-bind="obj"
            :key="obj.prop">
            <template slot-scope="scope">
              <template v-if="scope.row.state === 0">
                <a
                  class="mr10"
                  @click="popupAdd(scope.row)">
                  编辑
                </a>
                <a
                  class="mr10"
                  @click="editActive(scope.row, true)">
                  激活
                </a>
                <a @click="popupDel(scope.row)">删除</a>
              </template>
              <a
                v-else
                @click="editActive(scope.row, false)">
                取消激活
              </a>
            </template>
          </el-table-column>
          <el-table-column
            v-else
            v-bind="obj"
            :key="obj.prop" />
        </template>
      </x-table>
    </div>
  </div>
</template>

<script>
import afcFilterBar from 'afc-filter-bar-vue2';
import { cloneDeep } from 'lodash';
import { mapMutations, mapState } from 'vuex';

import {
  searchField,
  tableCols,
} from './table-cols';

import xTable from '@/components/x-table';
import mixinFactoryList from '@/function/mixin-factory-list';

export default {
  components: {
    afcFilterBar,
    xTable,
  },
  mixins: [mixinFactoryList],
  data() {
    return {
      searchForm: {},

      dataSet: {},
      actionUrl: process.env.VUE_APP_apiHost + 'cms/import/user',
      periodList: [], // 活动期数列表

      // 添加用户的对话框的显示控制
      showPopAdd: false,
      // 添加表单的数据
      addForm: {},
      // 添加用户的验证规则
      addRules: {
        jobnum: [
          {
            required: true,
            message: '请选择输入工号'
          }
        ],
        sex: [
          {
            required: true,
            message: '请选择性别'
          }
        ],
        user_type: [
          {
            required: true,
            message: '请选择用户身份'
          }
        ],
      },
    };
  },
  computed: {
    ...mapState(['uploadingFile']),
    headers() {
      if (this.user.token) {
        return {
          token: this.user.token,
          userid: this.user.userid,
          factoryid: this.user.factory_id,
          user_group: this.user.user_group,
        };
      } else {
        return {};
      }
    },
    tableCols() {
      const arr = cloneDeep(tableCols);
      // if (this.user.user_group) {
      //   // 工厂管理员，不能筛选工厂
      //   arr.splice(2, 1);
      // }
      return arr;
    },
    searchField() {
      const arr = cloneDeep(searchField);

      arr[2].formOpts.optsList = this.periodList.map(o => {
        return {
          ...o,
          label: `${o.label}[${o.value}]`,
        };
      });

      if (this.user.user_group) {
        // 工厂管理员，不能筛选工厂
        arr.splice(1, 1);
      } else {
        arr[1].formOpts.optsList = this.factoryList;
        arr[1].formOpts.events = {
          change: e => {
            this.onFactoryChange(e);
          },
        };
      }

      arr[arr.length - 1].formOpts.events.click = () => {
        this.popupAdd();
      };
      return arr;
    },
  },
  created() {
  },
  methods: {
    ...mapMutations(['setUploadingFile']),
    async init() {
      await this.getPeriodList();
      this.getList();
    },
    handelFilter() {
      this.dataSet.page_num = 1;
      this.getList();
    },
    onFactoryChange(factory_id) {
      this.periodList = [];
      this.getPeriodList(factory_id);
    },

    // 获取期数列表
    async getPeriodList(factory_id) {
      const { code, data } = await ajax({
        url: 'checkin/period',
        data: {
          state: 2,
          factory_id: factory_id || this.searchForm.factory_id,
        },
      });
      if (code) {
        this.loadFailed = true;
        return;
      }
      this.loadFailed = false;

      data.list.length && this.$set(this.searchForm, 'period_id', data.list[0].id);
      this.periodList = data.list.map(o => {
        return {
          label: o.code_name,
          value: o.id,
        };
      });
    },

    // 获取列表数据
    async getList() {
      if (!this.periodList.length) return;

      let search = cloneDeep(this.searchForm);
      delete search.add;

      const { code, data } = await ajax({
        url: 'checkin/chapter',
        data: { ...search },
      });
      if (code) {
        this.loadFailed = true;
        return;
      }
      this.loadFailed = false;

      this.dataSet = data;
    },

    // 排序
    async sort(o, step) {
      // 第一个和最后一个
      if ((o.sort_num === 1 && step < 0) || (o.sort_num === this.dataSet.list.length && step > 0)) return;

      const { code, error } = await ajax({
        url: 'checkin/chapter',
        method: 'put',
        data: {
          type: 'sort',
          id: o.id,
          step,
          cur_sort: o.sort_num,
          period_id: this.searchForm.period_id,
          factory_id: this.searchForm.factory_id,
        },
      });
      if (code || error) return;

      this.getList();
    },

    tableRowClassName({ row }) {
      if (row.state === 0) {
        return 'disable';
      } else {
        return '';
      }
    },

    // 添加/编辑用户
    popupAdd(o = {}) {
      this.$router.push(`chapter-add?factory_id=${this.searchForm.factory_id || ''}&period_id=${o.period_id || this.searchForm.period_id}&period_name=${o.period_name || this.periodList.find(o => o.value === this.searchForm.period_id)?.label || ''}&id=${o.id || ''}`);
    },

    // 删除确认
    popupDel(row) {
      this.$confirm(`<div class="mb20">确定删除 ${row.title} 吗？
        <p class="poa">删除后将无法恢复。</p>
      </div>`, '删除提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true,
      }).then(async () => {
        const { code, error } = await ajax({
          url: 'checkin/chapter',
          method: 'delete',
          data: {
            id: row.id,
            period_id: this.searchForm.period_id,
            factory_id: this.searchForm.factory_id,
          },
        });
        if (code || error) return;

        this.getList();
        this.$message.success('操作成功');
      })
        .catch(() => {});
    },

    // 激活/取消激活
    editActive(row, type) {
      const cancelText = !type ? '取消' : '';
      this.$confirm(`确定${cancelText}激活 ${row.title} 吗？`, cancelText + '激活提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(async () => {
        const { code, error } = await ajax({
          url: 'checkin/chapter',
          method: 'put',
          data: {
            type: 'active',
            state: type ? 1 : 0,
            id: row.id,
            factory_id: this.searchForm.factory_id,
          },
        });
        if (code || error) return;

        this.getList();
        this.$message.success('操作成功');
      })
        .catch(() => {});
    },
  },
};
</script>

<style lang="scss">
  @import './index';
</style>

import { Controller, Post, Body, Headers } from '@nestjs/common';

import { CustomHeadersDto } from '../dto';
import { VolunteerApplySubmitDto } from '../dto/Volunteer';
import { VolunteerService } from '../services/Volunteer';
import { validateDto } from '../filters/validate-dto';

@Controller('volunteer')
export class VolunteerController {
  constructor(private readonly service: VolunteerService) {}

  /** 志愿者申请-提交 */
  @Post('test-submit')
  async testSubmit(
    @Body() body: VolunteerApplySubmitDto,
    @Headers() headers: CustomHeadersDto,
  ): Promise<ResObj> {
    const headerDto = await validateDto(CustomHeadersDto, headers);
    return this.service.testSubmit(body, headerDto);
  }
}

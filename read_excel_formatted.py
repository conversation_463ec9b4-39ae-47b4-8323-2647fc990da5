import sys
import os
import zipfile
import xml.etree.ElementTree as ET

def read_xlsx_content(file_path):
    """Read and display content from an XLSX file in a formatted way."""
    try:
        # Open the XLSX file as a ZIP archive
        with zipfile.ZipFile(file_path, 'r') as xlsx:
            # Get the sheet data
            sheet_data = xlsx.read('xl/worksheets/sheet1.xml')
            
            # Parse the XML
            root = ET.fromstring(sheet_data)
            
            # Define the namespace
            ns = {'s': 'http://schemas.openxmlformats.org/spreadsheetml/2006/main'}
            
            # Get the shared strings if available
            shared_strings = []
            if 'xl/sharedStrings.xml' in xlsx.namelist():
                ss_data = xlsx.read('xl/sharedStrings.xml')
                ss_root = ET.fromstring(ss_data)
                for si in ss_root.findall('.//s:si', ns):
                    text_elements = si.findall('.//s:t', ns)
                    if text_elements:
                        shared_strings.append(''.join(t.text if t.text else '' for t in text_elements))
                    else:
                        shared_strings.append('')
            
            # Process the rows
            rows = []
            for row in root.findall('.//s:row', ns):
                cells = []
                for cell in row.findall('.//s:c', ns):
                    cell_value = ''
                    v_element = cell.find('s:v', ns)
                    
                    if v_element is not None and v_element.text:
                        # Check if it's a shared string
                        if cell.get('t') == 's' and shared_strings:
                            try:
                                idx = int(v_element.text)
                                if 0 <= idx < len(shared_strings):
                                    cell_value = shared_strings[idx]
                            except (ValueError, IndexError):
                                cell_value = v_element.text
                        else:
                            cell_value = v_element.text
                    
                    cells.append(cell_value)
                
                if cells:  # Only add non-empty rows
                    rows.append(cells)
            
            # Print the content in a more readable format
            print(f"文件: {os.path.basename(file_path)}")
            print(f"总行数: {len(rows)}")
            print("\n表格内容:")
            
            # Determine column widths for better formatting
            if rows:
                # Ensure all rows have the same number of columns
                max_cols = max(len(row) for row in rows)
                for row in rows:
                    while len(row) < max_cols:
                        row.append("")
                
                # Calculate column widths
                col_widths = [max(len(str(row[i])) for row in rows) for i in range(max_cols)]
                col_widths = [min(width, 20) for width in col_widths]  # Limit width to 20 chars
                
                # Print header row with separators
                header = rows[0]
                header_line = " | ".join(str(header[i]).ljust(col_widths[i])[:col_widths[i]] for i in range(len(header)))
                print(header_line)
                print("-" * len(header_line))
                
                # Print data rows
                for row in rows[1:20]:  # Print first 20 data rows
                    line = " | ".join(str(row[i]).ljust(col_widths[i])[:col_widths[i]] for i in range(len(row)))
                    print(line)
                
                if len(rows) > 21:
                    print("...")
                    # Print the last row
                    last_row = rows[-1]
                    line = " | ".join(str(last_row[i]).ljust(col_widths[i])[:col_widths[i]] for i in range(len(last_row)))
                    print(line)
    
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
        read_xlsx_content(file_path)
    else:
        print("Please provide the path to an Excel file")

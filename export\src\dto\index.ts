import { ValidatorConstraint, ValidatorConstraintInterface, registerDecorator } from 'class-validator';

// 自定义验证逻辑
@ValidatorConstraint({
  name: 'isOptionalNumber',
  async: false
})
class IsOptionalNumberConstraint implements ValidatorConstraintInterface {
  validate(value: any) {
    if (!value) {
      return true;
    }
    // 如果传了值，则必须是 number 类型
    return typeof value == 'number';
  }

  defaultMessage() {
    return 'must be an integer number';
  }
}

// 自定义装饰器
export function IsOptionalNumber() {
  return function (object: any, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      constraints: [],
      validator: IsOptionalNumberConstraint,
    });
  };
}
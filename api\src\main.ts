import * as fs from 'fs';

import { NestFactory } from '@nestjs/core';
import { ValidationPipe, NestApplicationOptions } from '@nestjs/common';
import { json } from 'express';

import { CustomValidationFilter } from './filters/custom-validation';
import { AppModule } from './app.module';

async function bootstrap() {
  const opts: NestApplicationOptions = { logger: ['error', 'warn'] };
  if (process.env.NODE_ENV === 'production') {
    // 读取 SSL/TLS 证书和私钥
    opts.httpsOptions = {
      key: fs.readFileSync('/www/wwwroot/node/ssl/node.employeehealth.cn.key', 'utf8'),
      cert: fs.readFileSync('/www/wwwroot/node/ssl/node.employeehealth.cn.crt', 'utf8'),
    };
  }
  const app = await NestFactory.create(AppModule, opts);

  // 启用 CORS
  app.enableCors();

  // 全局设置接受 JSON
  app.use(json()); // 默认限制 100kb，可以传入配置参数
  
  app.useGlobalPipes(new ValidationPipe({
    transform: true, // 自动将查询参数转换为 DTO 类型
    whitelist: false, // 自动移除无效的属性
  }));

  // 注册全局过滤器
  app.useGlobalFilters(new CustomValidationFilter());
  
  await app.listen(process.env.portApi);
  console.log(`${new Date().toLocaleString()} - http${process.env.NODE_ENV === 'production' ? 's' : ''} on port:${process.env.portApi}`);
}
bootstrap();

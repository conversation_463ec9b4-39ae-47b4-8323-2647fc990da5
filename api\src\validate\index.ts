import {
  registerDecorator,
  ValidationOptions,
  ValidationArguments,
} from 'class-validator';

export function IsArrayOfObjects(validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      name: 'isArrayOfObjects',
      target: object.constructor,
      propertyName: propertyName,
      options: {
        message: '每个元素必须是对象',
        ...validationOptions,
      },
      validator: {
        validate(value: any, args: ValidationArguments) {
          return (
            Array.isArray(value) &&
            value.every((item) => item !== null && typeof item === 'object' && !Array.isArray(item))
          );
        },
      },
    });
  };
}

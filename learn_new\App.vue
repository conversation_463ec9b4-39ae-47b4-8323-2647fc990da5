<script setup>
import { onLaunch } from '@dcloudio/uni-app';
import { useUserStore } from '@/stores/user';
import { useStartParams } from '@/stores/start-params';
import miniLogin from '@/utils/mini-login';
import { realImgSrc } from '@/utils/tools.js';

const { updateUserInfo } = useUserStore();
const { setStartParams } = useStartParams();

async function bindingFactory(factory_id, times) {
	const { code, error, data } = await ajax({
		url: 'user/binding-factory',
		method: 'post',
		data: {
			factory_id
		}
	});
	if (code || error) {
		// 最多请求5次
		if (times < 5) {
			setTimeout(() => {
				bindingFactory(factory_id, ++times);
			}, Math.pow(times, 2) * 1000);
		}
		return;
	}
}

onLaunch((opts) => {
	console.log('opts', opts);
	// const user = uni.getStorageSync('user') || {
	// 	img_host: VITE_IMG_HOST
	// };

	// 加载全局字体
	uni.loadFontFace({
		global: true,
		family: 'pingfang',
		source: realImgSrc('learn/pf.woff2')
	});
	// uni.loadFontFace({
	// 	global: true,
	// 	family: 'iconfont',
	// 	source: 'url("https://at.alicdn.com/t/c/font_3761357_fqs3vsa0fq7.woff2?t=1668518728906")',
	// })

	const { statusBarHeight, windowWidth } = uni.getWindowInfo();
	updateUserInfo('statusBarHeight', statusBarHeight + 'px');
	updateUserInfo('safePadHeight', statusBarHeight + 44 + 'px');
	updateUserInfo('windowWidth', windowWidth);

	const { osName } = uni.getDeviceInfo();
	updateUserInfo('osName', osName);

	// 小程序码中带的字符串参数转换成对象
	if (typeof opts.query.scene === 'string') {
		if (opts.query.scene.includes(':;')) {
			// 精简参数
			const [path, factory_id, jobnum, period_id] = decodeURIComponent(opts.query.scene).split(':;');
			opts.query.path = path;
			opts.query.factory_id = factory_id;
			opts.query.jobnum = jobnum;
			opts.query.period_id = period_id;
		} else {
			// 普通参数
			let arr = decodeURIComponent(opts.query.scene).split('&');
			arr.forEach((v) => {
				let val = v.split('=');
				opts.query[val[0]] = val[1];
			});
		}
	}
	setStartParams(opts.query);

	// #ifdef MP-WEIXIN
	// 登录
	miniLogin(() => {
		// 如果带上了工厂ID，将微信号绑定到工厂
		if (opts.query.factory_id) {
			updateUserInfo('factory_id', opts.query.factory_id);
			bindingFactory(opts.query.factory_id);
		}
	});

	// 强制更新小程序
	const updateManager = uni.getUpdateManager();
	// updateManager.onCheckForUpdate(function (res) {
	//   // 请求完新版本信息的回调
	//   console.log(res.hasUpdate);
	// });
	updateManager.onUpdateReady(() => {
		uni
			.showModal({
				title: '更新提示',
				content: '新版本已经准备好，马上重启体验吧',
				showCancel: false
			})
			.then((res) => {
				if (res.confirm) {
					updateManager.applyUpdate();
				}
			});
	});
	// #endif
});
</script>

<style lang="scss">
@import './app';
// @import "uview-ui/index.scss";
</style>

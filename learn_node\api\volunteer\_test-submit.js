/**
 * <AUTHOR>
 * @description 志愿者提交
 */

module.exports = async (req, res) => {
	let json = {
		code: 0,
		data: {},
		msg: '',
	};

	const login_jobnum = safeString(req.headers.login_jobnum);
	const factoryid = parseInt(req.headers.factoryid);
	const answer_list = req.body.answer_list;

	if (!login_jobnum || !factoryid || !answer_list?.length) {
		json.code = 201;
		json.msg = '缺少参数';
		res.json(json);
		return;
	}

	querySql(`select title, type, dimension, is_scored from ${databasePre}volunteer_test_topic order by sort_num asc;`, (err, result, conn) => {
		if (err) {
			res.json(err);
			return;
		}
		
		const list = result;
		if (answer_list.length !== list.length) {
			json.code = 202;
			json.msg = '题目数量不对';
			res.json(json);
			return;
		}

		const dimension = {}; // 维度
		const values = [];
		const diffIdxArr = [5,6,7,18]; // 反相计分的题数索引
		list.forEach((o, i) => {
			const answer = answer_list[i];
			
			// 如果参与计分
			if (o.is_scored) {
				if (diffIdxArr.includes(i)) {
					answer.score = 6 - answer.score;
				}

				if (!dimension[o.dimension]) {
					dimension[o.dimension] = [];
				}
				dimension[o.dimension].push(answer.score);
			}

			values.push(`(${factoryid}, '${login_jobnum}', ${answer.topic_id}, '${o.dimension || ''}', '${safeString(answer.value)}', ${answer.score || answer.score === 0 ? answer.score : null}, ${o.is_scored}, now(), '${o.title}', '${o.type}')`);
		})
		
		// 算分
		const dimension_list = [];
		const entries = Object.entries(dimension);
		entries.forEach(([label, arr]) => {
			let value = 0;
			if (label === '服务意愿') {
				value = (25 * (arr.reduce((a, b) => a + b) / arr.length)).toFixed(3);
			} else {
				value = (25 * ((arr.reduce((a, b) => a + b) / arr.length) - 1)).toFixed(3);
			}

			dimension_list.push({
				label,
				value,
			});
		})

		const scoreArr = dimension_list.filter(o => o.label !== '服务意愿');
		const total_score = (parseFloat(scoreArr[0].value) / 3 + parseFloat(scoreArr[1].value) / 3 + (parseFloat(scoreArr[2].value) + parseFloat(scoreArr[3].value) + parseFloat(scoreArr[4].value)) / 9).toFixed(2);

		// 插入数据
		execTrans([
			`insert into ${databasePre}shenqingzyz_data(factory_id, jobnum, avgA, avgB, avgC, avgD, avgE, avgF, create_time) values('${factoryid}', '${login_jobnum}', '${dimension_list[0].value}', '${dimension_list[1].value}', '${dimension_list[2].value}', '${dimension_list[3].value}', '${dimension_list[4].value}', '${dimension_list[5].value}', now())`,
			
			`insert into ${databasePre}volunteer_test_log(factory_id, jobnum, topic_id, dimension, answer, score, is_scored, create_time, topic_title, topic_type) values${values.join()};`
		], (err, re) => {
			if (err) {
				json.code = 205;
				json.msg = err.message;
				res.json(json);
				return;
			}

			json.data = {
				dimension_list, // 纬度得分
				total_score, // 总分
			}

			res.json(json);
		})
	})
}
@import '@/scss/mixins.scss';

.formal-model2{
	background-image: url('#{$imgHost}learn/model2_bg.jpg');	
	
	.q1-1{
		background-image: url('#{$imgHost}learn/question_intro_bg.png');
	}
	.q1-2, .q1-3{
		// background-image: url('#{$imgHost}learn/question_btntit_bg.png');
		.item{
			margin: 30rpx auto 0;
			width: 630rpx;
			height: 200rpx;
			font-size: 35rpx;
			padding: 0 40rpx;
		}
	}
	.q1-4{
		background-image: url('#{$imgHost}learn/q1-4_bg.png');
		.intro{
			background: url('#{$imgHost}learn/q1-4_intro.png') 0 0/100% 100%;
			width: 672rpx;
			height: 718rpx;
			margin-top: 100rpx;
		}
	}
	.q1-5{
		background-image: url('#{$imgHost}learn/question_btntit_bg.png');
		::v-deep .line0, ::v-deep .line1, ::v-deep .line2, ::v-deep .line3{
			&::after{
				content: '';
				position: absolute;
				left: 100%;
				width: 100rpx;
				height: 0;
				border-top: 6rpx dashed #fff;
				transform-origin: 0 -3rpx;
			}
		}
		::v-deep .line2{
			&::after{
				width: 250rpx;
				transform: rotate(66deg);
			}
		}
		::v-deep .line3{
			&::after{
				width: 250rpx;
				transform: rotate(-67deg);
			}
		}
	}
	
	.q2-2{
		::v-deep .p1{
			margin: -320rpx 0 0 -320rpx;
		}
		::v-deep .p2{
			margin: -320rpx 0 0 80rpx;
		}
		::v-deep .p3{
			margin: -210rpx 0 0 -100rpx;
			font-size: 28rpx;
			height: 66rpx;
		}
		::v-deep .p4{
			margin: -100rpx 0 0 -320rpx;
		}
		::v-deep .p5{
			margin: -100rpx 0 0 80rpx;
		}
		::v-deep .p6{
			margin: 10rpx 0 0 -100rpx;
			font-size: 28rpx;
			height: 66rpx;
		}
		::v-deep .p7{
			margin: 120rpx 0 0 -320rpx;
		}
		::v-deep .p0{
			margin: 120rpx 0 0 80rpx;
		}
	}
	.q2-5{
		background-image: url('#{$imgHost}learn/question_btntit_bg.png');
		::v-deep .line0, ::v-deep .line1, ::v-deep .line2, ::v-deep .line3{
			&::after{
				content: '';
				position: absolute;
				left: 100%;
				height: 0;
				border-top: 6rpx dashed #fff;
				transform-origin: 0 0;
				width: 300rpx;
				transform: rotate(-72deg);
			}
		}
		::v-deep .line0{
			&::after{
				width: 500rpx;
				transform: rotate(80.5deg);
			}
		}
		::v-deep .item{
			padding: 0 14rpx;
		}
	}
	
	.q3-2{
		background: rgb(243,253,254);
		.title{
			width: 600rpx;
			height: 160rpx;
			background: none;
		}
		.states{
			margin: 120rpx auto 0;
			width: 540rpx;
			display: flex;
			flex-wrap: wrap;
			justify-content: space-between;
			font-size: 32rpx;
		}
		.state{
			text-align: center;
			margin-bottom: 80rpx;
			position: relative;
			&:before{
				content: '';
				display: block;
				width: 202rpx;
				height: 202rpx;
				margin: 0 auto 10rpx;
				background: url('#{$imgHost}learn/state1.png') 0 0/100% 100%;
				border: solid 8rpx #9aa6b7;
				border-radius: 50%;
			}
			// .check{
			// 	position: absolute;
			// 	right: -8rpx;
			// 	top: -8rpx;
			// 	font-size: 74rpx !important;
			// 	color: #478cfe !important;
			// 	z-index: 1;
			// 	display: none;
			// 	&::after{
			// 		content: '';
			// 		position: absolute;
			// 		left: 8rpx;
			// 		top: 8rpx;
			// 		right: 8rpx;
			// 		bottom: 8rpx;
			// 		background: #fff;
			// 		border-radius: 50%;
			// 		z-index: -1;
			// 	}
			// }
			.check .uni-icons{
				position: absolute;
				right: -8rpx;
				top: -8rpx;
				font-size: 74rpx !important;
				color: #478cfe !important;
				z-index: 1;
				display: none;
				&::after{
					content: '';
					position: absolute;
					left: 8rpx;
					top: 8rpx;
					right: 8rpx;
					bottom: 8rpx;
					background: #fff;
					border-radius: 50%;
					z-index: -1;
				}
			}
			
			&.done{
				&:before{
					border-color: #478cfe;
				}
				// .check{
				.check .uni-icons{
					display: block;
				}
			}
		}
		.s2{
			&:before{
				background-image: url('#{$imgHost}learn/state2.png');
			}
		}
		.s3{
			&:before{
				background-image: url('#{$imgHost}learn/state3.png');
			}
		}
		.s4{
			&:before{
				background-image: url('#{$imgHost}learn/state4.png');
			}
		}
	}
	.q3-3{
		.img2{
			width: 628rpx;
			height: 674rpx;
			background-image: url('#{$imgHost}learn/double2.png');
		}
	}
}
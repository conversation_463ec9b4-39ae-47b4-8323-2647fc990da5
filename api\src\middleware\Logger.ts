import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';

import { getIp } from '../utils/tools';
import { LoggerService } from '../services/Logger';

@Injectable()
export class LoggerMiddleware implements NestMiddleware {
  constructor(private readonly logger: LoggerService) {} // 注入 LoggerService

  async use(req: Request, res: Response, next: NextFunction) {
    const rnd = Math.random().toString()
      .substring(2);
    const log = this.logger.log.bind(this.logger);
    const originalSend = res.send; // 保存原始的 res.send 方法

    res.send = function (body) {
      log(rnd + '-res:' + body);
      return originalSend.call(this, body);
    };

    const {
      method,
      originalUrl,
      body,
      headers
    } = req;
    const ip = await getIp(req);

    log(`${rnd}-${ip}-[${method}] req:${decodeURIComponent(originalUrl)} ${method === 'GET' ? '' : JSON.stringify(body)} headers:${JSON.stringify(headers)}`);
    next();
  }
}
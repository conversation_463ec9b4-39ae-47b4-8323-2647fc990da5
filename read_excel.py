import sys
import os
import struct

def read_excel_header(file_path):
    """Read and display basic information about an Excel file."""
    try:
        with open(file_path, 'rb') as f:
            # Read the first 8 bytes to check if it's an Excel file
            header = f.read(8)
            
            # Check if it's an XLSX file (ZIP format)
            if header.startswith(b'PK\x03\x04'):
                print(f"File: {os.path.basename(file_path)}")
                print("Format: XLSX (Office Open XML)")
                
                # Try to read the content types to get more info
                f.seek(0)
                content = f.read()
                
                # Look for sheet names
                sheet_names = []
                sheet_marker = b'xl/worksheets/sheet'
                pos = 0
                while True:
                    pos = content.find(sheet_marker, pos)
                    if pos == -1:
                        break
                    end_pos = content.find(b'.xml', pos)
                    if end_pos != -1:
                        sheet_num = content[pos + len(sheet_marker):end_pos].decode('utf-8', errors='ignore')
                        sheet_names.append(f"Sheet{sheet_num}")
                    pos = end_pos if end_pos != -1 else pos + 1
                
                if sheet_names:
                    print(f"Sheets found: {', '.join(sheet_names)}")
                
                # Look for text content
                print("\nSample text content found in file:")
                text_chunks = []
                pos = 0
                while len(text_chunks) < 20:  # Limit to 20 chunks
                    # Find potential text (sequences of printable ASCII)
                    text_start = None
                    for i in range(pos, min(len(content) - 4, pos + 10000)):
                        # Look for sequences that might be text
                        if all(32 <= b <= 126 for b in content[i:i+4]):
                            text_start = i
                            break
                    
                    if text_start is None:
                        break
                    
                    # Extract potential text (up to 50 chars)
                    text_end = text_start
                    while text_end < len(content) and text_end - text_start < 50:
                        if 32 <= content[text_end] <= 126 or content[text_end] in (9, 10, 13):  # ASCII printable or tab/newline
                            text_end += 1
                        else:
                            break
                    
                    if text_end - text_start >= 4:  # Only keep if at least 4 chars
                        text = content[text_start:text_end].decode('utf-8', errors='ignore')
                        if text not in text_chunks and not text.isspace() and len(text.strip()) > 3:
                            text_chunks.append(text)
                    
                    pos = text_end + 1
                    if pos >= len(content):
                        break
                
                for i, chunk in enumerate(text_chunks, 1):
                    print(f"{i}. {chunk}")
                
            else:
                print(f"File: {os.path.basename(file_path)}")
                print("Format: Not recognized as an Excel file")
    
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
        read_excel_header(file_path)
    else:
        print("Please provide the path to an Excel file")

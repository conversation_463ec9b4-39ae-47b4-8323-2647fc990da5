/**
 * @description 百日打卡-用户活动信息
 */

module.exports = (req, res) => {
	let json = {
		code: 0,
		data: {},
		msg: '',
	};
	
	const factoryid = parseInt(req.headers.factoryid);
	const jobnum = safeString(req.headers.login_jobnum);
	const period_id = parseInt(req.query.period_id);
	
	if (!factoryid || !jobnum || !period_id) {
		json.code = 201;
		json.msg = '缺少参数';
		res.json(json);
		return;
	}

	querySql(`select id, days, end_time from ${databasePre}checkin_period_conf where factory_id=${factoryid} and state=1 and id=${period_id} and start_time<=now();

	select l.days_sum, l.chapter_id, c.days_sum as chapter_days_sum, l.create_time from ${databasePre}checkin_log l, ${databasePre}checkin_chapter c where l.factory_id=${factoryid} and l.period_id=${period_id} and l.jobnum='${jobnum}' and l.chapter_id=c.id order by l.id asc;
	
	select checkin_days_sum from ${databasePre}checkin_task_favorite where factory_id=${factoryid} and jobnum='${jobnum}' and period_id=${period_id} order by id desc limit 0,1;
	
	select c.id, c.day_num, c.type, c.title, c.tooltips, (select count(1) from ${databasePre}checkin_test_log where test_id=c.id and jobnum='${jobnum}' and period_id=${period_id}) as complete, c.cert_src from ${databasePre}checkin_test_conf c where c.factory_id=${factoryid} and c.state=1 and c.period_id=${period_id} order by c.day_num asc;
	
	select task_list, days_sum, checkin_type, days_type, id from ${databasePre}checkin_chapter where factory_id=${factoryid} and period_id=${period_id} and state=1 order by sort_num asc;
	
	select * from ${databasePre}checkin_days_conf where factory_id=${factoryid} and period_id=${period_id} order by chapter_id asc, sort_num asc;
	
	select count(1) as count from ${databasePre}cert where jobnum='${jobnum}' and factory_id='${factoryid}'	and type=1 and checkin_period_id=${period_id};`, (err, result) => {
		if (err) {
			res.json(err);
			return;
		}

		const row = result[0][0];
		if (!row?.id) {
			json.code = 101;
			// json.msg = '活动未开放';
			json.msg = '活动未开始';
			res.json(json);
			return;
		}
		
		const days_total = row.days;
		const logInfo = result[1][result[1].length - 1] || {};
		// const favorite = result[2][0] || { checkin_days_sum: 0 };
		const testList = result[3];
		const chapterList = result[4];
		const daysList = result[5];
		const gotCert = result[6][0].count > 0;

		if (logInfo.days_sum === undefined) {
			logInfo.days_sum = 0;
		}

		let task_id = null; // 任务ID，首页点击后直接跳去打卡
		let days_conf_id = null; // 天数配置ID，首页点击后直接跳去打卡
		let daysSum = 0;
		let days_type = 0;
		for (let index = 0; index < chapterList.length; index++) {
			const chapter = chapterList[index];
			// console.log('daysList', daysList);
			// console.log('chapter', chapter);
			
			daysSum += chapter.days_sum;
			if (logInfo.days_sum < daysSum) {
				const idx = logInfo.days_sum - daysSum + chapter.days_sum;
				// console.log(logInfo.days_sum, daysSum, chapter.days_sum);
				// console.log('idx', idx);
				if (chapter.days_type === 1) {
					// 分别设置
					days_conf_id = daysList.filter(o => o.chapter_id === chapter.id)[idx].id;
				} else {
					// 统一设置
					task_id = chapter.task_list.split(',')[idx] || null;
				}
				days_type = chapter.days_type;
				break;
			}
		}
		
		json.data = {
			days_sum: logInfo.days_sum, // 累计打卡天数
			days_total,
			task_id,
			days_conf_id,
			days_type,
			task_evaluate: null, // 是否要作出最喜欢的任务评价
			// 是否需要先进行测试
			test_info: null,
		}

		// 是否有未完成的评价
		// for (let index = 0; index < result[1].length; index++) {
		// 	const obj = result[1][index];
		// 	if (index + 1 >= favorite.checkin_days_sum + obj.chapter_days_sum) {
		// 		// 如果未做过这章的评价
		// 		json.data.task_evaluate = {
		// 			chapter_id: obj.chapter_id,
		// 		}
		// 		break;
		// 	}
		// }

		// 是否有需要完成的测试
		const testInfo = testList.find(o => {
			let num = 0;
			if (o.type === 0) {
				// 前测
				num = 1;
			}
			return logInfo.days_sum + num >= o.day_num && !o.complete;
		})

		// 活动已结束，只返回后测题
		const endTime = result[1].length ? new Date(timeFormatter(result[1][0]?.create_time.getTime(), 'Y-m-d 00:00:00')).getTime() + row.days * 24 * 3600 * 1000 : row.end_time;
		if (testInfo && (Date.now() < endTime || testInfo.type === 1)) {
			json.data.test_info = {
				test_id: testInfo.id,
				title: testInfo.title,
				tooltips: testInfo.tooltips,
			};
		}

		// 是否有需要发证书未发的情况
		const getcert = testList.find(o => o.complete && o.cert_src);
		if (!gotCert && getcert) {
			json.data.getcert = {
				cert_src: getcert.cert_src,
				test_id: getcert.id,
			}
		}

		res.json(json);
	})
}
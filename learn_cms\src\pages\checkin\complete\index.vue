<!--
 * <AUTHOR>
 * @LastEditTime: 2023-12-08 21:08:43
 * @description 打卡活动 - 员工完成情况
-->
<template>
  <div class="checkin-data-complete ui-layout-col">
    <afc-filter-bar
      class="mb10"
      size="small"
      :field="searchField"
      :init-filter="searchForm"
      @valueChange="searchForm = $event"
      @onFilter="handelFilter" />

    <div class="flex1">
      <x-table
        :data-set="dataSet"
        :load-list-failed="loadFailed"
        @reload="init"
        @size-change="sizeChange"
        @current-change="currentChange"
        @sort-change="sortChange">
        <template v-for="obj in tableCols">
          <el-table-column
            v-if="obj.prop === 'jobnum'"
            v-bind="obj"
            :key="obj.prop">
            <template slot-scope="scope">
              <a @click="popupLogs(scope.row)">{{ scope.row.jobnum }}</a>
            </template>
          </el-table-column>
          <el-table-column
            v-else-if="obj.prop === 'test'"
            v-bind="obj"
            :key="obj.prop">
            <template slot-scope="scope">
              <a @click="lookTest(scope.row)">查看</a>
            </template>
          </el-table-column>
          <el-table-column
            v-else
            v-bind="obj"
            :key="obj.prop" />
        </template>
      </x-table>
    </div>

    <el-dialog
      :title="'打卡记录 - ' + popLogsJobnum"
      :visible.sync="showPopLogs"
      width="700px"
      :close-on-click-modal="false"
      @closed="logList = []">
      <div
        v-loading="showPopLogs && !logList.length"
        class="popup-logs">
        <div
          v-for="(o, i) in logList"
          :key="i"
          class="popup-logs__item"
          :class="{
            checked: o.checked,
            supplement: o.supplement,
            notcheck: o.notcheck,
            locked: o.locked,
          }">
          {{ i + 1 }}
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import afcFilterBar from 'afc-filter-bar-vue2';
import { cloneDeep } from 'lodash';

import {
  searchField,
  tableCols,
} from './table-cols';

import xTable from '@/components/x-table';
import mixinFactoryList from '@/function/mixin-factory-list';

export default {
  components: {
    afcFilterBar,
    xTable,
  },
  mixins: [mixinFactoryList],
  data() {
    return {
      dataSet: {},
      searchForm: {},
      periodList: [], // 活动期数列表
      sortObj: {},

      logList: [], // 打卡记录
      showPopLogs: false,
      popLogsJobnum: '',
    };
  },
  computed: {
    tableCols() {
      const arr = cloneDeep(tableCols);
      // if (this.user.user_group) {
      //   // 工厂管理员，不能筛选工厂
      //   arr.splice(1, 1);
      // }
      return arr;
    },
    searchField() {
      const arr = cloneDeep(searchField);

      arr[1].formOpts.optsList = this.periodList.map(o => {
        return {
          ...o,
          label: `${o.label}[${o.value}]`,
        };
      });

      if (this.user.user_group) {
        // 工厂管理员，不能筛选工厂
        arr.splice(0, 1);
      } else {
        arr[0].formOpts.optsList = this.factoryList;
        arr[0].formOpts.events = {
          change: e => {
            this.onFactoryChange(e);
          },
        };
      }

      arr[arr.length - 2].formOpts.events.click = () => {
        this.exportTest();
      };
      arr[arr.length - 1].formOpts.events.click = () => {
        this.exportExcel();
      };
      return arr;
    },
  },
  created() {
    // 工厂管理员，手动初始化
    if (this.user.user_group) {
      this.init();
    }
  },
  methods: {
    async init() {
      await this.getPeriodList();
      this.getList();
    },
    handelFilter() {
      this.dataSet.page_num = 1;
      this.getList();
    },
    onFactoryChange(factory_id) {
      this.periodList = [];
      this.getPeriodList(factory_id);
    },

    // 获取期数列表
    async getPeriodList(factory_id) {
      const { code, data } = await ajax({
        url: 'checkin/period',
        data: {
          state: 2,
          factory_id: factory_id || this.searchForm.factory_id,
        },
      });
      if (code) {
        this.loadFailed = true;
        return;
      }
      this.loadFailed = false;

      this.searchForm.period_id = data.list[0]?.id;
      this.periodList = data.list.map(o => {
        return {
          label: o.code_name,
          value: o.id,
        };
      });
    },

    // 获取列表数据
    async getList() {
      if (!this.periodList.length) return;

      const pager = cloneDeep(this.dataSet);
      delete pager.list;
      delete pager.total;

      const search = cloneDeep(this.searchForm);
      delete search.export;
      delete search['export-test'];

      const { code, data } = await ajax({
        url: 'checkin-data/complete',
        data: {
          ...pager,
          ...search,
          ...this.sortObj,
        },
      });
      if (code) {
        this.loadFailed = true;
        return;
      }
      this.loadFailed = false;

      this.dataSet = data;
    },
    sizeChange(num) {
      this.$set(this.dataSet, 'page_size', num);
      this.dataSet.page_num = 1;
      this.getList();
    },
    currentChange(num) {
      this.$set(this.dataSet, 'page_num', num);
      this.getList();
    },
    sortChange({ prop, order }) {
      if (order) {
        this.sortObj = {
          field: prop,
          direction: order,
        };
      } else {
        this.sortObj = {};
      }
      this.getList();
    },

    lookTest(o) {
      this.$router.push(`test?period_id=${this.searchForm.period_id}&jobnum=${o.jobnum}&factory_id=${o.factory_id}`);
    },

    async popupLogs(row) {
      this.showPopLogs = true;
      this.popLogsJobnum = row.jobnum;
      
      const { code, data } = await ajax({
        url: 'checkin-data/log-info',
        data: {
          jobnum: row.jobnum,
          factory_id: row.factory_id,
          period_id: this.searchForm.period_id,
        },
      });
      if (code) return;
      this.logList = data;
    },

    // 导出 - 测试结果
    exportTest() {
      const search = cloneDeep(this.searchForm);
      delete search.export;
      delete search['export-test'];
      
      this.download({
        url: '/export/checkin/test',
        data: {
          ...search,
          period_name: this.periodList.find(o => o.value === this.searchForm.period_id).label,
          factory_name: this.user.user_group ? this.user.factory_name : this.factoryList.find(o => o.value === this.searchForm.factory_id).label,
        },
      });
    },
    // 导出
    exportExcel() {
      const search = cloneDeep(this.searchForm);
      delete search.export;
      delete search['export-test'];
      
      this.download({
        url: '/export/checkin/complete',
        data: {
          ...search,
          ...this.sortObj,
          period_name: this.periodList.find(o => o.value === this.searchForm.period_id).label,
          factory_name: this.user.user_group ? this.user.factory_name : this.factoryList.find(o => o.value === this.searchForm.factory_id).label,
        },
      });
    },
  },
};
</script>

<style lang="scss">
  @import './index';
</style>

/**
 * @description 打卡信息对象
 */

import {
	ref,
} from 'vue';
import {
	defineStore
} from 'pinia';

export const useCheckinInfoStore = defineStore('checkinInfo', () => {
	const checkinInfo = ref({});

	// 从接口获取
	async function getCheckinInfo(periodId) {
		if (!periodId) return;

		const {
			code,
			data
		} = await ajax({
			url: 'api/checkin/userinfo',
			data: {
				period_id: periodId,
			}
		});
		if (code) return;

		resetCheckinInfo(data);
	}

	// 重置信息，全量替换
	function resetCheckinInfo(obj) {
		checkinInfo.value = obj;
	}
	// 更新单个信息
	function updateCheckinInfo(key, value) {
		checkinInfo.value[key] = value;
	}

	return {
		checkinInfo,
		resetCheckinInfo,
		updateCheckinInfo,
		getCheckinInfo,
	};
});
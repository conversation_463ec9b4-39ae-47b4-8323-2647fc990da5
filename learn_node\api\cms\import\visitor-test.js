/**
 * @description 导入 - 小课堂测试题
 */

module.exports = (req, res) => {
	let json = {
		code: 0,
		data: {},
		msg: '',
	};

	const file = req.file;

	if (!file) {
		json.code = 201;
		json.msg = '缺少参数';
		res.json(json);
		return;
	}

	const xlsx = require('node-xlsx').default;
	const excel = xlsx.parse(file.buffer);

	// 先删掉之前的题目
	let sqlArr = [
		`delete from ${databasePre}visitor_test_topic`,
	];
	
	excel[0].data.forEach((value, index) => {
		// 表头直接跳过，标题数据为空时也跳过
		if (!index || !value[0]) return; // 表头直接跳过
8 
		const [
			title,
			score,
			answer,
			analysis,
			A,
			B,
			C,
			D,
		] = value;

		// 插入题目
		sqlArr.push(`insert into ${databasePre}visitor_test_topic(title, score, answer, analysis, A, B, C, D, sort_num) values('${safeString(title).trim()}', ${score}, '${answer}', '${analysis}', '${A}', '${B}', '${C}', '${D}', ${index})`);
	});
	
	execTrans(sqlArr, (err, re) => {
		if (err) {
			res.json(err);
			return;
		}

		res.json(json);
	});
}
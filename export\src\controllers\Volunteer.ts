import { Controller, Get, Query } from '@nestjs/common';

import { ApplyQueryDto, DataQueryDto } from '../dto/Volunteer';
import { VolunteerService } from '../services/Volunteer';

@Controller('volunteer')
export class VolunteerController {
  constructor(private readonly service: VolunteerService) {}

  /** 志愿者申请记录 */
  @Get('apply')
  async getApplyList(@Query() query: ApplyQueryDto): Promise<ResObj> {
    return this.service.getApplyList(query);
  }

  /** 志愿者申请-数据统计 */
  @Get('data')
  async getData(@Query() query: DataQueryDto): Promise<ResObj> {
    return this.service.getData(query);
  }

  /** 志愿者申请-数据统计-超管 */
  @Get('data-admin')
  async getDataAdmin(): Promise<ResObj> {
    return this.service.getDataAdmin();
  }
}

/**
 * @description 积分与成就 - 积分明细
 */

module.exports = (req, res) => {
	let json = {
		code: 0,
		data: {},
		msg: '',
	};

	const user_group = req.headers.user_group === undefined ? 1 : parseInt(req.headers.user_group); // 用户组，0超级管理员，1工厂管理员

	/* 
		TODO:
		根据用户关联到的user_group和factory_id来鉴权
	 */
	let factory_id = '';
	if (user_group === 1) {
		// 工厂管理员、筛选当前工厂的
		factory_id = parseInt(req.headers.factoryid);
	} else {
		factory_id = parseInt(req.query.factory_id) || parseInt(req.body.factory_id) || 0;
	}
	if (!factory_id) {
		json.code = 210;
		json.msg = '缺少参数';
		res.json(json);
		return;
	}

	switch (req.method.toLowerCase()) {
		// 新增
		case 'post':
			var jobnum = safeString(req.body.jobnum);
			var score = parseInt(req.body.score) || 0;
			var remark = safeString(req.body.remark);

			if (!jobnum || !score || !factory_id) {
				json.code = 201;
				json.msg = '缺少参数';
				res.json(json);
				return;
			}

			querySql(`select count(1) as count from ${databasePre}user where factory_id=${factory_id} and jobnum='${jobnum}' and state=1`, (err, result, conn) => {
				if (err) {
					res.json(err);
					return;
				}

				if (!result[0].count) {
					json.code = 202;
					json.msg = '工号不存在或已注销';
					res.json(json);
					return;
				}

				conn.query(`insert into ${databasePre}user_points(factory_id, jobnum, type, score, remark, create_time) values(${factory_id}, '${jobnum}', 0, ${score}, '${remark}', now())`, (err, result) => {
					if (err) {
						res.json(err);
						return;
					}
					res.json(json);
				});
			});
		break;

		// 列表
		default:
		case 'get':
			var page_num = parseInt(req.query.page_num) || 1;
			var page_size = parseInt(req.query.page_size) || 15;
			var jobnum = safeString(req.query.jobnum);
			var user_type = safeString(req.query.user_type);
			var start_time = safeString(req.query.start_time);
			var end_time = safeString(req.query.end_time);
			
			var where = '';
			var arr = [
				`factory_id='${factory_id}'`,
			];

			if (start_time) {
				arr.push(`create_time>=STR_TO_DATE('${start_time}', '%Y-%m-%d')`);
			}
			if (end_time) {
				arr.push(`create_time<=STR_TO_DATE('${end_time} 23:59:59', '%Y-%m-%d %H:%i:%S')`);
			}

			if (arr.length) {
				where = `where ${arr.join(' and ')}`;
			}

			// 用户表 + 志愿者培训 + 小课堂 + 打卡活动 + 志愿者服务 + 其它积分
			const userWhere = `where factory_id=${factory_id} and ${jobnum ? `jobnum='${jobnum}'` : `jobnum != ''`}${user_type ? ` and user_type='${user_type}'` : ''}`;
			querySql(`select count(1) as total from ${databasePre}user ${userWhere};
			select jobnum, user_type from ${databasePre}user ${userWhere} order by id limit ${(page_num - 1) * page_size}, ${page_size};`, (err, result, conn) => {
				if (err) {
					res.json(err);
					return;
				}

				const total = result[0][0]?.total || 0;
				const userList = result[1];
				if (!userList.length) {
					res.json(json);
					return;
				}

				const jobnumStr = userList.map(o => `'${o.jobnum}'`).join();
				conn.query(`select sum(ifnull(module1, 0) + ifnull(module2, 0) + ifnull(module3, 0) + ifnull(module4, 0) + ifnull(module5, 0) + ifnull(module6, 0) + ifnull(module7, 0)) as score, jobnum from ${databasePre}allpoint_data where factory_id=${factory_id} and jobnum in(${jobnumStr}) group by jobnum;

				SELECT r.jobnum, r.sum2 as score FROM ${databasePre}visitor_data r
				JOIN (
					SELECT jobnum, MAX(id) AS max_id
					FROM ${databasePre}visitor_data
					where factory_id=${factory_id} and sum2 != 0 and jobnum in(${jobnumStr})
					GROUP BY jobnum
				) latest
				ON r.jobnum = latest.jobnum AND r.id = latest.max_id
				where factory_id=${factory_id};

				select sum(points) as score, jobnum from ${databasePre}checkin_log ${where} and jobnum in(${jobnumStr}) group by jobnum;

				select ifnull(sum(points), 0) as score, jobnum from ${databasePre}service_log ${where} and jobnum in(${jobnumStr}) and is_del=0 and check_status<2 group by jobnum;

				select ifnull(sum(score), 0) as score, jobnum from ${databasePre}user_points ${where} and jobnum in(${jobnumStr}) and type=0 group by jobnum;`, (err, result) => {
					if (err) {
						res.json(err);
						return;
					}

					var jobnumObj = {};
					userList.forEach(({ jobnum, user_type }) => {
						jobnumObj[jobnum.toUpperCase()] = { user_type };
					})

					var keyArr = ['score_training', 'score_visitor', 'score_checkin', 'score_service', 'score_others'];
					result.forEach((list, idx) => {
						list.forEach(({ jobnum, score }) => {
							jobnum = jobnum.toUpperCase();
							if (jobnumObj[jobnum] === undefined) return; // 过滤脏数据
							jobnumObj[jobnum][keyArr[idx - 1]] = score;
							jobnumObj[jobnum].score_total = (jobnumObj[jobnum].score_total || 0) + score;
						})
					})

					// 按总积分倒序					
					var list = Object.entries(jobnumObj).sort((a, b) => {
						return (b[1].score_total || 0) - (a[1].score_total || 0);
					}).map(([jobnum, scoreObj], idx) => {
						jobnum = jobnum.toUpperCase();
						return {
							...scoreObj,
							jobnum,
							factory_id,
							rank: idx + 1,
						}
					})
					json.data.total = total;
					json.data.page_num = page_num;
					json.data.page_size = page_size;
					json.data.list = list;
					res.json(json);
				});
			});
		break;
	}
}
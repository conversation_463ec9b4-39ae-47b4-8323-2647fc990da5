import { Controller, Get, Post, Body, Headers } from '@nestjs/common';

import { CustomHeadersDto } from '../dto';
import { VisitorApplySubmitDto } from '../dto/Visitor';
import { VisitorService } from '../services/Visitor';
import { validateDto } from '../filters/validate-dto';

@Controller('visitor')
export class VisitorController {
  constructor(private readonly service: VisitorService) {}

  /** 小课堂-获取题目 */
  @Get('test-info')
  async testInfo(
    @Headers() headers: CustomHeadersDto,
  ): Promise<ResObj> {
    const headerDto = await validateDto(CustomHeadersDto, headers);
    return this.service.testInfo(headerDto);
  }

  /** 小课堂-提交 */
  @Post('test-submit')
  async testSubmit(
    @Body() body: VisitorApplySubmitDto,
    @Headers() headers: CustomHeadersDto,
  ): Promise<ResObj> {
    const headerDto = await validateDto(CustomHeadersDto, headers);
    return this.service.testSubmit(body, headerDto);
  }
}

/**
 * <AUTHOR>
 * @description 定时任务
	*    *    *    *    *    *
	┬    ┬    ┬    ┬    ┬    ┬
	│    │    │    │    │    │
	│    │    │    │    │    └ day of week (0 - 7) (0 or 7 is Sun)
	│    │    │    │    └───── month (1 - 12)
	│    │    │    └────────── day of month (1 - 31)
	│    │    └─────────────── hour (0 - 23)
	│    └──────────────────── minute (0 - 59)
	└───────────────────────── second (0 - 59, OPTIONAL)
 */

const { scheduleJob } = require('node-schedule');

module.exports = () => {
	// 每天 03:00，删掉预览工号数据
	scheduleJob('00 00 03 * * *', require('./do/delete-preview'));
	// 每天 03:05，更新总积分排名数据
	scheduleJob('00 05 03 * * *', require('./do/score-rank'));
}

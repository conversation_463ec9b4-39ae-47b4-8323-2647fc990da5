<!--
 * <AUTHOR>
 * @Date: 2023-01-13 22:08:23
 * @LastEditTime: 2023-07-23 01:20:24
 * @description 志愿者答题首页
-->
<template>
	<view
		class="formal-index"
		:style="{ paddingTop: user.safePadHeight }">
		<uni-icons
			type="back"
			class="nav-back"
			:style="{ marginTop: user.statusBarHeight }"
			@click="goBack"></uni-icons>

		<!-- 右上角图标 -->
		<view
			class="tools-icons"
			:style="{ marginTop: user.safePadHeight }">
			<text
				class="iconfont i-info"
				@click="navigate('/learn/intro')"></text>
			<text
				class="iconfont i-medal mt40"
				@click="navigate('/learn/medal', { jobnum: user.loginJobnum })"></text>
		</view>

		<view class="menus">
			<template
				v-for="(o, i) in menus"
				:key="i">
				<view
					class="button block border menu"
					:class="{ disabled: i > 0 && !o.status }"
					@click="goModel(o, i)">
					{{ i + 1 }}.{{ o.module_name }}
				</view>
			</template>
		</view>
	</view>

	<PreloadImage :list="preloadImage" />
</template>

<script setup>
import { onShow } from '@dcloudio/uni-app';
import { ref } from 'vue';
import { storeToRefs } from 'pinia';
import { useUserStore } from '@/stores/user';
import { goBack, navigate } from '@/utils/tools.js';
import preloadMap from './preload-map.js';

import PreloadImage from '@/components/PreloadImage/index.vue';
const preloadImage = ref();

const { user } = storeToRefs(useUserStore());

const menus = ref([]);

async function getMenus() {
	const { code, data } = await ajax({
		url: 'learn/menu',
		data: {
			jobnum: user.value.loginJobnum
		}
	});
	if (code) return;

	if (data.length > 7) {
		data.length = 7;
	}

	menus.value = data.filter((o) => o.switch);

	// 接口回来后再预请求
	setTimeout(() => {
		preloadImage.value = preloadMap.map((v) => 'learn/' + v);
	}, 300);
}

function goModel(o, i) {
	if (i > 0 && (!o.switch || !o.status)) return;

	if (i === menus.value.length - 1) {
		navigate('/learn/model1', { type: 1, jobnum: user.value.loginJobnum });
	} else {
		navigate('/learn/model' + o.id, { jobnum: user.value.loginJobnum });
	}
}

onShow(() => {
	getMenus();
});
</script>

<style lang="scss">
@import 'index.scss';
</style>

/** 增加缓存数据处理方法
 * @param params 详细的参数对象
 */

import {
	storeToRefs
} from 'pinia';
import {
	useUserStore
} from '@/stores/user';
import {
	Toast,
} from '@/utils/tools.js';
import { useIsCheckinPreview } from '@/hooks/is-checkin-preview.js';

let loadingCount = 0;
let show_loading_cd = 0;
let posting = {}; // 是否正在请求中（用于防止多次点击）

const ajax = (params) => {
	// 初始化参数，只有一个字符串参数的情况下、设置为url
	let url = params;
	let data = {};
	let method = 'get';
	let parse = 'json';
	let noLoad = true; // 默认不显示全局loading
	let contentType = 'application/x-www-form-urlencoded';
	let specialCode = false;
	let timeout = 10000;
	let repeat = true; // 接口是否可以重复提交（以url做唯一key）

	if (typeof params == 'object') {
		({
			url = url,
			data = data,
			method = method,
			parse = parse,
			noLoad = noLoad,
			contentType = contentType,
			specialCode = specialCode,
			timeout = timeout,
			repeat = repeat,
		} = params);
	}

	const { user } = storeToRefs(useUserStore());
	const { isCheckinPreview } = storeToRefs(useIsCheckinPreview());

	return new Promise((resolve) => {
		const header = {
			'content-type': contentType
		};
		if (user.value.wx_openid) {
			header.wxopenid = user.value.wx_openid;
		}
		if (user.value.factory_id) {
			header.factoryid = user.value.factory_id;
		}
		if (user.value.userid) {
			header.userid = user.value.userid;
		}
		if (user.value.loginJobnum) {
			header.login_jobnum = user.value.loginJobnum;
		}
		if (isCheckinPreview.value) {
			header.is_checkin_preview = 1;
		}

		if (!noLoad) {
			// 延迟显示加载中，避免一闪而过
			loadingCount++;
			clearTimeout(show_loading_cd);
			show_loading_cd = setTimeout(() => {
				if (loadingCount > 0) {
					uni.showLoading({
						title: '加载中...'
					});
				}
			}, 500);
		}

		// 加载完后执行
		const loaded = () => {
			delete posting[url];
			if (noLoad) return;

			if (loadingCount > 0) {
				loadingCount--;
			}
			if (loadingCount <= 0 && show_loading_cd) {
				uni.hideLoading();
				clearTimeout(show_loading_cd);
				show_loading_cd = undefined;
			}
		};

		// 如果接口不能重复调用，正在请求中，直接返回
		if (!repeat) {			
			if (posting[url]) {
				return;
			}
		}

		if (url.startsWith('api/')) {
			url = VITE_API_HOST + url.substring(4);
		} else {
			url = VITE_NODE_HOST + url;
		}

		posting[url] = true;
		uni.request({
			url,
			header,
			method,
			data,
			sslVerify: false,
			timeout,
			success({
				data,
				statusCode,
			}) {
				loaded();
				if (![200, 201, 304].includes(statusCode)) {
					data.code = 1;
					data.msg = data.message || data.msg;
				}

				if (!specialCode) {
					// 0为正确
					if (data.code) {
						setTimeout(() => {
							Toast(data.msg || '网络异常');
						}, 50)
					}
				}

				if (typeof data == 'object') {
					resolve(data)
				} else {
					// 兼容后台报错的情况
					resolve({
						code: 2,
						msg: data,
					})
				}				
			},
			fail(err) {
				loaded();
				if (!specialCode) {
					setTimeout(() => {
						Toast(err.msg || '网络异常');
					}, 50)
				}
				resolve({
					code: 3,
					msg: err.message,
				})
			}
		})
	})
};

export default ajax;
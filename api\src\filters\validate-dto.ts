import { plainToInstance } from 'class-transformer';
import { validate } from 'class-validator';
import { BadRequestException } from '@nestjs/common';

/**
 * 校验是否符合DTO定义
 * @param dto 定义的DTO
 * @param obj 需要校验的对象
 * @returns 根据DTO转换后的对象
 */
export async function validateDto<T>(dto: new () => T, obj: any): Promise<T> {
  const instance = plainToInstance(dto, obj);
  const errors = await validate(instance as object);
  if (errors.length > 0) {
    throw new BadRequestException(Object.values(errors[0].constraints).join());
  }
  return instance;
}

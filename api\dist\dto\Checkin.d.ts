export declare class CheckinTestAnswerDto {
    topic_id: number;
    score: number;
    value: string;
}
export declare class CheckinTestSubmitDto {
    period_id: number;
    test_id: number;
    answer_list: CheckinTestAnswerDto[];
}
export declare class ActivityDetailQueryDto {
    period_id: number;
}
export declare class UserInfoQueryDto {
    period_id: number;
}
export declare class CheckinSubmitDto {
    chapter_id: number;
    task_id?: number;
    days_conf_id?: number;
    experience?: string;
    img_keys?: string;
}
export declare class CheckinLogInfoDto {
    period_id: number;
}
export declare class CheckinDaysInfoDto {
    period_id: number;
    days_num?: number;
}

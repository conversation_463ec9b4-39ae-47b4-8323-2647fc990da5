import { ExceptionFilter, Catch, ArgumentsHost, HttpException } from '@nestjs/common';
import { Response } from 'express';

@Catch(HttpException)
export class CustomValidationFilter implements ExceptionFilter {
  catch(exception: HttpException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const status = exception.getStatus();
    const exceptionResponse = exception.getResponse() as any;
    let res = {} as ResObj;

    // 自定义错误响应格式
    if (status === 400) {
      let msg = exceptionResponse.message;
      if (Array.isArray(exceptionResponse.message)) {
        msg = exceptionResponse.message?.[0] || 'Validation failed';
      }
      res.code = status;
      res.msg = msg;
    } else {
      res = exceptionResponse;
    }

    response.status(status).json(res);
  }
}
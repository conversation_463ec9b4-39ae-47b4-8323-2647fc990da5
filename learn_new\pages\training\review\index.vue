<template>
	<view class="training-review">
		<view class="header df col">
			<view class="f72 fwb">
				<MultiLineText :text="info.title" />
			</view>
			<view class="f32 mt5 pt10">
				<MultiLineText :text="info.desc" />
			</view>
		</view>
		<view class="drops f32">
			<view
				class="item"
				:class="{ open: o.open }"
				:key="o.title"
				v-for="o in info.list">
				<view
					class="title fwb df s-c"
					@click="open(o)">
					{{ o.title }}
					<uni-icons
						class="arrow"
						type="forward"
						size="16"></uni-icons>
				</view>
				<Description :content="o.content" />
				<view
					class="download"
					@click="saveCard(o.cardSrc)">
					<uni-icons
						type="download"
						color="#fff"
						size="30"></uni-icons>
					<view class="c_b f28">下载知识卡片</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup name="TrainingReview">
/**
 * @description 志愿者培训-知识回顾
 */
import { ref } from 'vue';
import { storeToRefs } from 'pinia';
import { useUserStore } from '@/stores/user';
import { onLoad } from '@dcloudio/uni-app';
import { Toast, setLocal, getLocal } from '@/utils/tools.js';

import MultiLineText from '@/components/MultiLineText/index.vue';
import Description from '../components/widgets/Description/index.vue';

const { user } = storeToRefs(useUserStore());
const info = ref({});
let { curChapterId = 1, curCatalogId = 1 } = getLocal('trainingProcess') || {};

async function getDetailInfo() {
	const { code, data } = await ajax({
		url: 'training/review',
		data: {
			chapter_id: chapterId.value
		}
	});
	if (code) return;

	info.value = data;

	// 只更新进度到更多，避免进度更新回去
	if (curChapterId > chapterId.value) return;
	if (curChapterId == chapterId.value) {
		if (curCatalogId > catalogId.value) return;
	}
	setLocal(`trainingProcess`, {
		curChapterId: parseInt(chapterId.value),
		curCatalogId: parseInt(catalogId.value),
		complete: true
	});
}

function open(o) {
	o.open = !o.open;
}

// 下载知识卡片
function saveCard(arr) {
	uni.getSetting({
		complete(res) {
			const auth = res.authSetting['scope.writePhotosAlbum'];
			if (auth) {
				// 已授权
				saveImage(arr);
			} else if (auth === false) {
				// 已拒绝授权
				uni.showModal({
					title: '提示',
					content: '授权后才能保存，现在去授权？',
					success(res) {
						if (res.confirm) {
							uni.openSetting();
						}
					}
				});
			} else {
				// 未授权
				uni.authorize({
					scope: 'scope.writePhotosAlbum',
					success() {
						saveImage(arr);
					},
					fail() {
						// 拒绝了
						uni.showModal({
							title: '提示',
							content: '同意授权才能保存哦',
							showCancel: false,
							success(res) {
							}
						});
					}
				});
			}
		}
	});
}

// 保存多张海报
function saveImage(arr) {
	const failedObj = {}; // 失败对象
	const fun = function (idx) {
		const url = 'https://www.employeehealth.cn/images/' + arr[idx];
		const filePath = `${wx.env.USER_DATA_PATH}/${Date.now()}${idx}.jpg`;
		uni.downloadFile({
			url,
			filePath,
			complete(res) {
				if (res.statusCode !== 200) {
					failedObj[idx] = true;
					if (idx < arr.length - 1) {
						Toast(res.errMsg);
						fun(++idx);
					} else {
						uni.hideLoading();
						if (Object.keys(failedObj).length) {
							uni.showModal({
								title: '提示',
								content: '部分卡片保存失败',
								showCancel: false
							});
						} else {
							if (user.value.osName === 'ios') {
								Toast('保存成功');
							}
						}
					}
					return;
				}

				uni.saveImageToPhotosAlbum({
					filePath: res.filePath,
					success() {
						/* 删除缓存 */
						uni.getFileSystemManager().unlink({
							filePath,
							success(r) {}
						});
					},
					fail(err) {
						failedObj[idx] = true;
						Toast(err.errMsg);
					},
					complete() {
						if (idx < arr.length - 1) {
							fun(++idx);
						} else {
							uni.hideLoading();
							if (Object.keys(failedObj).length) {
								uni.showModal({
									title: '提示',
									content: '部分卡片保存失败',
									showCancel: false
								});
							} else {
								if (user.value.osName === 'ios') {
									Toast('保存成功');
								}
							}
						}
					}
				});
			}
		});
	};
	fun(0);
	uni.showLoading({
		title: '下载中...',
		mask: true
	});
}

const chapterId = ref();
const catalogId = ref();
onLoad((opts) => {
	chapterId.value = parseInt(opts.chapter_id);
	catalogId.value = parseInt(opts.catalog_id);
	getDetailInfo();
});
</script>

<style lang="scss">
@import 'index.scss';
</style>

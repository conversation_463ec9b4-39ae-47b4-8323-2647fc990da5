/**
 * <AUTHOR>
 * @description 路由map
 */

export default [
  {
    path: 'home',
    meta: {
      title: '数据概况',
      icon: 'i-data',
      needAuth: true,
    },
  },
  {
    path: 'user',
    meta: {
      title: '用户管理',
      icon: 'el-icon-user',
      needAuth: false,
    },
    children: [
      {
        path: 'index',
        meta: {
          title: '用户列表',
          needAuth: false,
        },
      }
    ],
  },
  {
    path: 'activity',
    meta: {
      title: '活动入口管理',
      icon: 'el-icon-menu',
      needAuth: false,
    },
    children: [
      {
        path: 'index',
        meta: {
          title: '活动入口管理',
          needAuth: false,
        },
      }
    ],
  },
  {
    path: 'visitor',
    meta: {
      title: '心理健康小课堂',
      icon: 'i-visitor-menu',
      needAuth: false,
    },
    children: [
      {
        path: 'test',
        meta: {
          title: '测评数据',
          needAuth: false,
        },
      },
      {
        path: 'data',
        meta: {
          title: '数据统计',
          needAuth: false,
        },
      }
    ],
  },
  {
    path: 'volunteer',
    meta: {
      title: '申请志愿者',
      icon: 'i-apply-menu',
      needAuth: false,
    },
    children: [
      {
        path: 'apply',
        meta: {
          title: '申请记录',
          needAuth: false,
        },
      },
      {
        path: 'data',
        meta: {
          title: '数据统计',
          needAuth: false,
        },
      }
    ],
  },
  {
    path: 'learn',
    meta: {
      title: '志愿者培训',
      icon: 'i-volunteer-menu',
      needAuth: false,
    },
    children: [
      {
        path: 'module',
        meta: {
          title: '模块学习情况',
          needAuth: false,
        },
      },
      {
        path: 'data',
        meta: {
          title: '数据统计',
          needAuth: false,
        },
      }
    ],
  },
  {
    path: 'checkin',
    meta: {
      title: '打卡活动',
      icon: 'el-icon-document-checked',
      needAuth: false,
    },
    children: [
      {
        path: 'period',
        meta: {
          title: '活动配置',
          needAuth: false,
        },
      },
      {
        path: 'complete',
        meta: {
          title: '员工完成情况',
          needAuth: false,
        },
      },
      {
        path: 'involvement',
        meta: {
          title: '内容参与情况',
          needAuth: false,
        },
      },
      {
        path: 'emotion',
        meta: {
          title: '情绪打卡',
          needAuth: false,
        },
      },
      {
        path: 'test',
        meta: {
          title: '心态评估',
          needAuth: false,
          hide: true,
        },
      }
    ],
  },
  {
    path: 'checkin-config',
    meta: {
      title: '打卡活动-配置',
      icon: 'el-icon-date',
      needAuth: true,
    },
    children: [
      {
        path: 'chapter',
        meta: {
          title: '章节列表',
          needAuth: true,
        },
      },
      {
        path: 'chapter-add',
        meta: {
          title: '章节详情',
          needAuth: true,
          hide: true,
        },
      },
      {
        path: 'task',
        meta: {
          title: '任务配置',
          needAuth: true,
        },
      },
      {
        path: 'medal',
        meta: {
          title: '勋章配置',
          needAuth: true,
        },
      },
      {
        path: 'test',
        meta: {
          title: '测试试卷配置',
          needAuth: true,
        },
      },
      {
        path: 'test-topic',
        meta: {
          title: '测试题目配置',
          needAuth: true,
          hide: true,
        },
        // }, {
        //   path: 'rank',
        //   meta: {
        //     title: '排名数据',
        //     needAuth: false,
        //   },
      }
    ],
  },
  {
    path: 'service',
    meta: {
      title: '服务记录',
      icon: 'el-icon-timer',
      needAuth: false,
    },
    children: [
      {
        path: 'statistics',
        meta: {
          title: '统计汇总',
          needAuth: false,
        },
      },
      {
        path: 'logs',
        meta: {
          title: '记录明细',
          needAuth: false,
        },
      }
    ],
  },
  {
    path: 'points',
    meta: {
      title: '积分与成就',
      icon: 'el-icon-coin',
      needAuth: false,
    },
    children: [
      {
        path: 'logs',
        meta: {
          title: '积分明细',
          needAuth: false,
        },
      },
      {
        path: 'medalcert',
        meta: {
          title: '勋章与证书',
          needAuth: false,
        },
      }
    ],
  },
  {
    path: 'system',
    meta: {
      title: '系统管理',
      icon: 'i-setting',
      needAuth: true,
    },
    children: [
      {
        path: 'factory',
        meta: {
          title: '工厂管理',
          needAuth: true,
        },
      },
      {
        path: 'user',
        meta: {
          title: '系统用户管理',
          needAuth: true,
        },
      }
    ],
  }
];

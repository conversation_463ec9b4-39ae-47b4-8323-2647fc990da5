import { IsString, <PERSON>N<PERSON>ber, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';

export class UserQueryDto {
  /** 工厂ID */
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
    factory_id: number;

  /** 工厂名称 */
  @IsString()
    factory_name: string;
  
  /** 创建时间-开始 */
  @IsOptional()
  @IsString()
    start_time: string;

  /** 创建时间-结束 */
  @IsOptional()
  @IsString()
    end_time: string;

  /** 上次登录时间-开始 */
  @IsOptional()
  @IsString()
    login_start_time: string;

  /** 上次登录时间-结束 */
  @IsOptional()
  @IsString()
    login_end_time: string;
    
  /** 状态 */
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
    state: number;

  /** 用户类型 */
  @IsOptional()
  @IsString()
    user_type: string;
    
  /** 工号 */
  @IsOptional()
  @IsString()
    jobnum: string;
}

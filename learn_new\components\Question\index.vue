<template>
<view class="com-question">
	<swiper :style="{ height: `calc(100vh - ${user.safePadHeight})` }" :current="swiperIdx" :duration="300" @change="swiperIdx = $event.target.current" disable-touch>
		<swiper-item v-for="(o, i) in list" :key="i" @touchmove.stop="">
			<!-- 注解 -->
			<view class="swiper-question__annotate" v-if="o.annotate">{{ o.annotate }}</view>
			
			<!-- 题目 -->
			<view class="swiper-question__title df c-c">
				<view>
					<text class="mr20">({{ i + 1 }}/{{ list.length }})</text>
					<text>{{ o.topic }}{{ o.answer.length > 1 ? '[多选]' : '' }}</text>
				</view>
			</view>
			
			<!-- 进度条 -->
			<view class="swiper-question__progress">
				<view class="swiper-question__progress-bar" :style="{ width: (100 * chooseArr.length / list.length).toFixed(2) + '%' }"></view>
			</view>
			
			<!-- 回答 -->
			<view class="swiper-question__answers" :class="{ short, 'short-min': short && o.chossNum > 5, disabled }">
				<view class="swiper-question__answer" @click="chooseItem(i, answerMapList[serial])" :class="{ active: chooseArr[i] && chooseArr[i].checked.includes(answerMapList[serial]) }" v-for="(val, serial) in o.chossNum" :key="serial">
					<view class="swiper-question__answer-text">
						<view class="swiper-question__answer-serial">{{ answerMapList[serial] }}:</view>
						<view>{{ o[answerMapList[serial]] }}</view>
					</view>
				</view>
			</view>
			
			<view class="swiper-question__btns">
				<view class="swiper-question__btn fl prev" @click="moveQuestion(-1)" v-show="swiperIdx > 0 && (list[swiperIdx] && !list[swiperIdx].hidePrev)">{{ prevText }}</view>
				<view class="swiper-question__btn fr next" @click="moveQuestion(1)">
					{{ swiperIdx === list.length - 1 && showSubmitText ? '提交' : nextText }}
				</view>
			</view>
		</swiper-item>
	</swiper>
	
	
	<!-- 解析 弹窗 -->
	<uni-popup ref="jiexiRef" type="center">
		<view class="popup-fullscreen jiexi">
			<view class="title">答案解析</view>
			<view class="intro-box">
				<view class="answer">
					<text v-if="jiexiObj.jiexi_tit">{{ jiexiObj.jiexi_tit }}</text>
					<text v-else>正确答案：{{ jiexiObj.answer }}</text>
				</view>
				<view class="subtit">解析</view>
				<view class="info">{{ jiexiObj.jiexi }}</view>
			</view>
			<view class="button small-submit" @click="continueAnswer">{{ swiperIdx === list.length - 1 && showSubmitText ? '确定' : nextText }}</view>
		</view>
	</uni-popup>
	
	
	<!-- category 弹窗 -->
	<uni-popup ref="categoryRef" type="center">
		<view class="popup-dialog">
			<text class="iconfont i-ring"></text>
			
			<view class="tal">{{ categoryTips }}</view>
			
			<button class="iknow mt40" @click="categoryRef.close()">开始选择</button>
		</view>
	</uni-popup>
</view>
</template>

<script setup>
import { ref, onMounted, watch, watchEffect, nextTick } from 'vue';
import { storeToRefs } from 'pinia';
import { useUserStore } from '@/stores/user';
import {
	Toast,
} from '@/utils/tools.js';

const props = defineProps({
	/* 题目列表
	{
		tihao, // 题号
		annotate, // 注解
		topic, // 标题
		chossNum, // 选项个数
		A,
		···
		E,
		answer, // 正确答案
		jiexi, // 解析内容
		jiexi_tit, // 解析的标题
	}
	*/
	list: {
		type: Array,
		default() {
			return [];
		}
	},
	
	// 是否是 矮选项 模式
	short: {
		type: Boolean,
		default: false,
	},
	
	prevText: {
		type: String,
		default: '上一题',
	},
	
	nextText: {
		type: String,
		default: '下一题',
	},
	
	// 最后一题的按钮显示成“提交”
	showSubmitText: {
		type: Boolean,
		default: true,
	},
	
	// 已做过的答案
	answer: Array,
	
	// 分组信息
	category: Object,
})
const emit = defineEmits([
	'midComplete',
	'submit',
])
defineExpose({
	addSwiperIdx: () => {
		swiperIdx.value++;
	},
})

const { user } = storeToRefs(useUserStore());

const answerMapList = answerMap;
const swiperIdx = ref(0);
const chooseArr = ref([]);
const jiexiObj = ref({});
const categoryTips = ref('');
const disabled = ref(false); // 是否不可修改

const categoryRef = ref();
const jiexiRef = ref();


// 上一题、下一题、提交
function moveQuestion(step, notSubmit) {
	if (step < 0) {
		if (swiperIdx.value > 0) {
			swiperIdx.value -= 1;
		}
	} else {
		// 下一题
		if (chooseArr.value[swiperIdx.value]?.checked.length) {
			if (props.list[swiperIdx.value].jiexi) {
				// 有答题解析、展示解析
				jiexiObj.value = props.list[swiperIdx.value];
				jiexiRef.value.open();
			} else {
				// 无答题解析、直接切换下一题
				if (swiperIdx.value < props.list.length - 1) {
					if (props.list[swiperIdx.value].midComplete) {
						emit('midComplete');
					} else {
						swiperIdx.value += 1;
					}
				} else {
					// 提交
					!notSubmit && emit('submit', chooseArr.value);
				}
			}
		} else {
			Toast('请先选择答案');
		}
	}
}
		
function chooseItem(idx, checked) {
	if (disabled.value) return;
	
	if (props.list[idx].answer.length > 1) {
		// 多选
		if (chooseArr.value[idx]) {
			let exist = chooseArr.value[idx].checked.indexOf(checked);
			if (exist > -1) {
				chooseArr.value[idx].checked.splice(exist, 1);
			} else {
				chooseArr.value[idx].checked.push(checked);
			}
			
			chooseArr.value[idx].correct = chooseArr.value[idx].checked.sort().join('') === props.list[idx].answer;
		} else {
			chooseArr.value[idx] = {
				checked: [checked],
				correct: false,
				id: props.list[idx]?.id,
			}
		}
	} else {
		// 单选
		chooseArr.value[idx] = {
			checked: [checked],
			correct: checked === props.list[idx].answer,
			id: props.list[idx]?.id,
		}
		nextTick(() => {
			moveQuestion(1, true);
		})
	}
}
		
function continueAnswer() {
	if (swiperIdx.value < props.list.length - 1) {
		if (props.list[swiperIdx.value].midComplete) {
			emit('midComplete');
		} else {
			swiperIdx.value += 1;
		}
	} else {
		emit('submit', chooseArr.value);
	}
	jiexiRef.value.close();
}


watchEffect(() => {
	if (props.answer?.length) {
		chooseArr.value = props.answer;
		disabled.value = true;
	} else {
		disabled.value = false;
	}
})

onMounted(() => {
	watch(swiperIdx, (idx, oldIdx) => {
		if (idx < oldIdx || props.list[idx]?.category === props.list[oldIdx]?.category) return;
		
		categoryTips.value = props.category[props.list[idx].category]
		categoryRef.value.open();
	})
	
	watch(() => props.category, (obj) => {
		if (swiperIdx.value !== 0) return;
		
		categoryTips.value = obj[props.list[0].category];
		setTimeout(() => {
			categoryRef.value.open();
		}, 150)
	})
})
</script>

<style lang="scss">
	@import 'index.scss';
</style>
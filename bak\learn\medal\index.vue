<!--
 * <AUTHOR>
 * @Date: 2023-03-31 22:59:15
 * @LastEditTime: 2023-08-12 15:23:39
 * @description 勋章页
-->
<template>
	<view
		class="formal-medal"
		:style="{ paddingTop: user.statusBarHeight }"
	>
		<uni-icons
			type="back"
			class="nav-back"
			@click="goBack()"
		></uni-icons>

		<view class="title">我的成就</view>
		<view class="subtitle">Achievement</view>

		<view class="medals">
			<text
				class="medal"
				:class="['medal' + v, { got: completeModelLen >= v }]"
				v-for="v in medalArr"
				:key="v"
			></text>
		</view>

		<button
			class="button block round light-reverse"
			:class="{ disabled: !gotCert }"
			@click="popupCertVisible = true"
		>
			心理关爱志愿者证书
		</button>

		<PopupCert
			img-src="learn/medal-cert.png"
			v-model="popupCertVisible"
		/>
	</view>
</template>

<script setup>
import { onLoad, onReady } from '@dcloudio/uni-app';
import { ref } from 'vue';
import { storeToRefs } from 'pinia';
import { useUserStore } from '@/stores/user';

import PopupCert from '@/components/PopupCert/index.vue';

import { goBack, realImgSrc } from '@/utils/tools.js';

const { user } = storeToRefs(useUserStore());

const completeModelLen = ref(0);
const gotCert = ref(false); // 是否获得证书
const medalArr = ref([1, 2, 3, 4]);

const popupCertVisible = ref(false);

// 查询获得勋章总数
async function getTotalModal() {
	const { code, error, data } = await ajax({
		url: 'modal/total_num'
	});
	if (code || error) return;

	completeModelLen.value = data;
}
getTotalModal();

// 查询是否获得证书
async function getGotCert() {
	const { code, error, data } = await ajax({
		url: 'modal/cert',
		data: {
			jobnum: user.value.loginJobnum
		}
	});
	if (code || error) return;

	gotCert.value = data;
}

onReady(() => {
	getGotCert();
});
</script>

<style lang="scss">
@import 'index.scss';
</style>

module.exports = {
  parser: '@typescript-eslint/parser',
  parserOptions: {
    project: 'tsconfig.json',
    tsconfigRootDir: __dirname,
    ecmaVersion: 'latest',
    sourceType: 'module',
    parser: '@typescript-eslint/parser',
    ecmaFeatures: {
      modules: true,
      jsx: true
    }
  },
  plugins: ['@typescript-eslint'],
  extends: [
    'eslint:recommended',
    'plugin:@typescript-eslint/recommended',
    'plugin:import/recommended', //对import排序
    'plugin:import/typescript',
  ],
  root: true,
  env: {
    node: true,
    jest: true,
  },
  ignorePatterns: ['.eslintrc.js'],
  rules: {
    'prefer-const': ['warn', {"destructuring": "all"}],
    'no-console': [
      'warn',
      {
        allow: [
          'log',
          'error',
          'warn',
          'table'
        ]
      }
    ],
    'no-irregular-whitespace': 'off',
    indent: [
      'warn',
      2,
      {
        ignoredNodes: ['TemplateLiteral'],
        SwitchCase: 1
      },
    ],
    semi: ['warn', 'always'],
    quotes: [
      'error',
      'single',
      { avoidEscape: true }
    ],
    'no-empty': ['warn', { allowEmptyCatch: true }],
    'quote-props': ['warn', 'as-needed'],
    'no-tabs': ['error'],
    'comma-dangle': ['warn', 'only-multiline'],
    curly: ['error', 'multi-line'],
    'no-multiple-empty-lines': [
      'warn',
      {
        max: 1,
        maxEOF: 1
      }
    ],
    'no-fallthrough': ['error', { commentPattern: 'fallthrough' }],
    eqeqeq: ['error', 'smart'],
    'block-spacing': 'error',
    'arrow-spacing': 'error',
    'switch-colon-spacing': 'error',
    'space-unary-ops': 'error',
    'space-in-parens': ['error', 'never'],
    'space-before-function-paren': [
      'warn',
      {
        anonymous: 'always',
        named: 'never',
        asyncArrow: 'always'
      }
    ],
    'space-before-blocks': 'error',
    'object-curly-spacing': ['error', 'always'],
    'no-trailing-spaces': ['error', { skipBlankLines: true }],
    'keyword-spacing': [
      'error',
      {
        before: true,
        after: true
      }
    ],
    'comma-spacing': [
      'error',
      {
        before: false,
        after: true
      }
    ],
    'no-multi-spaces': 'warn',
    'array-bracket-spacing': ['warn', 'never'], // 是否允许非空数组里面有多余的空格
    'newline-per-chained-call': ['warn', { ignoreChainWithDepth: 2 }], // 链式调用长度超过2时，强制要求换行
    'function-paren-newline': 'warn', // 强制函数括号内的参数一致换行或一致不换行
    'padded-blocks': ['warn', { blocks: 'never' }], // 块内不允许有多余的空行（class内放宽）
    'array-element-newline': [
      'warn',
      {
        multiline: true,
        minItems: 3
      }
    ], // 数组内元素>=3个换行
    'array-bracket-newline': [
      'warn',
      {
        multiline: true,
        minItems: 3
      }
    ], // 数组内元素 首尾换行
    'object-curly-newline': [
      'warn',
      {
        ObjectPattern: {
          multiline: true,
          minProperties: 4,
        },
        ObjectExpression: {
          multiline: true,
          minProperties: 2,
        },
        ImportDeclaration: { consistent: true },
        ExportDeclaration: {
          multiline: true,
          minProperties: 3,
        },
      },
    ],
    // 对象内属性换行规则
    'object-property-newline': ['warn', { allowAllPropertiesOnSameLine: false }], // 对象属性每行一个（>=2个时）

    // typescript规范
    'no-unused-vars': 'off',
    'key-spacing': 'off',
    '@typescript-eslint/key-spacing': ['warn', { afterColon: true }],
    'space-infix-ops': 'off',
    '@typescript-eslint/space-infix-ops': 'warn',
    '@typescript-eslint/interface-name-prefix': 'off',
    '@typescript-eslint/explicit-function-return-type': 'off',
    '@typescript-eslint/explicit-module-boundary-types': 'off',
    '@typescript-eslint/no-explicit-any': 'off',
    '@typescript-eslint/no-var-requires': 'off',
    '@typescript-eslint/no-unused-vars': [
      'warn',
      {
        args: 'all',
        argsIgnorePattern: '^_',
        caughtErrors: 'all',
        caughtErrorsIgnorePattern: '^_',
        destructuredArrayIgnorePattern: '^_',
        varsIgnorePattern: '^_',
        ignoreRestSiblings: true
      },
    ],
    '@typescript-eslint/type-annotation-spacing': [
      'warn',
      {
        before: false,
        after: true,
        overrides: {
          arrow: {
            before: true,
            after: true
          }
        }
      }
    ],
    '@typescript-eslint/space-before-blocks': 'warn',

    // import排序
    'import/order': [
      'warn',
      {
        'newlines-between': 'always',
        groups: [
          'builtin',
          'external',
          'internal',
          'index',
          'sibling',
          'parent',
          'object',
          'type'
        ]
      },
    ],
  },
};

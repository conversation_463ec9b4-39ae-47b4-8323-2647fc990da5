<template>
  <div class="points-logs ui-layout-col">
    <afc-filter-bar
      class="mb10"
      size="small"
      :field="searchField"
      :init-filter="searchForm"
      :show-button="false"
      @valueChange="searchForm = $event; init()" />
    <!-- 导入志愿者 -->
    <el-upload
      ref="upload"
      class="poa import"
      :action="actionUrl"
      :headers="headers"
      :data="uploadData"
      :multiple="false"
      :show-file-list="false"
      :on-error="error"
      :on-success="success"
      :before-upload="beforeUpload"
      :limit="1"
      accept=".xls,.xlsx">
      <a class="i-import vam">导入</a>
    </el-upload>
    <!-- 导出明细 -->
    <el-dropdown
      class="export"
      @command="exportExcel">
      <a class="i-export vam">明细</a>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item command="default">默认值</el-dropdown-item>
        <el-dropdown-item command="filter">筛选条件</el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>

    <div class="flex1">
      <x-table
        :data-set="dataSet"
        :load-list-failed="loadFailed"
        @reload="init"
        @size-change="sizeChange"
        @current-change="currentChange">
        <template v-for="obj in tableCols">
          <el-table-column
            v-if="obj.prop === 'img_keys'"
            v-bind="obj"
            :key="obj.prop">
            <template slot-scope="scope">
              <div
                v-if="scope.row.img_keys"
                style="display: flex;justify-content: center;">
                <el-image
                  v-for="key in scope.row.img_keys.split(',')"
                  :key="key"
                  style="width: 48px; height: 48px; margin: 0 5px;"
                  :src="imgHost + key"
                  :preview-src-list="scope.row.img_keys.split(',').map(v => imgHost + v)" />
              </div>
            </template>
          </el-table-column>
          <el-table-column
            v-else-if="obj.prop === 'operation'"
            v-bind="obj"
            :key="obj.prop">
            <template slot-scope="scope">
              <a
                v-if="[0, 3].includes(scope.row.check_status)"
                @click="popupCheck(scope.row)">
                审核
              </a>
            </template>
          </el-table-column>
          <el-table-column
            v-else
            v-bind="obj"
            :key="obj.prop" />
        </template>
      </x-table>
    </div>

    <!-- 新增用户 -->
    <el-dialog
      title="新增其它积分"
      :visible.sync="showPopAdd"
      width="500px"
      class="add"
      :close-on-click-modal="false">
      <el-form
        ref="addForm"
        :rules="addRules"
        :model="addForm"
        size="medium"
        label-position="right"
        label-width="120px">
        <el-form-item
          v-if="user.user_group === 0"
          label="所属工厂："
          prop="factory_id">
          <el-select
            v-if="!user.user_group"
            v-model="addForm.factory_id"
            placeholder="请选择所属工厂">
            <el-option
              v-for="item in factoryList"
              :key="item.value"
              :label="item.label"
              :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item
          label="工号："
          prop="jobnum">
          <el-input
            v-model.trim="addForm.jobnum"
            placeholder="请输入工号"
            maxlength="30"
            show-word-limit />
        </el-form-item>
        <el-form-item
          label="积分数量："
          prop="score">
          <el-input-number
            v-model="addForm.score"
            :min="-9999"
            :step="1"
            :max="9999" />
        </el-form-item>
        <el-form-item
          label="备注信息："
          prop="remark">
          <el-input
            v-model.trim="addForm.remark"
            placeholder="请输入备注信息"
            maxlength="200"
            show-word-limit />
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button
          type="primary"
          size="small"
          @click="submitAddForm">
          确定
        </el-button>
        <el-button
          size="small"
          @click="showPopAdd = false">
          取消
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
/**
 * @description 积分与成就 - 积分明细
 */
import afcFilterBar from 'afc-filter-bar-vue2';
import { cloneDeep } from 'lodash';
import { mapMutations, mapState } from 'vuex';

import {
  searchField,
  tableCols,
} from './table-cols';

import xTable from '@/components/x-table';
import mixinFactoryList from '@/function/mixin-factory-list';

export default {
  components: {
    afcFilterBar,
    xTable,
  },
  mixins: [mixinFactoryList],
  data() {
    return {
      dataSet: {},
      actionUrl: process.env.VUE_APP_apiHost + 'cms/import/points',
      searchForm: {},
      // 添加用户的对话框的显示控制
      showPopAdd: false,
      // 添加表单的数据
      addForm: {},
      // 添加用户的验证规则
      addRules: {
        factory_id: [
          {
            required: true,
            message: '请选择工厂'
          }
        ],
        jobnum: [
          {
            required: true,
            message: '请输入工号'
          }
        ],
        score: [
          {
            required: true,
            message: '请输入积分'
          }
        ],
      },
    };
  },
  computed: {
    ...mapState(['uploadingFile']),
    headers() {
      if (this.user.token) {
        return {
          token: this.user.token,
          userid: this.user.userid,
          factoryid: this.user.factory_id,
          user_group: this.user.user_group,
        };
      } else {
        return {};
      }
    },
    tableCols() {
      const arr = cloneDeep(tableCols);
      // if (this.user.user_group) {
      //   // 工厂管理员，不能筛选工厂
      //   arr.splice(1, 1);
      // }
      return arr;
    },
    searchField() {
      const arr = cloneDeep(searchField);

      if (this.user.user_group) {
        // 工厂管理员，不能筛选工厂
        arr.splice(0, 1);
      } else {
        arr[0].formOpts.optsList = this.factoryList;
      }

      arr[arr.length - 1].formOpts.events.click = () => {
        this.popupAdd();
      };
      // arr[arr.length - 1].formOpts.events.click = () => {
      //   this.exportExcel();
      // };
      return arr;
    },
    uploadData() {
      if (!this.user.user_group) {
        // 超级管理员
        return { factory_id: this.searchForm.factory_id };
      } else {
        return {};
      }
    },
  },
  created() {
    // 工厂管理员，手动初始化
    if (this.user.user_group) {
      this.init();
    }
  },
  methods: {
    ...mapMutations(['setUploadingFile']),
    async init() {
      if (!this.searchForm.factory_id) return;
      this.getList();
    },
    handelFilter() {
      this.dataSet.page_num = 1;
      this.getList();
    },

    // 获取列表数据
    async getList() {
      const pager = cloneDeep(this.dataSet);
      delete pager.list;
      delete pager.total;

      const search = cloneDeep(this.searchForm);
      delete search.add;
      delete search.export;
      if (search.create_time?.length) {
        search.start_time = search.create_time[0];
        search.end_time = search.create_time[1];
      }
      delete search.create_time;

      const { code, data } = await ajax({
        url: 'points/logs',
        data: {
          ...pager,
          ...search,
        },
      });
      if (code) {
        this.loadFailed = true;
        return;
      }
      this.loadFailed = false;

      this.dataSet = data;
    },
    sizeChange(num) {
      this.$set(this.dataSet, 'page_size', num);
      this.dataSet.page_num = 1;
      this.getList();
    },
    currentChange(num) {
      this.$set(this.dataSet, 'page_num', num);
      this.getList();
    },

    // 添加/编辑用户
    popupAdd(o = {}) {
      let obj = cloneDeep(o);

      this.addForm = obj;
      this.showPopAdd = true;
      this.$nextTick(() => {
        this.$refs.addForm.clearValidate();
      });
    },
    submitAddForm() {
      this.$refs.addForm.validate().then(async validate => {
        if (!validate) return;

        const { code } = await ajax({
          url: 'points/logs',
          method: 'post',
          data: this.addForm,
        });
        if (code) return;

        this.showPopAdd = false;
        this.getList();
        this.$message.success('操作成功');
      });
    },

    // 上传文件的回调
    error(err) {
      this.$message.error(err);
      this.setUploadingFile(false);
      this.$refs.upload.clearFiles();
    },
    success(res) {
      this.setUploadingFile(false);
      this.$refs.upload.clearFiles();

      if (res.code) {
        this.$message.error(res.msg);
      } else {
        this.$message.success('导入成功');
        this.getList();
      }
    },
    beforeUpload() {
      if (this.uploadingFile > 0) return false;
      this.setUploadingFile(true);
    },

    // 导出
    exportExcel(type) {
      const search = cloneDeep(this.searchForm);
      delete search.export;
      if (search.create_time?.length) {
        search.start_time = search.create_time[0];
        search.end_time = search.create_time[1];
      }
      delete search.create_time;
      
      this.download({
        url: '/export/points/logs',
        data: {
          ...(type === 'filter' ? search : {}),
          factory_name: this.user.user_group ? this.user.factory_name : this.factoryList.find(o => o.value === this.searchForm.factory_id).label,
        },
      });
    },
  },
};
</script>

<style lang="scss">
  @import './index';
</style>

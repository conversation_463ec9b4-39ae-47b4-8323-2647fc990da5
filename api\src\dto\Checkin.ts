import {
  IsInt,
  IsString,
  IsOptional,
  IsArray,
  ArrayNotEmpty,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

import { IsArrayOfObjects } from '../validate';

export class CheckinTestAnswerDto {
  /** 题目ID */
  @IsInt()
    topic_id: number;

  /** 得分 */
  @IsOptional()
  @IsInt()
    score: number;

  /** 答案 */
  @IsOptional()
  @IsString()
    value: string;
}

/** 百日打卡-提交测试问卷 */
export class CheckinTestSubmitDto {
  /** 活动ID */
  @IsInt()
    period_id: number;

  /** 测试ID */
  @IsInt()
    test_id: number;

  /** 答案列表 */
  @IsArray()
  @ArrayNotEmpty()
  @IsArrayOfObjects()
  @ValidateNested({ each: true })
  @Type(() => CheckinTestAnswerDto)
    answer_list: CheckinTestAnswerDto[];
}

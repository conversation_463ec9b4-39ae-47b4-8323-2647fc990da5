import {
  IsInt,
  IsString,
  IsOptional,
  IsArray,
  ArrayNotEmpty,
  ValidateNested,
} from 'class-validator';
import { Type, Transform } from 'class-transformer';

import { IsArrayOfObjects } from '../validate';

export class CheckinTestAnswerDto {
  /** 题目ID */
  @IsInt()
    topic_id: number;

  /** 得分 */
  @IsOptional()
  @IsInt()
    score: number;

  /** 答案 */
  @IsOptional()
  @IsString()
    value: string;
}

/** 打卡活动-提交测试问卷 */
export class CheckinTestSubmitDto {
  /** 活动ID */
  @Transform(({ value }) => Number(value))
  @IsInt()
    period_id: number;

  /** 测试ID */
  @Transform(({ value }) => Number(value))
  @IsInt()
    test_id: number;

  /** 答案列表 */
  @IsArray()
  @ArrayNotEmpty()
  @IsArrayOfObjects()
  @ValidateNested({ each: true })
  @Type(() => CheckinTestAnswerDto)
    answer_list: CheckinTestAnswerDto[];
}

/** 打卡活动-详情查询 */
export class ActivityDetailQueryDto {
  /** 活动ID */
  @Transform(({ value }) => Number(value))
  @IsInt()
    period_id: number;
}

/** 打卡活动-用户信息查询 */
export class UserInfoQueryDto {
  /** 活动ID */
  @Transform(({ value }) => Number(value))
  @IsInt()
    period_id: number;
}

/** 打卡活动-提交打卡 */
export class CheckinSubmitDto {
  /** 章节ID */
  @IsInt()
    chapter_id: number;

  /** 任务ID */
  @IsOptional()
  @IsInt()
    task_id?: number;

  /** 天数配置ID */
  @IsOptional()
  @IsInt()
    days_conf_id?: number;

  /** 打卡体验 */
  @IsOptional()
  @IsString()
    experience?: string;

  /** 图片keys */
  @IsOptional()
  @IsString()
    img_keys?: string;
}

/** 打卡活动-打卡记录查询参数 */
export class CheckinLogInfoDto {
  /** 活动ID */
  @Transform(({ value }) => Number(value))
  @IsInt()
    period_id: number;
}

/** 打卡活动-每天任务情况查询参数 */
export class CheckinDaysInfoDto {
  /** 活动ID */
  @Transform(({ value }) => Number(value))
  @IsInt()
    period_id: number;

  /** 第几天 */
  @Transform(({ value }) => Number(value))
  @IsOptional()
  @IsInt()
    days_num?: number;
}
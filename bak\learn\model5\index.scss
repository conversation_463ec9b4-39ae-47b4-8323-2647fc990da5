@import '@/scss/mixins.scss';

.formal-model5{
	background-image: url('#{$imgHost}learn/model2_bg.jpg');
	.formal-menu{
		margin: 0 auto 40rpx;
		.title{
			height: 100rpx;
		}
		.border-box{
			padding: 50rpx 60rpx;
			.button{
				min-height: 90rpx;
				line-height: 1.2;
			}
		}
	}
	
	.c_yellow{
		color: #f6bc50;
		font-weight: bold;
	}
	
	.q4-2{
		.img1{
			width: 656rpx;
			height: 759rpx;
			background-image: url('#{$imgHost}learn/double3.png');
			margin-top: 40rpx;
		}
		.img2{
			width: 613rpx;
			height: 721rpx;
			background-image: url('#{$imgHost}learn/double4.png');
			margin-top: 70rpx;
		}
	}
	.q4-8{
		.tag{
			width: 625rpx;
			margin: 10rpx auto;
			text-align: center;
			min-height: 74rpx;
			height: auto;
			padding: 14rpx 30rpx;
			line-height: 1.2;
		}
		.main-btn{
			margin-top: 80rpx;
		}
	}
}
/**
 * @description 百日打卡活动信息
 */

module.exports = (req, res) => {
	let json = {
		code: 0,
		data: {},
		msg: '',
	};
	
	const factoryid = parseInt(req.headers.factoryid);
	const login_jobnum = safeString(req.headers.login_jobnum);
	
	if (!factoryid || !login_jobnum) {
		json.code = 201;
		json.msg = '缺少参数';
		res.json(json);
		return;
	}

	querySql(`select c.id as period_id, code_name, activity_desc, start_time, end_time from ${databasePre}checkin_period_conf c, ${databasePre}user u where c.factory_id=${factoryid} and u.factory_id=c.factory_id and u.jobnum='${login_jobnum}' and c.user_type like concat('%[', u.user_type ,']%') and c.state=1 and c.start_time<=now() and c.end_time>now() order by c.id desc limit 0,1;
	
	select count(1) as emotion_log from ${databasePre}checkin_emotion_log where factory_id=${factoryid} and jobnum='${login_jobnum}' and create_time>=STR_TO_DATE('${timeFormatter(undefined, 'Y-m-d 00:00:00')}', '%Y-%m-%d %H:%i:%S');
	
	select c.id as period_id, code_name, activity_desc, start_time, end_time from ${databasePre}checkin_period_conf c, ${databasePre}user u, ${databasePre}checkin_log l where l.jobnum=u.jobnum and l.period_id=c.id and c.factory_id=${factoryid} and u.factory_id=c.factory_id and u.jobnum='${login_jobnum}' and c.user_type like concat('%[', u.user_type ,']%') and c.state=1 and c.end_time<=now() order by c.end_time desc limit 0,1`, (err, result) => {
		if (err) {
			res.json(err);
			return;
		}

		if (result[0].length) {
			// 正在活动中
			json.data = result[0][0];
		} else {
			// 没有进行中的活动，返回最近结束的活动
			// 仅返回用户参与过的，只保留30天
			const info = result[2][0];			
			
			if (info?.end_time && new Date(info.end_time).getTime() > Date.now() - 30 * 24 * 3600 * 1000) {
				json.data = info;
			} else {
				json.data = {};
			}
		}

		json.data.start_time = new Date(json.data.start_time).getTime();
		json.data.end_time = new Date(json.data.end_time).getTime();
		json.data.emotion_log = result[1][0].emotion_log;
		res.json(json);
	})
}
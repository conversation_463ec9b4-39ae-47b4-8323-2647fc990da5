import {
  ValidatorConstraint,
  ValidatorConstraintInterface,
  registerDecorator,
  IsString,
  IsInt,
  IsNotEmpty
} from 'class-validator';
import { Transform } from 'class-transformer';

// 自定义验证逻辑
@ValidatorConstraint({
  name: 'isOptionalNumber',
  async: false
})
class IsOptionalNumberConstraint implements ValidatorConstraintInterface {
  validate(value: any) {
    if (!value) {
      return true;
    }
    // 如果传了值，则必须是 number 类型
    return typeof value == 'number';
  }

  defaultMessage() {
    return 'must be an integer number';
  }
}

// 自定义装饰器
export function IsOptionalNumber() {
  return function (object: any, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      constraints: [],
      validator: IsOptionalNumberConstraint,
    });
  };
}

/** 统一公共头部-登录后 */
export class CustomHeadersDto {
  /** 工号 */
  @IsNotEmpty()
  @IsString()
    login_jobnum: string;

  /** 工厂ID */
  @Transform(({ value }) => Number(value))
  @IsNotEmpty()
  @IsInt()
    factoryid: number;
    
  /** 微信openid */
  @IsNotEmpty()
  @IsString()
    wxopenid: string;

  /** 用户ID */
  @Transform(({ value }) => Number(value))
  @IsNotEmpty()
  @IsInt()
    userid: number;
}
import { IsString, IsInt, IsOptional, IsEnum } from 'class-validator';
import { Transform } from 'class-transformer';

import { IsOptionalNumber } from './index';

/** 员工完成情况-查询参数 */
export class CompleteQueryDto {
  /** 活动ID */
  @Transform(({ value }) => Number(value))
  @IsInt()
    period_id: number;
  
  /** 活动代号 */
  @IsString()
    period_name: string;

  /** 工号 */
  @IsOptional()
  @IsString()
    jobnum: string;

  /** 工厂ID */
  @IsInt()
    factory_id: number;

  /** 工厂名称 */
  @IsString()
    factory_name: string;

  /** 排序字段 */
  @IsString()
    field: string = 'id';
  
  /** 排序方式 */
  @IsString()
    direction: string = 'asc';
}

/** 内容参与情况-查询参数 */
export class InvolvementQueryDto {
  /** 活动ID */
  @Transform(({ value }) => Number(value))
  @IsInt()
    period_id: number;

  /** 任务ID */
  @IsOptionalNumber()
    task_id: number;

  /** 工厂ID */
  @IsInt()
    factory_id: number;

  /** 工厂名称 */
  @IsString()
    factory_name: string;
    
  /** 活动代号 */
  @IsString()
    period_name: string;
}

/** 情绪打卡类型 */
enum EmotionType {
  /** 压力 */
  Stress = 'stress',
  /** 心情 */
  Mood = 'mood',
}
/** 情绪打卡-查询参数 */
export class EmotionQueryDto {
  /** 工厂ID */
  @IsInt()
    factory_id: number;

  /** 工厂名称 */
  @IsString()
    factory_name: string;

  /** 英文逗号拼接的工号 */
  @IsOptional()
  @IsString()
    jobnums: string;

  /** 打卡类型 */
  @IsEnum(EmotionType)
    type: EmotionType;
}
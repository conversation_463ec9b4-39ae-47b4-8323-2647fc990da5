<template>
<view class="formal-model2" :class="{ 'swiper-question-wrap': questionStep }" :style="{ paddingTop: user.statusBarHeight }">
	<uni-icons type="back" class="nav-back" @click="goBack()"></uni-icons>
	
	<!-- 题目列表 -->
	<view class="nav-title" v-if="questionStep" :style="{ top: user.statusBarHeight }">案例题</view>
	<Question v-if="questionStep" v-show="showQuestion" ref="questionRef" class="swiper-question" short :list="questionList" @midComplete="midComplete" prevText="上一页" nextText="下一页" @submit="submit" />
	
	<!-- 菜单 -->
	<template v-else>
		<view class="formal-menu">
			<view class="button block light title f38">认识心理健康</view>
			<view class="border-box mt30">
				<view class="button block light f36" :class="{ complete: completeStep >= 1 }" @click="q12Ref.open()">心理健康状态和保持</view>
				<view class="button block light f36" :class="{ complete: completeStep >= 2, disabled: completeStep < 1 }" @click="completeStep >= 1 && q21Ref.open()">心理困扰状态和应对</view>
				<view class="button block light f36" :class="{ complete: completeStep >= 3, disabled: completeStep < 2 }" @click="completeStep >= 2 && q31Ref.open()">心理障碍状态和治疗</view>
			</view>
		</view>
		<view class="button small-submit" style="width:300rpx" @click="goBack()">返回总目录</view>
	</template>
	
	<!-- 进入提示 弹窗 -->
	<uni-popup ref="popupRef" type="center">
		<view class="popup-dialog f30">
			<text class="iconfont i-ring"></text>
			
			<view class="f38 mb30">规则提示</view>
			<view>若您没有完成本小节的所有题目</view>
			<view>中途退出，我们不会保留您</view>
			<view>本小节的答题记录</view>
			
			<button class="iknow mt30" @click="popupRef.close()">我知道了</button>
		</view>
	</uni-popup>
	
	
	<!-- ===============================Q1 -->
	<!-- Q1-1 弹窗 -->
	<uni-popup ref="q11Ref" type="center">
		<view class="popup-fullscreen question q1-1">
			<view class="title">什么是心理健康？</view>
			<view class="intro-box">
				<view class="subtit">心理健康是健康的重要组成部分</view>
				<view class="info">它会影响我们的思考、感受和行为方式。它有助于确定我们如何处理压力、与他人相处以及做出健康的选择。</view>
			</view>
			<view class="button small-submit" @click="nextPage('q1-1')">下一页</view>
		</view>
	</uni-popup>
	
	<!-- Q1-2 弹窗 -->
	<uni-popup ref="q12Ref" type="center">
		<view class="popup-fullscreen question q1-2">
			<view class="button plain block btn-tit">心理状态</view>
			
			<view class="rainbow">
				<view class="types">
					<text>心理健康</text>
					<text>心理困扰</text>
					<text>心理障碍</text>
				</view>
				<view class="bar"></view>
				<view class="types">
					<text>健康</text>
					<text>疾病</text>
				</view>
			</view>
			
			<view class="button border block item">我们的身体偶尔会头疼感冒或出现严重疾病，心理上也会出现虚弱或疾病的状态。</view>
			<view class="button border block item">心理状态可大致分为三个层次，包括心理健康、心理困扰和心理障碍。</view>
			<view class="button border block item">在接下来的章节里，我们会认识每一种心理状态并掌握基本的应对方法。</view>
			
			<view class="button small-submit" @click="nextPage('q1-2', 'q1-3')">下一页</view>
		</view>
	</uni-popup>
	
	<!-- Q1-3 弹窗 -->
	<uni-popup ref="q13Ref" type="center">
		<view class="popup-fullscreen question q1-3">
			<view class="button plain block btn-tit">心理健康状态</view>
			
			<view class="button border block tips">世界卫生组织（WHO）认为，心理健康不仅仅是没有心理问题，更是一种积极的心理状态。在这种状态下，人能发挥自己的潜能，与身边人保持良好关系，为家庭、组织和社会作出有益贡献。</view>
			
			<view class="button block round main-btn" @click="nextPage('q1-3', 'q1')">开始答题</view>
		</view>
	</uni-popup>
	
	<!-- Q1-4 弹窗 -->
	<uni-popup ref="q14Ref" type="center">
		<view class="nav-title" :style="{ top: user.statusBarHeight }">案例题</view>
		<view class="popup-fullscreen question q1-4">
			<view class="intro"></view>
			<view class="btns">
				<view class="btn prev" @click="nextPage('q1-4', 'q1', true)">上一页</view>
				<view class="btn next" @click="nextPage('q1-4', 'q1-5')">下一页</view>
			</view>
		</view>
	</uni-popup>
	
	<!-- Q1-5 弹窗 -->
	<uni-popup ref="q15Ref" type="center">
		<view class="nav-title" :style="{ top: user.statusBarHeight }">案例题</view>
		<view class="popup-fullscreen question q1-5">
			<ConnectLine :info="lines1" @complete="lineComplete.lines1 = true" />
			<view class="btns" style="margin-top: 100rpx">
				<view class="btn prev" @click="nextPage('q1-5', 'q1-4')">上一页</view>
				<view class="btn next" @click="nextPage('q1-5', 'q1-6')">下一页</view>
			</view>
			<!-- <view class="button small-submit" @click="nextPage('q1-5', 'q1-6')">下一页</view> -->
		</view>
	</uni-popup>
	
	<!-- Q1-6 解析 -->
	<uni-popup ref="q16Ref" type="center">
		<view class="popup-fullscreen jiexi">
			<view class="title">答案解析</view>
			<view class="intro-box">
				<view class="answer">连线正确</view>
				<view class="subtit">解析</view>
				<view class="info">小艾处于健康的心理状态，她积极乐观，人际关系和谐，工作状态稳定，同时对生活有明确的目标。</view>
			</view>
			<view class="button small-submit" @click="nextPage('q1-6', 'q1')">下一页</view>
		</view>
	</uni-popup>
	
	<!-- ===============================Q2 -->
	<!-- Q2-1 弹窗 -->
	<uni-popup ref="q21Ref" type="center">
		<view class="popup-fullscreen question q2-1">
			<view class="button plain block btn-tit">心理困扰状态</view>
			<view class="button border block tips">由现实因素激发，持续时间短、情绪反应能在理智控制之下，不严重破坏社会功能、情绪反应尚未泛化的状态，处于心理健康状态与心理障碍状态之间。</view>
			<view class="button block round main-btn" @click="nextPage('q2-1', 'q2')">开始答题</view>
		</view>
	</uni-popup>
	
	<!-- Q2-2 弹窗 -->
	<uni-popup ref="q22Ref" type="center">
		<view class="nav-title" :style="{ top: user.statusBarHeight }">案例题</view>
		<view class="popup-fullscreen q2-2" v-if="questionStep == 'q2-2'">
			<DropCategory :info="dropCate1" @complete="nextPage('q2-2', 'q2-3')" />
		</view>
	</uni-popup>
	
	<!-- Q2-3 解析 -->
	<uni-popup ref="q23Ref" type="center">
		<view class="popup-fullscreen jiexi">
			<view class="title">答案解析</view>
			<view class="intro-box">
				<view class="answer">分类正确</view>
				<view class="subtit">解析</view>
				<view class="info">回想一下，你所经历的心理困扰，往往都是由哪类问题引起的？重点“突破”这些问题，有助于我们恢复心理健康状态。</view>
			</view>
			<view class="button small-submit" @click="nextPage('q2-3', 'q2-4')">下一页</view>
		</view>
	</uni-popup>
	
	<!-- Q2-4 对话 -->
	<uni-popup ref="q24Ref" type="center">
		<view class="popup-fullscreen">
			<Dialogue v-if="questionStep == 'q2-4'" :list="dialogue1" @submit="nextPage('q2-4', 'q2-5')" />
		</view>
	</uni-popup>
	
	<!-- Q2-5 连线 -->
	<uni-popup ref="q25Ref" type="center">
		<view class="nav-title" :style="{ top: user.statusBarHeight }">案例题</view>
		<view class="popup-fullscreen question q2-5">
			<ConnectLine :info="lines2" @complete="lineComplete.lines2 = true" />
			<view class="btns" style="margin-top: 100rpx">
				<view class="btn prev" @click="nextPage('q2-5', 'q2-4')">上一页</view>
				<view class="btn next" @click="nextPage('q2-5', 'q2-6')">下一页</view>
			</view>
			<!-- <view class="button small-submit" @click="nextPage('q2-5', 'q2-6')">提交</view> -->
		</view>
	</uni-popup>
	
	<!-- Q2-6 解析 -->
	<uni-popup ref="q26Ref" type="center">
		<view class="popup-fullscreen jiexi">
			<view class="title">答案解析</view>
			<view class="intro-box">
				<view class="answer">连线正确</view>
				<view class="subtit">解析</view>
				<view class="info">小张处于心理困扰的状态。因为生产压力大加上不适应新组长的管理方式，有很多负面情绪，工作态度也很消极，但并不会影响正常的生活。</view>
			</view>
			<view class="button small-submit" @click="nextPage('q2-6', 'q2')">下一页</view>
		</view>
	</uni-popup>
	
	
	<!-- ===============================Q3 -->
	<!-- Q3-1 弹框 -->
	<uni-popup ref="q31Ref" type="center">
		<view class="popup-fullscreen question q3-1">
			<view class="button plain block btn-tit">心理障碍状态</view>
			<view class="button border block tips">当个体在情绪、认知、行为的单一或多方面出现异常，且伴随精神痛苦或人际、工作、家庭功能方面的严重问题时，就可能处于心理障碍中。</view>
			<view class="button block round main-btn" @click="nextPage('q3-1', 'q3-2')">开始答题</view>
		</view>
	</uni-popup>
	
	<!-- Q3-2 弹窗 -->
	<uni-popup ref="q32Ref" type="center">
		<view class="popup-fullscreen q3-2">
			<view class="button plain block title">4种较为常见的心理障碍</view>
			<view class="states">
				<view class="state" :class="['s' + (i + 1), { done: o.done }]" v-for="(o, i) in stateArr" :key="i" @click="lookState(i)">
					<view>{{ o.name }}</view>
					<uni-icons type="checkbox-filled" class="check"></uni-icons>
				</view>
			</view>
			<view class="button small-submit" @click="lookState(5)">下一页</view>
		</view>
	</uni-popup>
	
	<!-- Q3-3 弹窗 -->
	<uni-popup ref="q33Ref" type="center">
		<view class="popup-fullscreen double-image q3-3">
			<swiper :duration="300" indicator-dots class="swiper-question">
				<swiper-item>
					<view class="button plain round block name" :style="{ marginTop: user.safePadHeight }">抑郁发作</view>
					<view class="button block light intro">情绪低落，兴趣减退，言语行为减少，失眠疲乏，消极悲观，自责自罪，厌世自杀等。</view>
					<view class="img img1"></view>
				</swiper-item>
				<swiper-item>
					<view class="button plain round block name" :style="{ marginTop: user.safePadHeight }">躁狂发作</view>
					<view class="button block light intro">不符情景的情绪高涨或易怒，自我评价夸大，精力充沛，思维奔逸，言语、行为或异常活动增多，严重时出现妄想和幻觉等症状。</view>
					<view class="img img2"></view>
				</swiper-item>
			</swiper>
			<view class="button small-submit" @click="lookState(1, true)">下一页</view>
		</view>
	</uni-popup>
	
	<!-- Q3-4 弹框 -->
	<uni-popup ref="q34Ref" type="center">
		<view class="popup-fullscreen question">
			<view class="button plain block btn-tit">焦虑症</view>
			<view class="button border block tips">焦虑症是最常见的心理障碍之一，患者会产生强烈、过度和持续的担心和恐惧。这些症状与实际情况或年龄不匹配，阻碍日常活动，并难以控制。</view>
			<view class="button block round plain main-btn" @click="lookState(2, true)">下一页</view>
		</view>
	</uni-popup>
	
	<!-- Q3-5 弹框 -->
	<uni-popup ref="q35Ref" type="center">
		<view class="popup-fullscreen question">
			<view class="button plain block btn-tit">精神分裂症</view>
			<view class="button border block tips">患者会出现妄想，即产生一些与自身相关的、与现实不相符的病理性思维，十分顽固，他人的解释、劝说等根本无法动摇。另外，患者可能出现幻觉，即在没有客观刺激的情况下，却有感官感受，例如：幻听、幻视。</view>
			<view class="button block round plain main-btn" @click="lookState(3, true)">下一页</view>
		</view>
	</uni-popup>
	
	<!-- Q3-6 连线 -->
	<uni-popup ref="q36Ref" type="center">
		<view class="nav-title" :style="{ top: user.statusBarHeight }">案例题</view>
		<view class="popup-fullscreen question q1-5">
			<ConnectLine :info="lines3" @complete="lineComplete.lines3 = true" />
			<view class="button small-submit" @click="nextPage('q3-6', 'q3')">提交</view>
		</view>
	</uni-popup>
	
	<!-- Q3-7 弹框 -->
	<uni-popup ref="q37Ref" type="center">
		<view class="popup-fullscreen intro">
			<view class="intro-ticket">
				<view class="dots">
					<text class="dot" v-for="v in 14" :key="v"></text>
				</view>
				<view class="button text">
					<view>早发现，早治疗，易康复</view>
					<view>处于心理障碍状态时，向专业人员求助是非常重要的第一步。通过规范的治疗，大部分患者都可以重归正常的生活。切记越早开始治疗，效果越好，康复的可能越大。</view>
				</view>
			</view>
			<view class="button plain round small-submit" @click="nextPage('q3-7', 'q3-8')">下一页</view>
		</view>
	</uni-popup>
	
	<!-- Q3-8 弹框 -->
	<uni-popup ref="q38Ref" type="center">
		<view class="popup-fullscreen intro">
			<view class="intro-ticket">
				<view class="dots">
					<text class="dot" v-for="v in 14" :key="v"></text>
				</view>
				<view class="button text">
					<view>非常重要！</view>
					<view>如果发现自己或他人有自伤或伤人的想法或计划，请立即拨打110求助。</view>
				</view>
			</view>
			<view class="button plain round small-submit" @click="nextPage('q3-8', 'q3-9')">下一页</view>
		</view>
	</uni-popup>
	
	<!-- Q3-9 弹框 -->
	<uni-popup ref="q39Ref" type="center">
		<view class="popup-fullscreen intro">
			<view class="intro-ticket">
				<view class="dots">
					<text class="dot" v-for="v in 14" :key="v"></text>
				</view>
				<view class="button text">
					<view>通过本模块的学习，我们了解到：</view>
					<view class="mt20">心理健康很重要，</view>
					<view>三种状态区分好，</view>
					<view>积极求助不丢人，</view>
					<view>助人还需本领高！</view>
				</view>
			</view>
			<view class="button plain round small-submit" @click="submit()">下一页</view>
		</view>
	</uni-popup>
	
	<!-- 完成进度 弹窗 -->
	<Process ref="processRef" :total="3" :step="completeStep" modelName="第一" :jobNum="user.loginJobnum" :isComplete="questionStep == 'q3-9'" @complete="goBack()" />
</view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { storeToRefs } from 'pinia';
import { useUserStore } from '@/stores/user';

import Question from '@/components/Question/index.vue';
import ConnectLine from '@/components/ConnectLine/index.vue';
import Process from '@/components/Process/index.vue';
import DropCategory from '@/components/DropCategory/index.vue';
import Dialogue from '@/components/Dialogue/index.vue';

import formatQuestion from '@/utils/format-formal-question.js';
import {
	goBack,
	getLocal,
	setLocal,
	postError,
	Toast,
	modelComplete,
} from '@/utils/tools.js';

const { user } = storeToRefs(useUserStore());

const modelKey = 'model2-complete';
const completeStep = ref(getLocal(modelKey) || 0); // 已完成的模块step
const questionList = ref([]);
const questionStep = ref(null);
const showQuestion = ref(true);
// 4种心理状态
const stateArr = ref([{
	name: '抑郁症',
}, {
	name: '双相情感障碍',
}, {
	name: '焦虑症',
}, {
	name: '精神分裂症',
}]);
const lineComplete = reactive({
	lines1: false,
	lines2: false,
	lines3: false,
})
// 连线对象1
const lines1 = ref({
	title: '请点击小艾的表现和|相应的心理状态',
	lineArr: [
		'与同事|相处融洽',
		'建立良好的|人际关系',
		'努力会让|生活更美好',
		'情绪积极',
		'工作认真|又快又好',
		'对组织有贡献',
		'通过学习成|为心理关爱|志愿者',
		'有成效的工作',
	],
	rightConn: [
		'0-1',
		'2-3',
		'4-7',
		'5-6',
	]
});
// 连线对象2
const lines2 = ref({
	title: '请将小张的表现跟他的|心理困扰状态连线',
	lineArr: [
		'情绪激动|愤怒不满',
		'觉得组长|在针对他',
		'与他人起冲突',
		'迟到',
		'工作表现不佳',
		'负面情绪多',
	],
	rightConn: [
		'0-5',
		'1-2',
		'3-4',
	]
});
// 连线对象3
const lines3 = ref({
	title: '请连线匹配以下典型表现|和心理障碍',
	lineArr: [
		'惊恐、焦虑',
		'焦虑症',
		'妄想、幻觉',
		'精神分裂症',
		'抑郁、躁狂',
		'抑郁症',
		'低落、厌世',
		'双相情感障碍',
	],
	rightConn: [
		'0-1',
		'2-3',
		'4-7',
		'5-6',
	]
});
// 拖动归类对象1
const dropCate1 = ref({
	title: '请将可能引发心理困扰的|问题移到对应的分类框中',
	totalProps: 8,
	category: [{
		name: '工作问题',
		list: [
			'生产压力',
			'调换岗位',
		]
	}, {
		name: '人际关系',
		list: [
			'与他人起冲突',
			'社交障碍',
		]
	}, {
		name: '婚恋问题',
		list: [
			'家庭矛盾',
			'失恋、单相思',
		]
	}, {
		name: '自身问题',
		list: [
			'过度追求完美',
			'过度自卑或内疚',
		]
	}],
});
// 对话列表
const dialogue1 = ref([{
	user: 0,
	msg: '你最近总是迟到，你遇到什么问题了吗？需要我帮忙吗？',
}, {
	user: 1,
	msg: '我不想看到新组长！他不讲道理！',
}, {
	user: 0,
	msg: '发生了什么事？',
}, {
	user: 1,
	msg: '这段时间生产压力大，我们总加班，有次忘记打卡，他居然不给我算加班！',
}, {
	user: 0,
	msg: '你有跟他沟通过吗？',
}, {
	user: 1,
	msg: '没有，我怀疑他就是故意针对我！',
}]);

const q11Ref = ref();
const q12Ref = ref();
const q13Ref = ref();
const q14Ref = ref();
const q15Ref = ref();
const q16Ref = ref();

const q21Ref = ref();
const q22Ref = ref();
const q23Ref = ref();
const q24Ref = ref();
const q25Ref = ref();
const q26Ref = ref();

const q31Ref = ref();
const q32Ref = ref();
const q33Ref = ref();
const q34Ref = ref();
const q35Ref = ref();
const q36Ref = ref();
const q37Ref = ref();
const q38Ref = ref();
const q39Ref = ref();
const processRef = ref();
const questionRef = ref();
const popupRef = ref();

const refs = reactive({
	q11Ref,
	q12Ref,
	q13Ref,
	q14Ref,
	q15Ref,
	q16Ref,

	q21Ref,
	q22Ref,
	q23Ref,
	q24Ref,
	q25Ref,
	q26Ref,

	q31Ref,
	q32Ref,
	q33Ref,
	q34Ref,
	q35Ref,
	q36Ref,
	q37Ref,
	q38Ref,
	q39Ref,
})


async function nextPage(current, next, noSwipe) {
	if ((current === 'q1-5' && next === 'q1-6') || (current === 'q2-5' && next === 'q2-6') || current === 'q3-6') {
		if (!lineComplete['lines' + current.substring(1, 2)]) {
			Toast('请先完成连线');
			return;
		}
	}
	
	// 格式化ref字符
	let currentRef = '';
	if (current?.includes('-')) {
		currentRef = current.replace(/-/g, '') + 'Ref';
	}
	let nextRef = '';
	if (next?.includes('-')) {
		nextRef = next.replace(/-/g, '') + 'Ref';
	}
	
	if (!next) {
		refs[currentRef].close();
		return;
	}
	
	if (next.includes('-')) {
		questionStep.value = next;
		refs[nextRef].open();
		refs[currentRef].close();
	} else {
		if (showQuestion.value) {
			// 首次进入答题
			const list = await ajax(`question/${next.replace('q', 'model2-')}.json`);
			questionList.value = formatQuestion(list, (obj, idx) => {
				// 插入中途切出的跳转
				if (next === 'q3') {
					if (idx === 0) {
						obj.midComplete = true;
					} else if (idx === 1) {
						obj.hidePrev = true;
					}
				} else {
					obj.midComplete = true;
				}
			});
		} else {
			// 答题中途切换出去，然后切回来
			showQuestion.value = true;
			if (!noSwipe) {
				questionRef.value.addSwiperIdx();
			}
		}
		questionStep.value = next;
		refs[currentRef].close();
	}
}

// 做题中途切出
function midComplete() {
	if (questionStep.value === 'q1' || questionStep.value === 'q1-6') {
		q14Ref.value.open();
	} else if (questionStep.value === 'q2' || questionStep.value === 'q2-6') {
		questionStep.value = 'q2-2';
		q22Ref.value.open();
	} else if (questionStep.value === 'q3') {
		lookState(0, true);
	}
	
	setTimeout(() => {
		showQuestion.value = false;
	}, 300)
}

// 全部做完
async function submit(arr) {
	switch(questionStep.value) {
		case 'q1':
		case 'q2':
			if (questionStep.value === 'q1') {
				modelComplete(modelKey, 1, completeStep);
				setLocal('answer-model2-1', arr.map(o => o.checked.join('')).join('-'));
			} else if (questionStep.value === 'q2') {
				modelComplete(modelKey, 2, completeStep);
				setLocal('answer-model2-2', arr.map(o => o.checked.join('')).join('-'));
			}
			processRef.value.open();
			setTimeout(() => {
				questionStep.value = null;
			}, 300)
		break;
		
		case 'q3':
			q37Ref.value.open();
			setLocal('answer-model2-3', arr.map(o => o.checked.join('')).join('-'));
			// setTimeout(() => {
			// 	questionStep.value = null;
			// }, 300)
		break;
		
		case 'q3-9':
			// 提交结果
			const { code, error } = await ajax({
				// url: 'api/api.php?a=putModule2',
				url: 'learn/submit',
				method: 'post',
				showLoading: true,
				data: {
					module: 2,
					jobnum: user.value.loginJobnum,
					op1: [
						getLocal('answer-model2-1'),
						getLocal('answer-model2-2'),
						getLocal('answer-model2-3'),
					].join('-'),
				}
			});
	
			// 异常处理
			if (code || error) {
				postError().then(() => {
					submit(arr);
				}).catch(() => {})
			} else {
				modelComplete(modelKey, 3, completeStep);
				processRef.value.open();
			}
		break;
	}
}

// 查看4种心理状态
function lookState(idx, done) {
	if (stateArr.value[idx]?.done) return;
	
	if (done) {
		stateArr.value[idx].done = true;
	}
	
	switch(idx) {
		case 0:
			if (done) {
				q32Ref.value.open();
			} else {
				nextPage('q3-2', 'q3');
			}
		break;
		
		case 1:
		case 2:
		case 3:
			if (done) {
				refs['q3' + (idx + 2) + 'Ref'].close()
			} else {
				refs['q3' + (idx + 2) + 'Ref'].open()
			}
		break;
		
		case 5:
			// 检查是否全部完成
			if (!stateArr.value.find(o => !o.done)) {
				nextPage('q3-2', 'q3-6');
				// return;
			} else {
				Toast('请先完成上面全部选项');
			}
		break;
	}
}

onMounted(() => {
	q11Ref.value.open();
})
</script>

<style lang="scss">
	@import 'index.scss';
</style>
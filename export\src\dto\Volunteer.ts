import { IsString, IsInt, IsOptional, IsDateString, IsEnum } from 'class-validator';

import { UserTypeMap } from '../types/enums';

/** 志愿者申请记录-查询参数 */
export class ApplyQueryDto {
  /** 工号 */
  @IsOptional()
  @IsString()
    jobnum: string;

  /** 工厂ID */
  @IsInt()
    factory_id: number;

  /** 工厂名称 */
  @IsString()
    factory_name: string;

  /** 用户身份 */
  @IsOptional()
  @IsEnum(UserTypeMap)
    user_type: UserTypeMap;

  /** 申请时间-开始时间 */
  @IsOptional()
  @IsDateString()
    start_time: string;

  /** 申请时间-结束时间 */
  @IsOptional()
  @IsDateString()
    end_time: string;
}

/** 志愿者申请记录-数据统计-查询参数 */
export class DataQueryDto {
  /** 工厂ID */
  @IsInt()
    factory_id: number;

  /** 工厂名称 */
  @IsString()
    factory_name: string;
}
import { Module, NestModule, MiddlewareConsumer } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';

import mysqlConfig from './config/mysql';
import modules from './modules/index';
import { LoggerMiddleware } from './middleware/Logger';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true, // 使配置可全局访问
      envFilePath: `.env.${process.env.NODE_ENV || 'production'}`,
    }),
    TypeOrmModule.forRoot({
      ...mysqlConfig[process.env.NODE_ENV || 'production'],
      entities: [__dirname + '/entities/*{.ts,.js}'],
      extra: {
        connectionLimit: 100, // 设置连接池的最大连接数
        waitForConnections: true, // 如果没有空闲连接，是否等待连接可用
        queueLimit: 0, // 排队连接请求的最大数量，0 表示不限制
      },
    }),
    ...modules,
  ],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    // 应用日志中间件
    consumer.apply(LoggerMiddleware).forRoutes('*');
  }
}

<template>
	<view
		class="login-index"
		:style="{ paddingTop: user.safePadHeight }">
		<!-- <uni-icons
			type="back"
			class="nav-back"
			@click="goBack()"></uni-icons> -->

		<!-- 登录后，没选性别 -->
		<view
			class="tac"
			v-if="userInfo && !userInfo.sex">
			<view class="sex-title f48 fwb">请选择你的性别</view>
			<view class="sex-tips f32">选择后不可更改</view>
			<view class="options df">
				<view
					class="options-li nan"
					:class="{ active: setSex === 1 }"
					@click="setSex = 1">
					<view class="iconfont i-nan"></view>
					我是男生
					<uni-icons
						class="checked"
						type="checkbox-filled"
						color="#5a85b5"
						size="32"></uni-icons>
				</view>
				<view
					class="options-li nv"
					:class="{ active: setSex === 2 }"
					@click="setSex = 2">
					<view class="iconfont i-nv"></view>
					我是女生
					<uni-icons
						class="checked"
						type="checkbox-filled"
						color="#5a85b5"
						size="32"></uni-icons>
				</view>
			</view>
			<button
				class="ui-button-blue submit"
				@click="updateSex">
				确定
			</button>
		</view>

		<template v-else>
			<view class="title fwb f56">
				<view>欢迎来到</view>
				<view>员工学习园地</view>
			</view>

			<input
				class="input"
				v-model.trim="number1"
				placeholder="请输入工号" />
			<input
				class="input"
				v-model.trim="number2"
				placeholder="请再输入一次" />

			<button
				class="ui-button-blue tologin"
				@click="submit">
				登录
			</button>
		</template>
	</view>

	<PreloadImage :list="preloadImage" />
</template>

<script setup>
import { ref } from 'vue';
import { storeToRefs } from 'pinia';
import { useUserStore } from '@/stores/user';
import { Toast, navigate } from '@/utils/tools.js';
import { UserType } from '@/utils/data-map.js';
import preloadMap from './preload-map.js';

import PreloadImage from '@/components/PreloadImage/index.vue';
const preloadImage = ref(preloadMap);

const { user } = storeToRefs(useUserStore());
const { updateUserInfo } = useUserStore();

const number1 = ref('');
const number2 = ref('');

const userInfo = ref();
// 提交登录
async function submit() {
	if (!number1.value || !number2.value) {
		Toast('请输入工号');
		return;
	}

	if (number1.value !== number2.value) {
		Toast('两次输入的工号不一致');
		return;
	}

	const { code, data } = await ajax({
		url: 'exclusive/login',
		method: 'post',
		data: {
			jobnum: number1.value
		}
	});
	if (code) return;
	userInfo.value = data;

	updateUserInfo('userid', data.id);
	updateUserInfo('userType', UserType[data.user_type]);
	updateUserInfo('sex', data.sex);
	updateUserInfo('loginJobnum', number1.value);

	// 跳到志愿者专属首页
	if (data.sex) {
		navigate('/exclusive/index');
	}
}

// 修改性别
const setSex = ref();
async function updateSex() {
	if (!setSex.value) {
		Toast('请先选择性别');
		return;
	}

	const { code, data } = await ajax({
		url: 'user/update-sex',
		method: 'post',
		data: {
			sex: setSex.value
		}
	});
	if (code) return;

	updateUserInfo('sex', setSex.value);
	navigate('/exclusive/index');
	setTimeout(() => {
		userInfo.value = null;
	}, 300);
}
</script>

<style lang="scss">
@import 'index.scss';
</style>

{
  "git.ignoreWindowsGit27Warning": true,
  "editor.fontFamily": "Fira Code",
  "editor.fontLigatures": "'ss01', 'ss02', 'ss05', 'ss03', 'ss07', 'ss08', 'cv01', 'cv02', 'cv16'",
  "terminal.integrated.fontFamily": "monospace",
  "typescript.tsdk": "node_modules/typescript/lib",
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "html",
    "vue",
    "wpy"
  ],
  "editor.tabSize": 2,
  // javaScript默认单引号
  "prettier.singleQuote": true,
  // 每行最大字符数(0 = 禁用)。
  "html.format.wrapLineLength": 300,
  // 保存时是否格式化文件
  "editor.formatOnSave": true,
  // 对象属性后是否需要添加逗号
  "prettier.trailingComma": "all",
  // 默认行尾字符。使用 \n 表示 LF，\r\n 表示 CRLF。
  "files.eol": "\n",
  // 是否保存时格式化
  "csscomb.formatOnSave": true,
  // 预订参数： "yandex"
  "csscomb.preset": "yandex",
  "javascript.updateImportsOnFileMove.enabled": "always",
  "files.associations": {
    "*.cjson": "jsonc",
    "*.wxss": "css",
    "*.wxs": "javascript"
  },
  "explorer.confirmDelete": false,
  "typescript.updateImportsOnFileMove.enabled": "always",
  "editor.maxTokenizationLineLength": 300,
  "diffEditor.ignoreTrimWhitespace": true,
  "editor.suggestSelection": "first",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit",
    "eslint.autoFixOnSave": "explicit"
  },
  "[jsonc]": {
    // "editor.defaultFormatter": "esbenp.prettier-vscode"
    "editor.defaultFormatter": "dbaeumer.vscode-eslint"
  },
  "[typescript]": {
    "editor.defaultFormatter": "dbaeumer.vscode-eslint"
  },
  "[javascript]": {
    "editor.defaultFormatter": "dbaeumer.vscode-eslint"
  },
  "[json]": {
    "editor.defaultFormatter": "dbaeumer.vscode-eslint"
  },
  "[vue]": {
    "editor.defaultFormatter": "dbaeumer.vscode-eslint"
  },
  "[javascriptreact]": {
    "editor.defaultFormatter": "dbaeumer.vscode-eslint"
  },
  "prettier.arrowParens": "avoid",
  "[css]": {
    "editor.defaultFormatter": "dbaeumer.vscode-eslint"
  },
  "[scss]": {
    "editor.defaultFormatter": "dbaeumer.vscode-eslint"
  },
  "workbench.editorAssociations": {
    "*.bson": "default"
  },
  "vue.codeActions.enabled": false
}
import { IsInt, IsOptional, IsString, IsEnum } from 'class-validator';
import { Transform } from 'class-transformer';

/** 测试类型 */
enum TestType {
  /** 全部 */
  All,
  /** 前测 */
  Before,
  /** 后测 */
  After
}
/** 小课堂-测评数据-查询参数 */
export class TestQueryDto {
  /** 工厂ID */
  @IsInt()
    factory_id: number;

  /** 工厂名称 */
  @IsString()
    factory_name: string;
    
  /** 测试类型 */
  @Transform(({ value }) => parseInt(value, 10))
  @IsEnum(TestType)
    type: TestType;
      
  /** 答题时间-开始 */
  @IsOptional()
  @IsString()
    start_time: string;

  /** 答题时间-结束 */
  @IsOptional()
  @IsString()
    end_time: string;

  /** IP地址 */
  @IsOptional()
  @IsString()
    ip: string;
}

/** 小课堂-数据统计-查询参数 */
export class DataQueryDto {
  /** 工厂ID */
  @IsInt()
    factory_id: number;

  /** 工厂名称 */
  @IsString()
    factory_name: string;
}
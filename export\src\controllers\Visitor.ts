import { Controller, Get, Query } from '@nestjs/common';

import { DataQueryDto, TestQueryDto } from '../dto/Visitor';
import { VisitorService } from '../services/Visitor';

@Controller('visitor')
export class VisitorController {
  constructor(private readonly service: VisitorService) {}

  /** 小课堂-测评数据 */
  @Get('test')
  async getTest(@Query() query: TestQueryDto): Promise<ResObj> {
    return this.service.getTest(query);
  }

  /** 小课堂-数据统计 */
  @Get('data')
  async getData(@Query() query: DataQueryDto): Promise<ResObj> {
    return this.service.getData(query);
  }

  /** 小课堂-数据统计-超管 */
  @Get('data-admin')
  async getDataAdmin(): Promise<ResObj> {
    return this.service.getDataAdmin();
  }
}

<!--
 * <AUTHOR>
 * @Date: 2022-11-15 23:13:08
 * @LastEditTime: 2023-07-23 01:20:18
 * @description 勋章介绍
-->
<template>
<view class="formal-intro" :style="{ paddingTop: user.statusBarHeight }">
	<uni-icons type="back" class="nav-back" @click="goBack"></uni-icons>
	
	<swiper :style="{ height: `calc(100vh - ${user.safePadHeight})` }" :duration="300" indicator-dots class="swiper-question">
		<swiper-item>
			<view class="title">学习奖励</view>
			<view class="img mt50">
				<view class="bg"></view>
			</view>
			<view class="tips">
				<view>亲爱的学员：</view>
				<view>欢迎开始你的学习之旅</view>
				<view>每完成一门课程</view>
				<view>即可获得一枚勋章</view>
			</view>
		</swiper-item>
		<swiper-item>
			<view class="title">学习奖励</view>
			<view class="img mt50">
				<view class="bg"></view>
				<text class="medal" :class="['medal' + i]" v-for="i in 4" :key="i"></text>
			</view>
			<view class="tips tac" style="width:100%">
				<view>集齐所有勋章，且满分完成后测</view>
				<view>你将获得《心理关爱志愿者》证书</view>
			</view>
		</swiper-item>
	</swiper>
</view>
</template>

<script setup>
import { storeToRefs } from 'pinia';
import { useUserStore } from '@/stores/user';

import {
	goBack,
} from '@/utils/tools.js';

const { user } = storeToRefs(useUserStore());
</script>

<style lang="scss">
	@import 'index.scss';
</style>
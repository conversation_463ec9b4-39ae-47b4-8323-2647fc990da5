/**
 * @description 表格字段
 */

// 筛选字段
export const searchField = [
  {
    label: '工厂：',
    prop: 'factory_id',
    formOpts: {
      componentName: 'ElSelect',
      optsList: [],
      props: {
        style: 'width:160px',
        filterable: true,
        'default-first-option': true,
      },
    },
  },
  {
    label: '工号：',
    prop: 'jobnum',
    width: '190px',
    formOpts: { props: { clearable: true } },
  },
  {
    label: '获取时间：',
    prop: 'create_time',
    formOpts: {
      componentName: 'ElDatePicker',
      props: {
        style: 'width:240px',
        type: 'daterange',
        'range-separator': '至',
        'start-placeholder': '开始日期',
        'end-placeholder': '结束日期',
        'value-format': 'yyyy-MM-dd',
        clearable: true,
      },
    },
  },
  {
    label: '',
    prop: 'export',
    customClass: 'poa export',
    defaultValue: '明细',
    formOpts: {
      componentName: 'a',
      props: {
        class: 'i-export vam',
        title: '导出明细',
      },
      events: {},
    },
  }
];

// 表格中的字段
export const tableCols = [
  {
    prop: 'jobnum',
    label: '工号',
    minWidth: '100px',
    align: 'center'
  },
  {
    prop: 'training',
    label: '志愿者培训',
    minWidth: '90px',
    align: 'center',
  },
  {
    prop: 'checkin',
    label: '打卡活动',
    minWidth: '90px',
    align: 'center',
  },
  {
    prop: 'service',
    label: '服务记录',
    minWidth: '90px',
    align: 'center',
  },
];
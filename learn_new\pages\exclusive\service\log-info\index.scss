@import '@/scss/mixins.scss';

.service-log-info {
	background: #fff;
	padding-bottom: calc(104rpx + env(safe-area-inset-bottom));

	.log-title {
		margin: 40rpx 140rpx 40rpx 48rpx;
		line-height: 64rpx;
		font-size: 56rpx;
		font-weight: bold;
		word-break: break-all;
		.moretools {
			position: absolute;
			right: 48rpx;
			top: 40rpx;
			width: 64rpx;
			height: 64rpx;
			line-height: 66rpx;
			border-radius: 50%;
			background: #f2f2f2;
			text-align: center;
			transform: rotate(90deg);
			font-weight: normal;
		}
	}
	.edit-tooltips {
		position: absolute;
		left: 0;
		right: 0;
		top: 0;
		bottom: 0;
		display: none;
		.ct {
			position: absolute;
			right: 48rpx;
			top: 120rpx;
			width: 190rpx;
			height: 230rpx;
			box-sizing: border-box;
			font-size: 28rpx;
			background: #fff;
			border: 1rpx solid #f4f4f4;
			font-weight: normal;
		}
		.item {
			margin: 8rpx 0;
			.uni-icons {
				margin-right: 6rpx;
			}
		}
		&.show {
			display: block;
		}
	}

	.module-name {
		display: flex;
		align-items: center;
		padding: 0 48rpx;
		.parent {
			padding: 0 24rpx;
			height: 64rpx;
			line-height: 64rpx;
			background: $c_module1;
			border-radius: 32rpx;
			font-size: 28rpx;
			margin-right: 20rpx;
			color: #fff;
			&.module2 {
				background: $c_module2;
			}
			&.module3 {
				background: $c_module3;
			}
			&.module4 {
				background: $c_module4;
			}
		}
	}

	.select-items {
		margin: 32rpx 48rpx 0;
		display: flex;
		justify-content: space-between;
		padding-bottom: 32rpx;
		.select-item {
			display: flex;
			align-items: center;
			line-height: 40rpx;
		}
	}

	.check-status {
		width: 654rpx;
		background: rgba(252, 197, 56, 0.2);
		border-radius: 30rpx;
		padding: 24rpx 32rpx;
		box-sizing: border-box;
		margin: 0 auto;
		.df {
			align-items: center;
		}
		&.status1 {
			background: rgba(126, 183, 142, 0.2);
			.iconfont {
				color: #7eb78e;
			}
		}
		&.status2 {
			background: rgba(240, 143, 143, 0.2);
			.iconfont {
				color: #e65757;
			}
		}
		&.status3 {
			background: rgba(252, 197, 56, 0.2);
			.iconfont {
				color: #f8aa14;
			}
		}
	}
	.check-status__ct {
		margin-top: 12rpx;
		line-height: 48rpx;
	}

	.harvest {
		padding: 32rpx 48rpx;
		background: #f2f2f2;
		margin: 32rpx 48rpx 0;
	}
	.harvest-title {
		margin: 0 0 10rpx;
		font-size: 32rpx;
		font-weight: bold;
	}
	.harvest-items {
		margin-top: 24rpx;
	}
	.harvest-items__item {
		margin-left: 26rpx;
	}

	.desc {
		margin-top: 32rpx;
		padding: 0 48rpx;
		font-size: 32rpx;
		line-height: 54rpx;
		word-wrap: break-word;
		word-break: break-all;
		.desc-title {
			color: $c_c;
		}
		.desc-content {
			margin-bottom: 16rpx;
		}
	}
	.imgs {
		display: flex;
		flex-wrap: wrap;
		margin: 64rpx 48rpx -20rpx;
		.img {
			width: 192rpx;
			height: 192rpx;
			margin: 0 20rpx 20rpx 0;
			border-radius: 12rpx;
			background: #f2f2f2;
		}
	}

	.ui-next-arrow {
		background: #fff;
		.uni-icons {
			color: $c_green !important;
			font-size: 50rpx !important;
			font-weight: bold;
		}
	}
}

<template>
<view class="formal-model5" :class="{ 'swiper-question-wrap': questionStep }" :style="{ paddingTop: user.statusBarHeight }">
	<uni-icons type="back" class="nav-back" @click="goBack()"></uni-icons>
	
	<!-- 题目列表 -->
	<view class="nav-title" v-if="questionStep" :style="{ top: user.statusBarHeight }">案例题</view>
	<Question v-if="questionStep" v-show="showQuestion" ref="questionRef" class="swiper-question" :short="shortQuestion" :list="questionList" @midComplete="midComplete" prevText="上一页" nextText="下一页" @submit="submit" />
	
	<!-- 菜单 -->
	<view v-else v-show="showMenu">
		<view class="formal-menu short">
			<view class="button block plain title f34">帮助疑似抑郁症的员工</view>
			<view class="border-box mt30">
				<view class="button block light f36" :class="{ complete: completeStep >= i + 1, disabled: completeStep < i }" @click="menuClick(i)" v-for="(v, i) in menuArr" :key="i">{{ v }}</view>
			</view>
		</view>
		<view class="button small-submit" style="width:300rpx" @click="goBack()">返回总目录</view>
	</view>
	
	
	<!-- ===============================Q2 -->
	<uni-popup ref="q21Ref" type="center">
		<view class="popup-fullscreen intro">
			<view class="intro-ticket">
				<view class="dots">
					<text class="dot" v-for="v in 14" :key="v"></text>
				</view>
				<view class="button text">除了帮助存在心理困扰的员工，志愿者/管理者还需要帮助存在心理障碍的员工，抑郁症是最常见的心理障碍之一。</view>
			</view>
			<view class="button plain round small-submit" @click="nextPage('q2-1', 'q2-2')">下一页</view>
		</view>
	</uni-popup>
	
	<uni-popup ref="q22Ref" type="center">
		<view class="popup-fullscreen question">
			<view class="button plain block btn-tit">了解抑郁症</view>
			
			<view class="button border block tips">抑郁症是十分常见的心理障碍，但大家仍然对它缺乏了解。据世界卫生组织（WHO）估计，中国有5400万抑郁症患者。要帮助疑似抑郁症的员工，志愿者/管理者首先需要正确认识抑郁症，包括了解抑郁症的症状、发病原因、诊断与治疗等。</view>
			
			<view class="button block round main-btn" @click="nextPage('q2-2', 'q2')">下一页</view>
		</view>
	</uni-popup>
	
	
	<!-- ===============================Q3 -->
	<uni-popup ref="q31Ref" type="center">
		<view class="popup-fullscreen question q3-1">
			<view class="button plain block btn-tit">初步识别疑似抑郁症的员工——R</view>
			
			<view class="button border block tips">
				<view>在工作场所，员工出现以下异常表现，志愿者/管理者便需要引起注意：</view>
				<view class="c_yellow mt20">工作状态变化：</view>
				<view>无法完成常规工作任务，迟到早退次数增加，经常忘记工作事宜，犯错误的次数增加，工作中无法集中注意力</view>
				<view class="c_yellow mt20">情绪状态变化：</view>
				<view>变得优柔寡断，敏感易怒，社交兴趣降低，看起来很疲惫、劳累</view>
			</view>
			
			<view class="button block round main-btn" @click="nextPage('q3-1', 'q3')">下一页</view>
		</view>
	</uni-popup>
	
	
	<!-- ===============================Q4 -->
	<uni-popup ref="q41Ref" type="center">
		<view class="popup-fullscreen question">
			<view class="button plain block btn-tit">与疑似抑郁症的员工谈心——T</view>
			
			<view class="button border block tips">在初步识别后，志愿者/管理者需要通过与员工谈心来获取更多信息，以进一步判断该员工的情况是否属于抑郁征兆。</view>
			
			<view class="button block round main-btn" @click="nextPage('q4-1', 'q4-2')">下一页</view>
		</view>
	</uni-popup>
	
	<uni-popup ref="q42Ref" type="center">
		<view class="popup-fullscreen double-image q4-2">
			<swiper :duration="300" indicator-dots class="swiper-question">
				<swiper-item>
					<view class="button plain round block name" :style="{ marginTop: user.safePadHeight }">心理困扰</view>
					<view class="img img1"></view>
				</swiper-item>
				<swiper-item>
					<view class="button plain round block name" :style="{ marginTop: user.safePadHeight }">抑郁征兆</view>
					<view class="img img2"></view>
				</swiper-item>
			</swiper>
			<view class="button small-submit" @click="nextPage('q4-2', 'q4-3')">下一页</view>
		</view>
	</uni-popup>
	
	<uni-popup ref="q43Ref" type="center">
		<view class="popup-fullscreen">
			<Dialogue v-if="questionStep == 'q4-3'" :list="dialogue1" @submit="nextPage('q4-3', 'q4')" />
		</view>
	</uni-popup>
	
	<uni-popup ref="q44Ref" type="center">
		<view class="popup-fullscreen">
			<Dialogue v-if="questionStep == 'q4-4'" :list="dialogue2" @submit="nextPage('q4-4', 'q41')" />
		</view>
	</uni-popup>
	
	<uni-popup ref="q47Ref" type="center">
		<view class="popup-fullscreen">
			<Dialogue v-if="questionStep == 'q4-7'" :list="dialogue3" @submit="nextPage('q4-7', 'q42')" />
		</view>
	</uni-popup>
	
	<uni-popup ref="q48Ref" type="center">
		<view class="popup-fullscreen question q4-8">
			<view class="button plain block btn-tit f36">关于抑郁症的关键问题参考</view>
			
			<view class="button block tag">你情绪低落持续多久了？</view>
			<view class="button block tag">你有什么爱好，都不进行了吗？</view>
			<view class="button block tag">你最近两周有睡眠问题吗？</view>
			<view class="button block tag">你经常感到疲惫吗？</view>
			<view class="button block tag">你是否更愿意一个人待在家里/寝室？</view>
			<view class="button block tag">你有感到难以集中注意力或者难以思考问题吗？</view>
			<view class="button block tag">你有出现原因不明的身体疼痛吗？</view>
			<view class="button block tag">你的胃口是否急剧减小/增大？</view>
			<view class="button block tag">你感到特别自责或者内疚吗？</view>
			
			<view class="button block round main-btn" @click="nextPage('q4-8', 'q4-9')">下一页</view>
		</view>
	</uni-popup>
	
	<uni-popup ref="q49Ref" type="center">
		<view class="popup-fullscreen intro">
			<view class="intro-ticket">
				<view class="dots">
					<text class="dot" v-for="v in 14" :key="v"></text>
				</view>
				<view class="button text">在询问中，志愿者/管理者如果发现员工存在心境抑郁、情绪低落的症状持续超过2周，或者对4个以上的问题给出了肯定的答案，便需要按照疑似抑郁的情况提供帮助。</view>
			</view>
			<view class="button block round main-btn" @click="submit()">我知道了</view>
		</view>
	</uni-popup>
	
	
	<!-- ===============================Q5 -->
	<uni-popup ref="q51Ref" type="center">
		<view class="popup-fullscreen question">
			<view class="button plain block btn-tit">帮助疑似抑郁症的员工——H</view>
			
			<view class="button border block tips">
				<view class="mb20">抑郁症患者需要接受专业的诊断和治疗。因此，在帮助疑似抑郁症的员工时，最重要的是要鼓励其就医。志愿者/管理者在鼓励员工就医时要注意保持耐心，并且尊重员工的意愿。</view>
				<view>· 展现同理心</view>
				<view>· 降低耻病感</view>
				<view>· 提供就医帮助</view>
				<view>· 持续鼓励就医</view>
				<view>· 尊重员工意愿</view>
			</view>
			
			<view class="button block round main-btn" @click="nextPage('q5-1', 'q5')">下一页</view>
		</view>
	</uni-popup>
	
	
	<!-- ===============================Q6 -->
	<uni-popup ref="q61Ref" type="center">
		<view class="popup-fullscreen question">
			<view class="button plain block btn-tit">警惕自杀风险</view>
			
			<view class="button border block tips">
				<view>抑郁症患者可能出现自杀倾向。因此，志愿者/管理者需要警惕疑似抑郁症的员工出现自杀征兆。可以从三个方面来观察员工是否存在自杀倾向。</view>
				<view class="c_yellow mt20">语言征兆：</view>
				<view>是否直接或间接表达想要自杀</view>
				<view class="c_yellow mt20">行为征兆：</view>
				<view>是否做出与自杀相关的行为</view>
				<view class="c_yellow mt20">情景征兆：</view>
				<view>是否遭遇了重大危机事件</view>
			</view>
			
			<view class="button block round main-btn" @click="nextPage('q6-1', 'q6')">下一页</view>
		</view>
	</uni-popup>
	
	<uni-popup ref="q62Ref" type="center">
		<view class="popup-fullscreen question">
			<view class="button plain block btn-tit">处理自杀危机的总原则</view>
			
			<view class="button border block tips">
				<view><text class="c_yellow">生命第一原则：</text>立即采取保护或送医措施，最大限度地保护员工及周围人的人身安全；</view>
				<view class="mt20"><text class="c_yellow">快速响应原则：</text>及时有效地进行上下、内外沟通；</view>
				<view class="mt20"><text class="c_yellow">亲属参与原则：</text>以最快的速度通知员工家属；</view>
				<view class="mt20"><text class="c_yellow">全程监护原则：</text>安排专人对干预对象全程监护；</view>
				<view class="mt20"><text class="c_yellow">保护隐私原则：</text>所有信息应仅限于相关工作人员知悉，未经当事人允许，禁止将员工信息泄露给与此事无关的领导同事等人。</view>
			</view>
			
			<view class="button block round main-btn" @click="nextPage('q6-2', 'q6-3')">我知道了</view>
		</view>
	</uni-popup>
	
	<uni-popup ref="q63Ref" type="center">
		<view class="popup-fullscreen">
			<Dialogue v-if="questionStep == 'q6-3'" :list="dialogue4" @submit="nextPage('q6-3', 'q6')" />
		</view>
	</uni-popup>
	
	<uni-popup ref="q64Ref" type="center">
		<view class="popup-fullscreen">
			<Dialogue v-if="questionStep == 'q6-4'" :list="dialogue5" @submit="nextPage('q6-4', 'q61')" />
		</view>
	</uni-popup>
	
	
	<!-- ===============================Q7 -->
	<uni-popup ref="q71Ref" type="center">
		<view class="popup-fullscreen question">
			<view class="button plain block btn-tit">练习</view>
			
			<view class="button border block tips">可以与其他志愿者轮换扮演疑似抑郁症的员工进行该模块RTH的练习。</view>
			
			<view class="button block round main-btn" @click="openComplete()">我知道了</view>
		</view>
	</uni-popup>
	
	
	<!-- 完成进度 弹窗 -->
	<Process ref="processRef" :total="6" :step="completeStep" modelName="第四" :jobNum="user.loginJobnum" :isComplete="questionStep == 'q7-1'" @complete="goBack()" />
</view>
</template>

<script setup>
import { ref, reactive, nextTick } from 'vue';
import { storeToRefs } from 'pinia';
import { useUserStore } from '@/stores/user';

import Question from '@/components/Question/index.vue';
import Dialogue from '@/components/Dialogue/index.vue';
import Process from '@/components/Process/index.vue';

import formatQuestion from '@/utils/format-formal-question.js';
import {
	goBack,
	getLocal,
	setLocal,
	postError,
	modelComplete,
} from '@/utils/tools.js';

const { user } = storeToRefs(useUserStore());

const modelKey = 'model5-complete';
const completeStep = ref(getLocal(modelKey) || 0); // 已完成的模块step
const questionList = ref([]);
const shortQuestion = ref(false);
const questionStep = ref(null);
const showQuestion = ref(true);
const showMenu = ref(true);
const menuArr = ref([
	'了解抑郁症',
	'初步识别疑似抑郁症的员工——R',
	'跟疑似抑郁症的员工谈心——T',
	'帮助疑似抑郁症的员工——H',
	'警惕自杀风险',
	'小结',
]);

// 对话列表1
const dialogue1 = ref([{
	user: 0,
	msg: '我最近心情不好。',
}, {
	user: 1,
	msg: '是遇到什么困难了吗？',
}, {
	user: 0,
	msg: '我和家人吵架了…….',
}, {
	user: 1,
	msg: '近期总体情绪状态如何？食欲睡眠如何？',
}, {
	user: 0,
	msg: '情绪总体比较平稳，睡眠、食欲都正常。',
}]);
// 对话列表2
const dialogue2 = ref([{
	user: 0,
	msg: '我不想出门，什么都不想干。',
}, {
	user: 1,
	msg: '也不打你喜爱的篮球了吗？',
}, {
	user: 0,
	msg: '做什么都觉得没意思，很累。',
}, {
	user: 1,
	msg: '是休息时间不够吗？',
}, {
	user: 0,
	msg: '每天睡十个小时还是提不起精神。',
}, {
	user: 1,
	msg: '这种状态持续多久了？',
}, {
	user: 0,
	msg: '大概四五个月了',
}]);
// 对话列表3
const dialogue3 = ref([{
	user: 0,
	msg: '我常常莫名其妙地感到心情不好、想哭。',
}, {
	user: 1,
	msg: '你是不是抑郁了？',
}, {
	user: 0,
	msg: '我自己也不清楚。',
}]);
// 对话列表4
const dialogue4 = ref([{
	user: 0,
	msg: '我感觉活着没意思，很痛苦。',
}, {
	user: 1,
	msg: '痛苦的时候，你有想过伤害自己吗？',
}, {
	user: 0,
	msg: '想过吧',
}, {
	user: 1,
	msg: '在你最难受的时候，有过轻生的念头吗？',
}, {
	user: 0,
	msg: '也许吧',
}, {
	user: 1,
	msg: '如果有，可以谈谈具体想法吗？是否有计划或准备？',
}]);
// 对话列表5
const dialogue5 = ref([{
	user: 0,
	msg: '我最近确实是有过自杀的念头了',
}, {
	user: 1,
	msg: '你怎么可以有这么荒唐和不负责任的想法呢？',
}, {
	user: 1,
	msg: '成年人能不能成熟稳重一点？',
}, {
	user: 1,
	msg: '提高自己的抗压能力吧！',
}, {
	user: 0,
	msg: '我...',
}, {
	user: 1,
	msg: '好了，就这样吧。',
}]);
const answerModel53 = ref([]); // 第三章节的答案
const answerModel55 = ref([]); // 第五章节的答案

const q21Ref = ref();
const q22Ref = ref();

const q31Ref = ref();

const q41Ref = ref();
const q42Ref = ref();
const q43Ref = ref();
const q44Ref = ref();
const q47Ref = ref();
const q48Ref = ref();
const q49Ref = ref();

const q51Ref = ref();

const q61Ref = ref();
const q62Ref = ref();
const q63Ref = ref();
const q64Ref = ref();

const q71Ref = ref();

const processRef = ref();
const questionRef = ref();

const refs = reactive({
	q21Ref,
	q22Ref,

	q31Ref,

	q41Ref,
	q42Ref,
	q43Ref,
	q44Ref,
	q47Ref,
	q48Ref,
	q49Ref,

	q51Ref,

	q61Ref,
	q62Ref,
	q63Ref,
	q64Ref,

	q71Ref,
})

function menuClick(i) {
	if (completeStep.value < i) return;
	
	if (i === 5) {
		nextPage('', 'q7');
	} else {
		refs[`q${i + 2}1Ref`].open();
	}
}

async function nextPage(current, next, noSwipe) {
	// 格式化ref字符
	let currentRef = '';
	if (current?.includes('-')) {
		currentRef = current.replace(/-/g, '') + 'Ref';
	}
	let nextRef = '';
	if (next?.includes('-')) {
		nextRef = next.replace(/-/g, '') + 'Ref';
	}

	if (next.includes('-')) {
		questionStep.value = next;
		refs[nextRef].open();
		refs[currentRef]?.close();
	} else {
		if (showQuestion.value) {
			// 首次进入答题
			
			// 让组件重新渲染
			if (['q4', 'q41', 'q42', 'q61'].includes(next)) {
				setTimeout(() => {
					questionStep.value = null;
					setTimeout(() => {
						questionStep.value = next;
					}, 10)
				}, 0)
			}
			
			if (next === 'q5') {
				shortQuestion.value = true;
			} else {
				shortQuestion.value = false;
			}
			
			const list = await ajax(`question/${next.replace('q', 'model5-')}.json`);
			questionList.value = formatQuestion(list, (obj, idx) => {
				// 插入中途切出的跳转
				if (next === 'q44') {
					if ([0, 2].includes(idx)) {
						obj.midComplete = true;
					} else if ([1, 3].includes(idx)) {
						obj.hidePrev = true;
					}
				// } else if (next === 'q5') {
				// 	if (idx === 4) {
				// 		obj.midComplete = true;
				// 	} else if (idx === 5) {
				// 		obj.hidePrev = true;
				// 	}
				} else if (next === 'q6') {
					shortQuestion.value = false;
					if (idx === 2) {
						obj.midComplete = true;
					} else if (idx === 3) {
						obj.hidePrev = true;
					}
				}
			});
		} else {
			// 答题中途切换出去，然后切回来

			showQuestion.value = true;
			if (!noSwipe) {
				questionRef.value.addSwiperIdx();
			}
		}
		questionStep.value = next;
		refs[currentRef]?.close();
	}
}

// 做题中途切出
function midComplete() {
	if (questionStep.value === 'q6') {
		questionStep.value = 'q62Ref';
	}
	refs[questionStep.value].open();
	
	setTimeout(() => {
		showQuestion.value = false;
	}, 300)
}

// 全部做完
async function submit(arr) {
	switch(questionStep.value) {
		case 'q4':
		case 'q41':
		case 'q42':
		case 'q6':
			if (questionStep.value === 'q4') {
				questionStep.value = 'q4-4';
				answerModel53.value = arr;
			} else if (questionStep.value === 'q41') {
				questionStep.value = 'q4-7';
				answerModel53.value.push(arr[0]);
			} else if (questionStep.value === 'q42') {
				questionStep.value = 'q4-8';
				answerModel53.value.push(arr[0]);
			} else if (questionStep.value === 'q6') {
				questionStep.value = 'q6-4';
				answerModel55.value = arr;
			}
			refs[questionStep.value.replace(/-/g, '') + 'Ref'].open();
		break;
		
		case 'q2':
		case 'q3':
		case 'q4-9':
		case 'q5':
		case 'q61':
		case 'q7':
			showMenu.value = true;
			if (questionStep.value === 'q2') {
				modelComplete(modelKey, 1, completeStep);
				processRef.value.open();
				setLocal('answer-model5-1', { answer: arr.map(o => o.checked.join('')).join('-'), rights: arr.filter(o => o.correct).length });
			} else if (questionStep.value === 'q3') {
				modelComplete(modelKey, 2, completeStep);
				processRef.value.open();
				setLocal('answer-model5-2', { answer: arr.map(o => o.checked.join('')).join('-'), rights: arr.filter(o => o.correct).length });
			} else if (questionStep.value === 'q4-9') {
				modelComplete(modelKey, 3, completeStep);
				processRef.value.open();
				setLocal('answer-model5-3', { answer: answerModel53.value.map(o => o.checked.join('')).join('-'), rights: answerModel53.value.filter(o => o.correct).length });
			} else if (questionStep.value === 'q5') {
				modelComplete(modelKey, 4, completeStep);
				processRef.value.open();
				setLocal('answer-model5-4', { answer: arr.map(o => o.checked.join('')).join('-'), rights: arr.filter(o => o.correct).length });
			} else if (questionStep.value === 'q61') {
				modelComplete(modelKey, 5, completeStep);
				processRef.value.open();
				
				const answer = [...answerModel55.value, ...arr];
				setLocal('answer-model5-5', { answer: answer.map(o => o.checked.join('')).join('-'), rights: answer.filter(o => o.correct).length });
			} else if (questionStep.value === 'q7') {					
				// 提交结果
				const op6 = JSON.stringify({ answer: arr.map(o => o.checked.join('')).join('-'), rights: arr.filter(o => o.correct).length });
				const { code, error } = await ajax({
					// url: 'api/api.php?a=putModule5',
					url: 'learn/submit',
					method: 'post',
					showLoading: true,
					data: {
						module: 5,
						jobnum: user.value.loginJobnum,
						op1: JSON.stringify(getLocal('answer-model5-1')),
						op2: JSON.stringify(getLocal('answer-model5-2')),
						op3: JSON.stringify(getLocal('answer-model5-3')),
						op4: JSON.stringify(getLocal('answer-model5-4')),
						op5: JSON.stringify(getLocal('answer-model5-5')),
						op6,
					}
				});
	
				// 异常处理
				if (code || error) {
					postError().then(() => {
						submit(arr);
					}).catch(() => {})
				} else {
					modelComplete(modelKey, 6, completeStep);
					q71Ref.value.open();
					refs[questionStep.value.replace(/-/g, '') + 'Ref']?.close();
					setTimeout(() => {
						questionStep.value = null;
					}, 200)
				}
				return;
			}
			refs[questionStep.value.replace(/-/g, '') + 'Ref']?.close();
			setTimeout(() => {
				questionStep.value = null;
			}, 200)
		break;
	}
}

function openComplete() {
	// 需要等复制完成以后再打开弹框，避免isComplete=false
	questionStep.value = 'q7-1';
	nextTick(() => {
		processRef.value.open();
	})
}
</script>

<style lang="scss">
	@import 'index.scss';
</style>
@import '@/scss/mixins.scss';

.formal-medal {
	background: linear-gradient(-30deg, #fff, #f0fafc, #fff);
	display: flex;
	flex-direction: column;

	.title {
		margin-top: 50rpx;
		font-size: 68rpx;
		text-align: center;
		letter-spacing: 10rpx;
	}
	.subtitle {
		font-size: 34rpx;
		text-align: center;
		letter-spacing: 5rpx;
	}
	.medals {
		display: flex;
		flex-wrap: wrap;
		width: 560rpx;
		margin: 0 auto 100rpx;
		justify-content: space-between;
	}
	.medal {
		background: url('#{$imgHost}learn/medal0.png') 0 0/100% 100%;
		width: 249rpx;
		height: 249rpx;
		margin-top: 80rpx;
	}
	.got {
		&.medal1 {
			background-image: url('#{$imgHost}learn/medal1.png');
		}
		&.medal2 {
			background-image: url('#{$imgHost}learn/medal2.png');
		}
		&.medal3 {
			background-image: url('#{$imgHost}learn/medal3.png');
		}
		&.medal4 {
			background-image: url('#{$imgHost}learn/medal4.png');
		}
	}

	.button {
		width: 400rpx;
		font-weight: normal;
	}
}

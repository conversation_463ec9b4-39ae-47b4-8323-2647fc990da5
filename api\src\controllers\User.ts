import { Controller, Get, Headers } from '@nestjs/common';

import { CustomHeadersDto } from '../dto';
import { UserService } from '../services/User';
import { validateDto } from '../filters/validate-dto';

@Controller('user')
export class UserController {
  constructor(private readonly service: UserService) {}

  /** 用户积分数据 */
  @Get('score-info')
  async scoreInfo(
    @Headers() headers: CustomHeadersDto,
  ): Promise<ResObj> {
    const headerDto = await validateDto(CustomHeadersDto, headers);
    return this.service.scoreInfo(headerDto);
  }
}

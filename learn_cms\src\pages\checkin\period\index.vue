<template>
  <div class="checkin-period ui-layout-col">
    <afc-filter-bar
      class="mb10"
      size="small"
      :field="searchField"
      :init-filter="searchForm"
      :show-button="false"
      @valueChange="searchForm = $event; init()" />

    <div class="flex1">
      <x-table
        :data-set="dataSet"
        :load-list-failed="loadFailed"
        @reload="init">
        <template v-for="obj in tableCols">
          <el-table-column
            v-if="obj.prop === 'factory_id'"
            v-bind="obj"
            :key="obj.prop">
            <template slot-scope="scope">
              {{ factoryList.find(o => o.value === scope.row.factory_id) ? factoryList.find(o => o.value === scope.row.factory_id).label : scope.row.factory_id }}
            </template>
          </el-table-column>
          <el-table-column
            v-else-if="obj.prop === 'days'"
            v-bind="obj"
            :key="obj.prop">
            <template slot-scope="scope">
              {{ scope.row.days || scope.row.step_len.split(',').reduce((pre, cur) => {
                return pre + parseInt(cur);
              }, 0) }}天
            </template>
          </el-table-column>
          <el-table-column
            v-else-if="obj.prop === 'operation'"
            v-bind="obj"
            :key="obj.prop">
            <template slot-scope="scope">
              <!-- 未激活 -->
              <template v-if="scope.row.state === 0">
                <a
                  class="mr10"
                  @click="editActive(scope.row, true)">
                  激活
                </a>
                <a
                  class="mr10"
                  @click="popupAdd(scope.row)">
                  编辑
                </a>
                <a @click="popupDel(scope.row)">删除</a>
              </template>
              <!-- 已激活 -->
              <template v-else>
                <a
                  class="mr10"
                  @click="editActive(scope.row, false)">
                  取消激活
                </a>
                <a
                  class="mr10"
                  @click="showCopy(scope.row)">
                  一键复制
                </a>
                <a
                  @click="showPreview(scope.row)">
                  预览
                </a>
              </template>
            </template>
          </el-table-column>
          <el-table-column
            v-else
            v-bind="obj"
            :key="obj.prop" />
        </template>
      </x-table>
    </div>

    <!-- 活动一键复制 -->
    <el-dialog
      title="一键复制"
      :visible.sync="copyVisible"
      width="550px"
      class="pop-add"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false">
      <el-form
        ref="copyForm"
        :rules="copyRules"
        :model="copyForm"
        label-position="right"
        label-width="140px">
        <!-- 超管才能选复制到不同工厂 -->
        <el-form-item
          v-if="user.user_group === 0"
          label="目标工厂："
          prop="copy_factory_id">
          <el-select
            v-model="copyForm.copy_factory_id"
            placeholder="请选择目标工厂">
            <el-option
              v-for="item in factoryList"
              :key="item.value"
              :label="item.label"
              :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item
          label="开始时间："
          prop="copy_start_time">
          <el-date-picker
            v-model="copyForm.copy_start_time"
            type="date"
            value-format="yyyy-MM-dd"
            style="width:300px"
            placeholder="请选择开始时间" />
        </el-form-item>
        <!-- 信息回填 -->
        <el-descriptions
          class="mt30 ml50 mr50"
          title="参考信息"
          :column="1"
          border>
          <el-descriptions-item label="活动名称">
            {{ copyForm.code_name }}（ID：{{ copyForm.period_id }}）
          </el-descriptions-item>
          <el-descriptions-item
            v-if="user.user_group === 0"
            label="源工厂">
            {{ factoryList.find(o => o.value == copyForm.factory_id)?.label }}
          </el-descriptions-item>
          <el-descriptions-item label="参与身份">
            {{ copyForm.user_type?.replace(/\[|\]/g, '') }}
          </el-descriptions-item>
          <el-descriptions-item label="活动时间">
            {{ copyForm.start_time }} ~ {{ copyForm.end_time }}（不含）
          </el-descriptions-item>
          <el-descriptions-item label="打卡天数">
            {{ copyForm.days }}天
          </el-descriptions-item>
          <el-descriptions-item label="简短描述">
            {{ copyForm.activity_desc }}
          </el-descriptions-item>
        </el-descriptions>
      </el-form>
      <span slot="footer">
        <el-button
          type="primary"
          size="small"
          :loading="copyLoading"
          @click="submitCopyForm">
          确定
        </el-button>
        <el-button
          size="small"
          :disabled="copyLoading"
          @click="copyVisible = false">
          取消
        </el-button>
      </span>
    </el-dialog>

    <!-- 新增/编辑 -->
    <el-dialog
      :title="addForm.id ? '编辑活动' : '新增活动'"
      :visible.sync="showPopAdd"
      width="550px"
      class="pop-add"
      :close-on-click-modal="false">
      <el-form
        ref="addForm"
        :rules="addRules"
        :model="addForm"
        size="medium"
        label-position="right"
        label-width="120px">
        <el-form-item
          v-if="addForm.id"
          label="活动ID：">
          {{ addForm.id }}
        </el-form-item>
        <el-form-item
          v-if="user.user_group === 0"
          label="所属工厂："
          prop="factory_id">
          <template v-if="addForm.id">{{ factoryList.find(o => o.value == addForm.factory_id).label }}</template>
          <el-select
            v-else
            v-model="addForm.factory_id"
            placeholder="请选择所属工厂">
            <el-option
              v-for="item in factoryList"
              :key="item.value"
              :label="item.label"
              :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item
          label="参与身份："
          prop="user_type">
          <el-select
            v-model="addForm.user_type"
            style="width:250px"
            multiple
            placeholder="请选择参与用户身份">
            <el-option
              v-for="v in userTypeMap"
              :key="v"
              :label="v"
              :value="v" />
          </el-select>
          <!-- <div
            class="c_c f12"
            style="margin:-5px 0 -10px">
            不同参与用户身份的活动可以同时进行
          </div> -->
        </el-form-item>
        <el-form-item
          label="活动名称："
          prop="code_name">
          <el-input
            v-model.trim="addForm.code_name"
            class="dib vam"
            placeholder="请输入活动名称"
            maxlength="15"
            show-word-limit />
          <div class="c_c f12 dib vam ml10">在小程序展示</div>
        </el-form-item>
        <el-form-item
          label="简短描述："
          prop="activity_desc">
          <el-input
            v-model.trim="addForm.activity_desc"
            class="dib"
            placeholder="请输入简短描述"
            maxlength="20"
            show-word-limit />
          <div class="c_c f12 dib vam ml10">作为副标题</div>
        </el-form-item>
        <el-form-item
          label="活动时间："
          prop="time_range">
          <el-date-picker
            v-model="addForm.time_range"
            class="dib vam"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            style="width:300px" />
        </el-form-item>
        <el-form-item
          label="阶段天数："
          prop="step_len">
          <el-tag
            v-for="(o, i) in (addForm.step_len || [])"
            :key="i"
            class="mr10"
            size="big"
            closable
            disable-transitions
            @close="delStep(i)">
            <div class="c_c mr10">
              <el-input-number
                v-model="o.value"
                style="width: 100px;"
                :min="1"
                :max="100"
                :step="1"
                step-strictly
                size="small" />
              天
            </div>
          </el-tag>
          <el-button @click="addStep">
            + 新增阶段
          </el-button>
          <div class="c_c">共持续 {{ days || 0 }} 天</div>
        </el-form-item>
        <el-form-item
          v-if="!addForm.id"
          label="活动状态："
          prop="state">
          <el-radio-group v-model="addForm.state">
            <el-radio-button :label="0">未激活</el-radio-button>
            <el-radio-button :label="1">激活</el-radio-button>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button
          type="primary"
          size="small"
          @click="submitAddForm">
          确定
        </el-button>
        <el-button
          size="small"
          @click="showPopAdd = false">
          取消
        </el-button>
      </span>
    </el-dialog>

    <!-- 预览活动 -->
    <el-dialog
      title="预览活动"
      :visible.sync="previewVisible"
      width="510px"
      class="pop-add"
      :close-on-click-modal="false"
      :close-on-press-escape="false">
      <el-form
        ref="previewForm"
        :rules="previewRules"
        :model="previewForm"
        label-position="right"
        label-width="140px">
        <el-form-item
          label="扫码预览："
          prop="qrcode">
          <span
            v-if="!previewQrcode"
            class="c_c">
            请先生成二维码
          </span>
          <img
            v-loading="previewLoading"
            class="db"
            :src="previewQrcode"
            style="width: 180px" />
        </el-form-item>
        <el-form-item
          label="模拟工号："
          prop="jobnum">
          <el-select
            v-model="previewForm.jobnum"
            placeholder="请选择所属工厂">
            <el-option
              v-for="item in previewJobnumList"
              :key="item"
              :label="item"
              :value="item" />
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button
          type="primary"
          :loading="previewLoading"
          @click="submitPreviewForm">
          生成二维码
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
/**
 * @description 打卡活动-活动配置
 */
import afcFilterBar from 'afc-filter-bar-vue2';
import { cloneDeep } from 'lodash';

import {
  searchField,
  tableCols,
} from './table-cols';

import xTable from '@/components/x-table';
import mixinFactoryList from '@/function/mixin-factory-list';
import tStr from '@/function/time-str';

export default {
  components: {
    afcFilterBar,
    xTable,
  },
  mixins: [mixinFactoryList],
  data() {
    return {
      searchForm: {},

      dataSet: {},
      showAllFactory: true, // 显示所有工厂的数据（不用强制筛选工厂）
      copyVisible: false, // 一键复制弹框
      copyForm: {},
      copyRules: {
        copy_factory_id: [
          {
            required: true,
            message: '请选择目标工厂'
          }
        ],
      },
      copyLoading: false, // 是否正在复制中
      
      previewVisible: false, // 预览弹框
      // 预览工号列表
      previewJobnumList: [
        'PreviewTest1',
        'PreviewTest2',
        'PreviewTest3',
        'PreviewTest4',
        'PreviewTest5',
      ],
      previewForm: {},
      previewRules: {
        jobnum: [
          {
            required: true,
            message: '请选择模拟工号'
          }
        ],
      },
      previewQrcode: null,
      previewLoading: false, // 是否正在生成二维码

      showPopAdd: false, // 添加用户的对话框的显示控制
      // 添加表单的数据
      addForm: {},
      // 添加的验证规则
      addRules: {
        user_type: [
          {
            required: true,
            message: '请选择参与用户身份'
          }
        ],
        code_name: [
          {
            required: true,
            message: '请输入活动名称'
          }
        ],
        time_range: [
          {
            required: true,
            message: '请选择活动时间'
          },
          {
            validator: (rule, value, callback) => {
              if (value?.length) {
                if (value[0] === value[1]) {
                  callback(new Error('不能选择同一天'));
                  this.timeValide = false;
                } else {
                  callback();
                  this.timeValide = true;
                  // const days = (new Date(value[1]).getTime() - new Date(value[0]).getTime()) / (24 * 3600 * 1000);
                  // if (days % 10 !== 0) {
                  //   callback(new Error('只能选择 10*N 天'));
                  //   this.timeValide = false;
                  // } else {
                  //   callback();
                  //   this.timeValide = true;
                  // }
                }
              } else {
                callback(new Error('请选择活动时间'));
                this.timeValide = false;
              }
            },
            trigger: 'change',
          },
        ],
        step_len: [
          {
            required: true,
            // validator: (rule, value, callback) => {
            //   if (this.addForm.days) {
            //     let stepDays = 0;
            //     value?.forEach(o => {
            //       stepDays += parseInt(o.value);
            //     });
            //     if (stepDays !== this.addForm.days) {
            //       callback(new Error('阶段总天数需和持续天数一致'));
            //     } else {
            //       callback();
            //     }
            //   } else {
            //     callback();
            //   }
            // },
            trigger: 'change',
          },
        ],
        factory_id: [
          {
            required: true,
            message: '请选择所属工厂'
          }
        ],
      },
      timeValide: false, // 活动时间是否校验通过
    };
  },
  computed: {
    tableCols() {
      const arr = cloneDeep(tableCols);
      if (this.user.user_group) {
        // 工厂管理员，不能筛选工厂
        arr.splice(1, 1);
      }
      return arr;
    },
    searchField() {
      const arr = cloneDeep(searchField);
      if (this.user.user_group) {
        // 工厂管理员，不能筛选工厂
        arr.splice(1, 1);
      } else {
        arr[1].formOpts.optsList = this.factoryList;
      }
      arr[arr.length - 1].formOpts.events.click = () => {
        this.popupAdd();
      };
      return arr;
    },
    // 活动持续的天数
    days() {
      let stepDays = 0;
      this.addForm.step_len?.forEach(o => {
        stepDays += parseInt(o.value);
      });
      return stepDays;
    },
    // showAddStep() {
    //   if (!this.addForm.days) {
    //     return false;
    //   }

    //   let stepDays = 0;
    //   this.addForm.step_len?.forEach(o => {
    //     stepDays += parseInt(o.value);
    //   });
    //   if (stepDays >= this.addForm.days) {
    //     return false;
    //   } else {
    //     return true;
    //   }
    // },
  },
  methods: {
    init() {
      this.getList();
    },

    // 获取列表数据
    async getList() {
      let search = cloneDeep(this.searchForm);

      const { code, data } = await ajax({
        url: 'checkin/period',
        data: { ...search },
      });
      if (code) {
        this.loadFailed = true;
        return;
      }
      this.loadFailed = false;

      this.dataSet = data;
    },

    // 添加/编辑用户
    popupAdd(o = { state: 0 }) {
      let obj = cloneDeep(o);

      if (obj.id) {
        obj.time_range = [obj.start_time, obj.end_time];
        obj.step_len = obj.step_len.split(',').map(value => ({ value }));
        obj.user_type = obj.user_type.split(',').map(value => value.replace(/\[|\]/g, ''));
      }

      this.addForm = obj;
      this.showPopAdd = true;
      this.timeValide = true;
      this.$nextTick(() => {
        this.$refs.addForm.clearValidate();
      });
    },
    submitAddForm() {
      this.$refs.addForm.validate().then(async validate => {
        if (!validate) return;

        const data = cloneDeep(this.addForm);
        data.start_time = data.time_range[0];
        data.end_time = data.time_range[1];
        data.step_len = data.step_len.map(o => o.value).join();
        data.user_type = data.user_type.map(v => `[${v}]`).join();
        data.days = this.days;
        delete data.time_range;

        const { code, error } = await ajax({
          url: 'checkin/period',
          method: this.addForm.id ? 'put' : 'post',
          data,
        });
        if (code || error) return;

        this.showPopAdd = false;
        this.getList();
        this.$message.success('操作成功');
      });
    },

    // 删除确认
    popupDel(row) {
      this.$confirm(`<div class="mb20">确定删除 ${row.code_name} 吗？
        <p class="poa">删除后将无法恢复。</p>
      </div>`, '删除提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true,
      }).then(async () => {
        const { code, error } = await ajax({
          url: 'checkin/period',
          method: 'delete',
          data: {
            id: row.id,
            factory_id: row.factory_id,
          },
        });
        if (code || error) return;

        this.getList();
        this.$message.success('操作成功');
      })
        .catch(() => {});
    },

    // 激活/取消激活
    editActive(row, type) {
      const cancelText = !type ? '取消' : '';
      this.$confirm(`确定${cancelText}激活 ${row.code_name} 吗？`, cancelText + '激活提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(async () => {
        const { code, error } = await ajax({
          url: 'checkin/period',
          method: 'put',
          data: {
            type: 'active',
            state: type ? 1 : 0,
            id: row.id,
            factory_id: row.factory_id,
          },
        });
        if (code || error) return;

        this.getList();
        this.$message.success('操作成功');
      })
        .catch(() => {});
    },

    addStep() {
      // let stepDays = 0;
      // this.addForm.step_len?.forEach(o => {
      //   stepDays += parseInt(o.value);
      // });
      // if (stepDays >= this.addForm.days) return;

      if (!this.addForm.step_len) {
        this.$set(this.addForm, 'step_len', []);
      }
      this.$set(this.addForm, 'step_len', [...this.addForm.step_len, { value: 1 }]);
      this.$nextTick(() => {
        this.$refs.addForm.validateField('step_len');
      });
    },
    delStep(idx) {
      let arr = cloneDeep(this.addForm.step_len);
      arr.splice(idx, 1);
      this.$set(this.addForm, 'step_len', arr);
      this.$nextTick(() => {
        this.$refs.addForm.validateField('step_len');
      });
    },

    // 弹出一键复制
    showCopy(row) {
      // this.copyForm = {
      //   period_id: row.id,
      //   code_name: row.code_name,
      // };
      const obj = cloneDeep(row);
      obj.period_id = row.id;
      this.copyForm = cloneDeep(obj);
      this.copyVisible = true;
    },
    submitCopyForm() {
      this.$refs.copyForm.validate().then(async validate => {
        if (!validate) return;

        this.copyLoading = true;
        const { code, error } = await ajax({
          url: 'checkin/period-copy',
          method: 'post',
          data: {
            period_id: this.copyForm.period_id,
            factory_id: this.copyForm.copy_factory_id,
            start_time: this.copyForm.copy_start_time,
          },
        });
        this.copyLoading = false;
        if (code || error) return;

        this.copyVisible = false;
        this.getList();
        this.$message.success('操作成功');
      });
    },

    // 弹出预览
    showPreview(row) {
      const obj = cloneDeep(row);
      this.previewForm = cloneDeep(obj);
      this.previewQrcode = null;
      this.$refs.previewForm?.clearValidate();
      this.previewVisible = true;
    },
    submitPreviewForm() {
      this.$refs.previewForm.validate().then(async validate => {
        if (!validate) return;

        this.previewLoading = true;
        this.previewQrcode = `${process.env.VUE_APP_apiHost}cms/wx-qrcode?factory_id=${this.user.factory_id}&path=checkinPreview&period_id=${this.previewForm.id}&jobnum=${this.previewForm.jobnum}&code_name=${this.previewForm.code_name}&activity_desc=${this.previewForm.activity_desc}`;
        setTimeout(() => {
          this.previewLoading = false;
        }, 1200);
      });
    },
  },
};
</script>

<style lang="scss">
  @import './index';
</style>

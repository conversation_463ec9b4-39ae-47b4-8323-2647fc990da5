<!--
 * <AUTHOR>
 * @LastEditTime: 2023-12-06 23:35:59
 * @description 打卡活动 - 内容参与情况
-->
<template>
  <div class="checkin-data-involvement ui-layout-col">
    <afc-filter-bar
      class="mb10"
      size="small"
      :field="searchField"
      :init-filter="searchForm"
      @valueChange="searchForm = $event"
      @onFilter="handelFilter" />

    <div class="flex1">
      <x-table
        :data-set="dataSet"
        :load-list-failed="loadFailed"
        @reload="init"
        @sort-change="sortChange">
        <template v-for="obj in tableCols">
          <el-table-column
            v-bind="obj"
            :key="obj.prop" />
        </template>
      </x-table>
    </div>
  </div>
</template>

<script>
import afcFilterBar from 'afc-filter-bar-vue2';
import { cloneDeep } from 'lodash';

import {
  searchField,
  tableCols,
} from './table-cols';

import xTable from '@/components/x-table';
import mixinFactoryList from '@/function/mixin-factory-list';

export default {
  components: {
    afcFilterBar,
    xTable,
  },
  mixins: [mixinFactoryList],
  data() {
    return {
      dataSet: {},
      searchForm: {},
      periodList: [], // 活动期数列表
      taskList: [], // 任务列表
    };
  },
  computed: {
    tableCols() {
      const arr = cloneDeep(tableCols);
      // if (this.user.user_group) {
      //   // 工厂管理员，不能筛选工厂
      //   arr.splice(1, 1);
      // }
      return arr;
    },
    searchField() {
      const arr = cloneDeep(searchField);

      arr[1].formOpts.optsList = this.periodList.map(o => {
        return {
          ...o,
          label: `${o.label}[${o.value}]`,
        };
      });
      arr[2].formOpts.optsList = this.taskList;

      if (this.user.user_group) {
        // 工厂管理员，不能筛选工厂
        arr.splice(0, 1);
      } else {
        arr[0].formOpts.optsList = this.factoryList;
        arr[0].formOpts.events = {
          change: e => {
            this.onFactoryChange(e);
          },
        };
      }

      arr[arr.length - 2].formOpts.events.click = () => {
        this.exportLogExcel();
      };
      arr[arr.length - 1].formOpts.events.click = () => {
        this.exportExcel();
      };
      return arr;
    },
  },
  created() {
    // 工厂管理员，手动初始化
    if (this.user.user_group) {
      this.init();
    }
  },
  methods: {
    async init() {
      await this.getPeriodList();
      this.getList();
      this.getTask();
    },
    handelFilter() {
      this.dataSet.page_num = 1;
      this.getList();
    },
    onFactoryChange(factory_id) {
      this.periodList = [];
      this.searchForm.task_id = '';
      this.getPeriodList(factory_id);
      this.getTask(factory_id);
    },

    // 获取期数列表
    async getPeriodList(factory_id) {
      const { code, data } = await ajax({
        url: 'checkin/period',
        data: {
          state: 2,
          factory_id: factory_id || this.searchForm.factory_id,
        },
      });
      if (code) {
        this.loadFailed = true;
        return;
      }
      this.loadFailed = false;

      this.searchForm.period_id = data.list[0]?.id;
      this.periodList = data.list.map(o => {
        return {
          label: o.code_name,
          value: o.id,
        };
      });
    },

    // 获取任务列表
    async getTask(factory_id) {
      const { code, data } = await ajax({
        url: 'checkin/task',
        data: {
          page_size: 999,
          factory_id: factory_id || this.searchForm.factory_id,
        },
      });
      if (code) return;

      this.taskList = data.list?.map(o => {
        return {
          label: o.title,
          value: o.id,
        };
      }) || [];
    },

    // 获取列表数据
    async getList() {
      if (!this.periodList.length) return;

      const search = cloneDeep(this.searchForm);
      delete search.export;

      const { code, data } = await ajax({
        url: 'checkin-data/involvement',
        data: {
          ...search,
          ...this.sortObj,
        },
      });
      if (code) {
        this.loadFailed = true;
        return;
      }
      this.loadFailed = false;

      this.dataSet = data;
    },
    sizeChange(num) {
      this.$set(this.dataSet, 'page_size', num);
      this.dataSet.page_num = 1;
      this.getList();
    },
    currentChange(num) {
      this.$set(this.dataSet, 'page_num', num);
      this.getList();
    },
    sortChange({ prop, order }) {
      if (order) {
        this.sortObj = {
          field: prop,
          direction: order,
        };
      } else {
        this.sortObj = {};
      }
      this.getList();
    },

    // 导出明细
    exportLogExcel() {
      const search = cloneDeep(this.searchForm);
      delete search.export;
      delete search['export-log'];
      
      this.download({
        url: '/export/checkin/logs',
        data: {
          ...search,
          period_name: this.periodList.find(o => o.value === this.searchForm.period_id).label,
          factory_name: this.user.user_group ? this.user.factory_name : this.factoryList.find(o => o.value === this.searchForm.factory_id).label
        },
      });
    },
    // 导出
    exportExcel() {
      const search = cloneDeep(this.searchForm);
      delete search.export;
      delete search['export-log'];
      
      this.download({
        url: '/export/checkin/involvement',
        data: {
          ...search,
          period_name: this.periodList.find(o => o.value === this.searchForm.period_id).label,
          factory_name: this.user.user_group ? this.user.factory_name : this.factoryList.find(o => o.value === this.searchForm.factory_id).label
        },
      });
    },
  },
};
</script>

<style lang="scss">
  @import './index';
</style>

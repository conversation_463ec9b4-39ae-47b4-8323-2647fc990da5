import { Controller, Get, Query } from '@nestjs/common';

import { StatisticsQueryDto, LogsQueryDto } from '../dto/Service';
import { ServiceService } from '../services/Service';

@Controller('service')
export class ServiceController {
  constructor(private readonly service: ServiceService) {}

  /** 服务记录-统计汇总 */
  @Get('statistics')
  async getStatistics(@Query() query: StatisticsQueryDto): Promise<ResObj> {
    return this.service.getStatistics(query);
  }

  /** 服务记录-记录明细 */
  @Get('logs')
  async getLogs(@Query() query: LogsQueryDto): Promise<ResObj> {
    return this.service.getLogs(query);
  }
}

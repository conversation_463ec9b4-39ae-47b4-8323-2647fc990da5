import { EntityManager } from 'typeorm';
import { CustomHeadersDto } from '../dto';
import { CheckinTestSubmitDto, ActivityDetailQueryDto, UserInfoQueryDto, CheckinLogInfoDto, CheckinSubmitDto, CheckinDaysInfoDto } from '../dto/Checkin';
export declare class CheckinService {
    private readonly entityManager;
    constructor(entityManager: EntityManager);
    activityInfo(headers: CustomHeadersDto): Promise<ResObj>;
    activityDetail(query: ActivityDetailQueryDto, headers: CustomHeadersDto): Promise<ResObj>;
    simpleDetail(query: ActivityDetailQueryDto): Promise<ResObj>;
    testSubmit(body: CheckinTestSubmitDto, headers: CustomHeadersDto): Promise<ResObj>;
    userInfo(query: UserInfoQueryDto, headers: CustomHeadersDto): Promise<ResObj>;
    logInfo(query: CheckinLogInfoDto, headers: CustomHeadersDto): Promise<ResObj>;
    submit(body: CheckinSubmitDto, headers: CustomHeadersDto): Promise<ResObj>;
    daysInfo(query: CheckinDaysInfoDto, headers: CustomHeadersDto): Promise<ResObj>;
}

import { Controller, Get, Post, Body, Headers } from '@nestjs/common';

import { CustomHeadersDto } from '../dto';
import { CheckinTestSubmitDto } from '../dto/Checkin';
import { CheckinService } from '../services/Checkin';
import { validateDto } from '../filters/validate-dto';

@Controller('checkin')
export class CheckinController {
  constructor(private readonly service: CheckinService) {}

  /** 打卡活动-活动信息 */
  @Get('activity-info')
  async activityInfo(@Headers() headers: CustomHeadersDto): Promise<ResObj> {
    const headerDto = await validateDto(CustomHeadersDto, headers);
    return this.service.activityInfo(headerDto);
  }

  /** 打卡活动-提交测试问卷 */
  @Post('test-submit')
  async testSubmit(
    @Body() body: CheckinTestSubmitDto,
    @Headers() headers: CustomHeadersDto,
  ): Promise<ResObj> {
    const headerDto = await validateDto(CustomHeadersDto, headers);
    return this.service.testSubmit(body, headerDto);
  }
}

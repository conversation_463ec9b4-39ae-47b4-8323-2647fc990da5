import { Controller, Get, Post, Body, Headers, Query } from '@nestjs/common';

import { CustomHeadersDto } from '../dto';
import { CheckinTestSubmitDto, ActivityDetailQueryDto, UserInfoQueryDto, CheckinLogInfoDto, CheckinSubmitDto } from '../dto/Checkin';
import { CheckinService } from '../services/Checkin';
import { validateDto } from '../filters/validate-dto';

@Controller('checkin')
export class CheckinController {
  constructor(private readonly service: CheckinService) {}

  /** 打卡活动-活动信息列表 */
  @Get('activity-info')
  async activityInfo(@Headers() headers: CustomHeadersDto): Promise<ResObj> {
    const headerDto = await validateDto(CustomHeadersDto, headers);
    return this.service.activityInfo(headerDto);
  }

  /** 打卡活动-活动详情 */
  @Get('activity-detail')
  async activityDetail(@Query() query: ActivityDetailQueryDto, @Headers() headers: CustomHeadersDto): Promise<ResObj> {
    const headerDto = await validateDto(CustomHeadersDto, headers);
    return this.service.activityDetail(query, headerDto);
  }

  /** 打卡活动-简单详情 */
  @Get('simple-detail')
  async simpleDetail(@Query() query: ActivityDetailQueryDto): Promise<ResObj> {
    return this.service.simpleDetail(query);
  }

  /** 打卡活动-提交测试问卷 */
  @Post('test-submit')
  async testSubmit(
    @Body() body: CheckinTestSubmitDto,
    @Headers() headers: CustomHeadersDto,
  ): Promise<ResObj> {
    const headerDto = await validateDto(CustomHeadersDto, headers);
    return this.service.testSubmit(body, headerDto);
  }

  /** 打卡活动-用户活动信息 */
  @Get('userinfo')
  async userInfo(
    @Query() query: UserInfoQueryDto,
    @Headers() headers: CustomHeadersDto,
  ): Promise<ResObj> {
    const headerDto = await validateDto(CustomHeadersDto, headers);
    return this.service.userInfo(query, headerDto);
  }

  /** 打卡活动-打卡记录 */
  @Get('log-info')
  async logInfo(
    @Query() query: CheckinLogInfoDto,
    @Headers() headers: CustomHeadersDto,
  ): Promise<ResObj> {
    const headerDto = await validateDto(CustomHeadersDto, headers);
    return this.service.logInfo(query, headerDto);
  }

  /** 打卡活动-提交打卡 */
  @Post('submit')
  async submit(
    @Body() body: CheckinSubmitDto,
    @Headers() headers: CustomHeadersDto,
  ): Promise<ResObj> {
    const headerDto = await validateDto(CustomHeadersDto, headers);
    return this.service.submit(body, headerDto);
  }
}
